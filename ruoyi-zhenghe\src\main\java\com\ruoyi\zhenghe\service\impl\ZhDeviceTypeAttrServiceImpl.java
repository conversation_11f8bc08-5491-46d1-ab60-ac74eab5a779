package com.ruoyi.zhenghe.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.zhenghe.domain.ZhDeviceTypeAttr;
import com.ruoyi.zhenghe.mapper.ZhDeviceTypeAttrMapper;
import com.ruoyi.zhenghe.service.IZhDeviceTypeAttrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备类型属性/物模型属性Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@Service
public class ZhDeviceTypeAttrServiceImpl implements IZhDeviceTypeAttrService 
{
    @Autowired
    private ZhDeviceTypeAttrMapper zhDeviceTypeAttrMapper;

    /**
     * 查询设备类型属性/物模型属性
     * 
     * @param id 设备类型属性/物模型属性主键
     * @return 设备类型属性/物模型属性
     */
    @Override
    public ZhDeviceTypeAttr selectZhDeviceTypeAttrById(Long id)
    {
        return zhDeviceTypeAttrMapper.selectZhDeviceTypeAttrById(id);
    }

    /**
     * 查询设备类型属性/物模型属性列表
     * 
     * @param zhDeviceTypeAttr 设备类型属性/物模型属性
     * @return 设备类型属性/物模型属性
     */
    @Override
    public List<ZhDeviceTypeAttr> selectZhDeviceTypeAttrList(ZhDeviceTypeAttr zhDeviceTypeAttr)
    {
        return zhDeviceTypeAttrMapper.selectZhDeviceTypeAttrList(zhDeviceTypeAttr);
    }

    /**
     * 新增设备类型属性/物模型属性
     * 
     * @param zhDeviceTypeAttr 设备类型属性/物模型属性
     * @return 结果
     */
    @Override
    public int insertZhDeviceTypeAttr(ZhDeviceTypeAttr zhDeviceTypeAttr)
    {
        zhDeviceTypeAttr.setCreateTime(DateUtils.getNowDate());
        return zhDeviceTypeAttrMapper.insertZhDeviceTypeAttr(zhDeviceTypeAttr);
    }

    /**
     * 修改设备类型属性/物模型属性
     * 
     * @param zhDeviceTypeAttr 设备类型属性/物模型属性
     * @return 结果
     */
    @Override
    public int updateZhDeviceTypeAttr(ZhDeviceTypeAttr zhDeviceTypeAttr)
    {
        zhDeviceTypeAttr.setUpdateTime(DateUtils.getNowDate());
        return zhDeviceTypeAttrMapper.updateZhDeviceTypeAttr(zhDeviceTypeAttr);
    }

    /**
     * 批量删除设备类型属性/物模型属性
     * 
     * @param ids 需要删除的设备类型属性/物模型属性主键
     * @return 结果
     */
    @Override
    public int deleteZhDeviceTypeAttrByIds(Long[] ids)
    {
        return zhDeviceTypeAttrMapper.deleteZhDeviceTypeAttrByIds(ids);
    }

    /**
     * 删除设备类型属性/物模型属性信息
     * 
     * @param id 设备类型属性/物模型属性主键
     * @return 结果
     */
    @Override
    public int deleteZhDeviceTypeAttrById(Long id)
    {
        return zhDeviceTypeAttrMapper.deleteZhDeviceTypeAttrById(id);
    }

    /**
     * 根据设备编号查询设备类型属性列表
     *
     * @param equipmentCode 设备编号
     * @return 设备类型属性列表
     */
    @Override
    public List<ZhDeviceTypeAttr> selectZhDeviceTypeAttrListByequipmentCode(String equipmentCode) {
        return zhDeviceTypeAttrMapper.selectZhDeviceTypeAttrListByequipmentCode(equipmentCode);
    }
}
