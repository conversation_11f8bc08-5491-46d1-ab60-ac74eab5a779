package com.ruoyi.zhenghe.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.zhenghe.domain.ZhDeviceProductionRecord;
import com.ruoyi.zhenghe.domain.dto.ProductQuantityDto;
import com.ruoyi.zhenghe.service.IZhDeviceProductionRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备产品生产记录Controller
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Api(tags = "设备产品生产记录管理")
@RestController
@RequestMapping("/zhenghe/deviceProductionRecord")
public class ZhDeviceProductionRecordController extends BaseController
{
    @Autowired
    private IZhDeviceProductionRecordService zhDeviceProductionRecordService;

    /**
     * 查询设备产品生产记录列表
     */
    @ApiOperation("查询设备产品生产记录列表")
    //@PreAuthorize("@ss.hasPermi('zhenghe:deviceProductionRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(ZhDeviceProductionRecord zhDeviceProductionRecord)
    {
        startPage();
        List<ZhDeviceProductionRecord> list = zhDeviceProductionRecordService.selectZhDeviceProductionRecordList(zhDeviceProductionRecord);
        return getDataTable(list);
    }

    /**
     * 查询设备生产汇总数据
     */
    @ApiOperation("查询设备生产汇总数据")
    //@PreAuthorize("@ss.hasPermi('zhenghe:deviceProductionRecord:list')")
    @GetMapping("/summary")
    public TableDataInfo summary(@RequestParam String deviceCode, 
                                @RequestParam(required = false) String beginTime,
                                @RequestParam(required = false) String endTime)
    {
        startPage();
        List<ZhDeviceProductionRecord> list = zhDeviceProductionRecordService.selectProductionSummary(deviceCode, beginTime, endTime);
        return getDataTable(list);
    }

    /**
     * 导出设备产品生产记录列表
     */
    @ApiOperation("导出设备产品生产记录列表")
    //@PreAuthorize("@ss.hasPermi('zhenghe:deviceProductionRecord:export')")
    @Log(title = "设备产品生产记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhDeviceProductionRecord zhDeviceProductionRecord)
    {
        List<ZhDeviceProductionRecord> list = zhDeviceProductionRecordService.selectZhDeviceProductionRecordList(zhDeviceProductionRecord);
        ExcelUtil<ZhDeviceProductionRecord> util = new ExcelUtil<ZhDeviceProductionRecord>(ZhDeviceProductionRecord.class);
        util.exportExcel(response, list, "设备产品生产记录数据");
    }

    /**
     * 获取设备产品生产记录详细信息
     */
    @ApiOperation("获取设备产品生产记录详细信息")
    //@PreAuthorize("@ss.hasPermi('zhenghe:deviceProductionRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(zhDeviceProductionRecordService.selectZhDeviceProductionRecordById(id));
    }

    /**
     * 新增设备产品生产记录
     */
    @ApiOperation("新增设备产品生产记录")
    //@PreAuthorize("@ss.hasPermi('zhenghe:deviceProductionRecord:add')")
    @Log(title = "设备产品生产记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZhDeviceProductionRecord zhDeviceProductionRecord)
    {
        return toAjax(zhDeviceProductionRecordService.insertZhDeviceProductionRecord(zhDeviceProductionRecord));
    }

    /**
     * 修改设备产品生产记录
     */
    @ApiOperation("修改设备产品生产记录")
    //@PreAuthorize("@ss.hasPermi('zhenghe:deviceProductionRecord:edit')")
    @Log(title = "设备产品生产记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhDeviceProductionRecord zhDeviceProductionRecord)
    {
        return toAjax(zhDeviceProductionRecordService.updateZhDeviceProductionRecord(zhDeviceProductionRecord));
    }

    /**
     * 删除设备产品生产记录
     */
    @ApiOperation("删除设备产品生产记录")
    //@PreAuthorize("@ss.hasPermi('zhenghe:deviceProductionRecord:remove')")
    @Log(title = "设备产品生产记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(zhDeviceProductionRecordService.deleteZhDeviceProductionRecordByIds(ids));
    }

    /**
     * 查询产品数量展示界面数据
     */
    @ApiOperation("查询产品数量展示界面数据")
    //@PreAuthorize("@ss.hasPermi('zhenghe:deviceProductionRecord:list')")
    @GetMapping("/productQuantity")
    public TableDataInfo getProductQuantityList(@RequestParam(required = false) String productName,
                                               @RequestParam(required = false) String beginTime,
                                               @RequestParam(required = false) String endTime)
    {
        // 由于使用了混合查询（MySQL + IoTDB），需要手动处理分页
        // 先获取所有数据，然后手动分页
        List<ZhDeviceProductionRecord> allList = zhDeviceProductionRecordService.selectProductQuantityList(productName, beginTime, endTime);

        // 手动分页处理
        return createManualPageResult(allList);
    }

    /**
     * 手动分页处理（用于混合查询场景）
     *
     * @param allList 所有数据列表
     * @return 分页结果
     */
    private TableDataInfo createManualPageResult(List<ZhDeviceProductionRecord> allList) {
        if (allList == null || allList.isEmpty()) {
            TableDataInfo emptyResult = new TableDataInfo();
            emptyResult.setCode(200);
            emptyResult.setMsg("查询成功");
            emptyResult.setTotal(0);
            emptyResult.setRows(new ArrayList<>());
            return emptyResult;
        }

        // 获取分页参数
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();

        // 计算总数
        int total = allList.size();

        // 计算分页参数
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);

        // 获取当前页数据
        List<ZhDeviceProductionRecord> pageData;
        if (startIndex >= total) {
            pageData = new ArrayList<>();
        } else {
            pageData = allList.subList(startIndex, endIndex);
        }

        // 构建分页结果
        TableDataInfo result = new TableDataInfo();
        result.setCode(200);
        result.setMsg("查询成功");
        result.setTotal(total);
        result.setRows(pageData);

        return result;
    }

    /**
     * 测试接口：验证productionCount数据流
     */
    @GetMapping("/testProductionCount")
    public AjaxResult testProductionCount() {
        // 创建一个测试对象
        ZhDeviceProductionRecord testRecord = new ZhDeviceProductionRecord();
        testRecord.setProductName("测试产品");
        testRecord.setDeviceCode("测试设备");
        testRecord.setProductionCount(888);

        // 验证getter/setter
        Integer retrievedCount = testRecord.getProductionCount();

        return AjaxResult.success("测试结果")
                .put("设置的值", 888)
                .put("读取的值", retrievedCount)
                .put("是否相等", retrievedCount != null && retrievedCount.equals(888));
    }

    /**
     * 导出产品数量数据
     */
    @ApiOperation("导出产品数量数据")
    //@PreAuthorize("@ss.hasPermi('zhenghe:deviceProductionRecord:export')")
    @Log(title = "产品数量数据", businessType = BusinessType.EXPORT)
    @PostMapping("/productQuantity/export")
    public void exportProductQuantity(HttpServletResponse response,
                                     @RequestParam(required = false) String productName,
                                     @RequestParam(required = false) String beginTime,
                                     @RequestParam(required = false) String endTime)
    {
        List<ProductQuantityDto> list = zhDeviceProductionRecordService.selectProductQuantityForExport(productName, beginTime, endTime);
        ExcelUtil<ProductQuantityDto> util = new ExcelUtil<ProductQuantityDto>(ProductQuantityDto.class);
        util.exportExcel(response, list, "产品数量数据");
    }

    /**
     * 查看产品生产明细
     */
    @ApiOperation("查看产品生产明细")
    //@PreAuthorize("@ss.hasPermi('zhenghe:deviceProductionRecord:list')")
    @GetMapping("/productDetail")
    public TableDataInfo getProductDetail(@RequestParam String productName,
                                         @RequestParam(required = false) String beginTime,
                                         @RequestParam(required = false) String endTime)
    {
        // 参数验证
        if (productName == null || productName.trim().isEmpty()) {
            return getDataTable(new java.util.ArrayList<>());
        }

        startPage();
        List<ZhDeviceProductionRecord> list = zhDeviceProductionRecordService.selectProductDetail(productName.trim(), beginTime, endTime);
        return getDataTable(list);
    }
}
