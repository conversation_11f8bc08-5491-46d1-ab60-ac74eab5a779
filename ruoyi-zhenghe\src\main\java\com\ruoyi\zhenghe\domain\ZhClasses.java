package com.ruoyi.zhenghe.domain;

import java.time.LocalTime;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 班次对象 zh_classes
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
@ApiModel(value = "ZhClasses",description = "班次对象")
public class ZhClasses extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 班次名称 */
    @ApiModelProperty(value = "班次名称")
    @Excel(name = "班次名称")
    private String classesName;

    /** 班次开始时间 */
    @ApiModelProperty(value = "班次开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "班次开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private String startTime;

    /** 班次结束时间 */
    @ApiModelProperty(value = "班次结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "班次结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private String endTime;

    /** 部门id */
    @Excel(name = "部门id")
    private Long deptId;

    /** 班次休息时间 */
    @ApiModelProperty(value = "班次休息时间")
    @Excel(name = "班次休息时间")
    private Integer restPeriod;

    public Integer getRestPeriod() {
        return restPeriod;
    }

    public void setRestPeriod(Integer restPeriod) {
        this.restPeriod = restPeriod;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setClassesName(String classesName) 
    {
        this.classesName = classesName;
    }

    public String getClassesName() 
    {
        return classesName;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("classesName", getClassesName())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("deptId", getDeptId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
