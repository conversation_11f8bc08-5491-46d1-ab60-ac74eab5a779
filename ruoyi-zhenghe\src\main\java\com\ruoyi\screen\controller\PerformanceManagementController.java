package com.ruoyi.screen.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.screen.service.AsyncComputeService;
import com.ruoyi.screen.service.DataPrecomputeService;
import com.ruoyi.screen.service.FallbackService;
import com.ruoyi.screen.service.MultiLevelCacheService;
import com.ruoyi.screen.util.PerformanceMonitor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 性能管理控制器
 * 提供性能监控、缓存管理、预计算控制等功能
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
@Slf4j
@Api(tags = "性能管理")
@RestController
@RequestMapping("/screen/performance")
public class PerformanceManagementController {

    @Autowired
    private PerformanceMonitor performanceMonitor;

    @Autowired
    private MultiLevelCacheService multiLevelCacheService;

    @Autowired
    private AsyncComputeService asyncComputeService;

    @Autowired
    private DataPrecomputeService dataPrecomputeService;

    @Autowired
    private FallbackService fallbackService;

    /**
     * 获取性能统计信息
     */
    @ApiOperation(value = "获取性能统计信息")
    @GetMapping("/stats")
    public AjaxResult getPerformanceStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 接口性能统计
            Map<String, PerformanceMonitor.InterfaceStats> interfaceStats = performanceMonitor.getAllStats();
            stats.put("interfaceStats", interfaceStats);
            
            // 缓存统计
            JSONObject cacheStats = multiLevelCacheService.getCacheStats();
            stats.put("cacheStats", cacheStats);
            
            // 异步计算任务统计
            Map<String, Object> asyncStats = new HashMap<>();
            asyncStats.put("computingTaskCount", asyncComputeService.getComputingTaskCount());
            asyncStats.put("computingTasks", asyncComputeService.getComputingTasks());
            stats.put("asyncStats", asyncStats);
            
            // 熔断器状态
            Map<String, Object> circuitBreakerStatus = fallbackService.getCircuitBreakerStatus();
            stats.put("circuitBreakerStatus", circuitBreakerStatus);
            
            return AjaxResult.success(stats);
        } catch (Exception e) {
            log.error("获取性能统计信息失败", e);
            return AjaxResult.error("获取性能统计信息失败");
        }
    }

    /**
     * 清空性能统计
     */
    @ApiOperation(value = "清空性能统计")
    @PostMapping("/stats/clear")
    public AjaxResult clearPerformanceStats() {
        try {
            performanceMonitor.clearStats();
            return AjaxResult.success("性能统计已清空");
        } catch (Exception e) {
            log.error("清空性能统计失败", e);
            return AjaxResult.error("清空性能统计失败");
        }
    }

    /**
     * 手动触发预计算
     */
    @ApiOperation(value = "手动触发预计算")
    @PostMapping("/precompute/trigger")
    public AjaxResult triggerPrecompute(@RequestParam String deptId, 
                                       @RequestParam(required = false) String timeType) {
        try {
            if (timeType == null) {
                timeType = "today";
            }
            
            dataPrecomputeService.triggerPrecompute(deptId, timeType);
            return AjaxResult.success("预计算任务已触发");
        } catch (Exception e) {
            log.error("触发预计算失败", e);
            return AjaxResult.error("触发预计算失败");
        }
    }

    /**
     * 手动触发当前数据预计算
     */
    @ApiOperation(value = "手动触发当前数据预计算")
    @PostMapping("/precompute/current")
    public AjaxResult triggerCurrentDataPrecompute() {
        try {
            dataPrecomputeService.precomputeCurrentData();
            return AjaxResult.success("当前数据预计算已触发");
        } catch (Exception e) {
            log.error("触发当前数据预计算失败", e);
            return AjaxResult.error("触发当前数据预计算失败");
        }
    }

    /**
     * 手动触发历史数据预计算
     */
    @ApiOperation(value = "手动触发历史数据预计算")
    @PostMapping("/precompute/historical")
    public AjaxResult triggerHistoricalDataPrecompute() {
        try {
            dataPrecomputeService.precomputeHistoricalData();
            return AjaxResult.success("历史数据预计算已触发");
        } catch (Exception e) {
            log.error("触发历史数据预计算失败", e);
            return AjaxResult.error("触发历史数据预计算失败");
        }
    }

    /**
     * 获取接口响应时间排行
     */
    @ApiOperation(value = "获取接口响应时间排行")
    @GetMapping("/stats/ranking")
    public AjaxResult getResponseTimeRanking() {
        try {
            Map<String, PerformanceMonitor.InterfaceStats> allStats = performanceMonitor.getAllStats();
            
            // 按平均响应时间排序
            Map<String, Object> ranking = new HashMap<>();
            allStats.entrySet().stream()
                .sorted((e1, e2) -> Long.compare(e2.getValue().getAverageDuration(), e1.getValue().getAverageDuration()))
                .forEach(entry -> {
                    Map<String, Object> stat = new HashMap<>();
                    stat.put("averageDuration", entry.getValue().getAverageDuration());
                    stat.put("maxDuration", entry.getValue().getMaxDuration());
                    stat.put("totalCalls", entry.getValue().getTotalCalls());
                    stat.put("slowCallRate", entry.getValue().getSlowCallRate());
                    ranking.put(entry.getKey(), stat);
                });
            
            return AjaxResult.success(ranking);
        } catch (Exception e) {
            log.error("获取响应时间排行失败", e);
            return AjaxResult.error("获取响应时间排行失败");
        }
    }

    /**
     * 获取慢接口列表
     */
    @ApiOperation(value = "获取慢接口列表")
    @GetMapping("/stats/slow")
    public AjaxResult getSlowInterfaces(@RequestParam(defaultValue = "30000") long threshold) {
        try {
            Map<String, PerformanceMonitor.InterfaceStats> allStats = performanceMonitor.getAllStats();
            
            Map<String, Object> slowInterfaces = new HashMap<>();
            allStats.entrySet().stream()
                .filter(entry -> entry.getValue().getAverageDuration() > threshold)
                .forEach(entry -> {
                    Map<String, Object> stat = new HashMap<>();
                    stat.put("averageDuration", entry.getValue().getAverageDuration());
                    stat.put("maxDuration", entry.getValue().getMaxDuration());
                    stat.put("slowCalls", entry.getValue().getSlowCalls());
                    stat.put("slowCallRate", entry.getValue().getSlowCallRate());
                    slowInterfaces.put(entry.getKey(), stat);
                });
            
            return AjaxResult.success(slowInterfaces);
        } catch (Exception e) {
            log.error("获取慢接口列表失败", e);
            return AjaxResult.error("获取慢接口列表失败");
        }
    }

    /**
     * 系统健康检查
     */
    @ApiOperation(value = "系统健康检查")
    @GetMapping("/health")
    public AjaxResult healthCheck() {
        try {
            Map<String, Object> health = new HashMap<>();
            
            // 检查接口平均响应时间
            Map<String, PerformanceMonitor.InterfaceStats> allStats = performanceMonitor.getAllStats();
            boolean hasSlowInterface = allStats.values().stream()
                .anyMatch(stat -> stat.getAverageDuration() > 30000);
            
            // 检查熔断器状态
            Map<String, Object> circuitBreakerStatus = fallbackService.getCircuitBreakerStatus();
            boolean hasOpenCircuitBreaker = circuitBreakerStatus.values().stream()
                .anyMatch(status -> {
                    if (status instanceof Map) {
                        Map<String, Object> statusMap = (Map<String, Object>) status;
                        return Boolean.TRUE.equals(statusMap.get("isOpen"));
                    }
                    return false;
                });
            
            // 检查异步任务数量
            int computingTaskCount = asyncComputeService.getComputingTaskCount();
            boolean hasTooManyTasks = computingTaskCount > 10;
            
            // 综合健康状态
            String status = "HEALTHY";
            if (hasSlowInterface || hasOpenCircuitBreaker || hasTooManyTasks) {
                status = "WARNING";
            }
            
            health.put("status", status);
            health.put("hasSlowInterface", hasSlowInterface);
            health.put("hasOpenCircuitBreaker", hasOpenCircuitBreaker);
            health.put("computingTaskCount", computingTaskCount);
            health.put("checkTime", System.currentTimeMillis());
            
            return AjaxResult.success(health);
        } catch (Exception e) {
            log.error("健康检查失败", e);
            return AjaxResult.error("健康检查失败");
        }
    }

    /**
     * 获取系统配置信息
     */
    @ApiOperation(value = "获取系统配置信息")
    @GetMapping("/config")
    public AjaxResult getSystemConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            
            // 线程池配置
            config.put("threadPoolSize", 10);
            config.put("precomputeThreadPoolSize", 5);
            config.put("batchQueryThreadPoolSize", 8);
            
            // 缓存配置
            JSONObject cacheConfig = multiLevelCacheService.getCacheStats();
            config.put("cacheConfig", cacheConfig);
            
            // 超时配置
            Map<String, Object> timeoutConfig = new HashMap<>();
            timeoutConfig.put("deviceInfoTimeout", 30);
            timeoutConfig.put("yieldTimeout", 60);
            timeoutConfig.put("energyTimeout", 120);
            config.put("timeoutConfig", timeoutConfig);
            
            return AjaxResult.success(config);
        } catch (Exception e) {
            log.error("获取系统配置失败", e);
            return AjaxResult.error("获取系统配置失败");
        }
    }
}
