# 产品数量查询接口历史数据一致性修复说明（基于IoTDB快照数据）

## 问题描述

在 `/productQuantity` 接口查询历史数据时，发现同一个历史时间段（如 2025-07-22 12:00:00 - 2025-07-22 13:00:00）的查询结果会随着查询时间的不同而变化，导致历史数据不一致的问题。

### 问题现象
- 查询历史时间段的产品数量
- 几分钟后再次查询同一时间段
- 返回的 `productionCount` 数值发生变化
- 历史数据应该是固定的，不应该变化
- `/productQuantity` 和 `/productDetail` 接口数据不一致

## 问题根因分析

### 1. 数据存储不完整
原有的IoTDB存储只保存了部分原始数据，缺乏完整的产品状态快照，导致历史查询时需要依赖实时计算。

### 2. 查询逻辑依赖实时数据
现有查询逻辑混合使用MySQL和IoTDB，但都依赖实时数据进行比例计算，无法保证历史数据的一致性。

### 3. 缺乏统一的数据源
不同接口使用不同的查询逻辑，导致数据不一致。

## 全新解决方案：基于IoTDB快照数据

### 1. 重新设计数据存储架构

#### 新增IoTDB快照存储
在每次MQTT消息处理时，保存完整的产品生产快照：

```java
// 新增快照数据结构
public static class ProductionSnapshot {
    private String productName;    // 产品名称
    private int status;           // 生产状态 (1=生产中, 0=停止)
    private int cumulativeCount;  // 累计产量
}

// 快照数据存储路径
root.{enterprise}.{deviceCode}.snapshot.{productKey}_status
root.{enterprise}.{deviceCode}.snapshot.{productKey}_count
```

#### 保持原有原始数据存储
继续保存原始MQTT数据作为备份：

```java
// 原始数据存储路径
root.{enterprise}.{deviceCode}.raw.counter
root.{enterprise}.{deviceCode}.raw.current_product
root.{enterprise}.{deviceCode}.raw.status
```

### 2. 修改MQTT消息处理逻辑

在 `executeMdIOQ` 方法中：

1. **收集所有产品状态**：遍历所有产品配置，收集每个产品的状态和累计产量
2. **保存完整快照**：每次MQTT消息都保存所有产品的完整状态快照
3. **保持向后兼容**：继续保存原始数据和MySQL记录

```java
// 准备产品快照数据
List<IoTDBUtil.ProductionSnapshot> productSnapshots = new ArrayList<>();

// 为每个产品添加快照
for (ZhDeviceTypeAttr attr : attrs) {
    // ... 解析产品状态 ...
    productSnapshots.add(new IoTDBUtil.ProductionSnapshot(
        productName, status, currentCount));
}

// 保存快照数据
ioTDBUtil.saveProductionSnapshot(enterpriseCode, deviceCode, productSnapshots, timestamp);
```

### 3. 重写查询逻辑

#### 新增基于快照的查询方法
```java
public int queryProductionCountFromSnapshot(String enterprise, String deviceCode,
                                          String productName, String startTime, String endTime)
```

#### 查询优先级策略
1. **优先使用快照数据**：最准确的历史数据查询方式
2. **回退到原始数据**：如果快照数据不可用
3. **最后使用MySQL**：如果IoTDB查询都失败

#### 统一查询接口
`/productQuantity` 和 `/productDetail` 接口都使用相同的IoTDB查询逻辑，确保数据一致性。

## 修复效果

### 历史数据查询
- ✅ **完全一致性**：基于IoTDB快照数据，历史查询结果完全一致
- ✅ **精确计算**：每次MQTT消息都保存完整状态，无需实时计算
- ✅ **数据完整性**：包含所有产品的状态和累计产量信息

### 接口一致性
- ✅ **统一数据源**：`/productQuantity` 和 `/productDetail` 使用相同的IoTDB查询逻辑
- ✅ **数据同步**：两个接口返回的数据完全一致
- ✅ **实时性保持**：当前时间段查询仍能反映最新变化

### 性能优化
- ✅ **查询效率**：IoTDB快照查询比复杂的MySQL计算更高效
- ✅ **数据可靠性**：多层回退机制确保查询成功率
- ✅ **存储优化**：结构化的快照数据便于查询和维护

## 验证方法

### 1. 单元测试
运行新的测试类 `IoTDBSnapshotConsistencyTest`：

```bash
# 测试IoTDB快照数据一致性
mvn test -Dtest=IoTDBSnapshotConsistencyTest#testSnapshotBasedHistoricalDataConsistency

# 测试两个接口的数据一致性
mvn test -Dtest=IoTDBSnapshotConsistencyTest#testProductQuantityAndDetailConsistency

# 测试IoTDB直接查询
mvn test -Dtest=IoTDBSnapshotConsistencyTest#testDirectIoTDBSnapshotQuery
```

### 2. 接口测试
使用PowerShell脚本多次调用接口验证一致性：

```powershell
# 多次查询同一历史时间段
for ($i=1; $i -le 5; $i++) {
    Write-Host "第 $i 次查询"
    Invoke-WebRequest -Uri "http://**********/prod-api/zhenghe/deviceProductionRecord/productQuantity?beginTime=2025-07-22%2012%3A00%3A00&endTime=2025-07-22%2013%3A00%3A00" -Headers @{"Authorization"="Bearer YOUR_TOKEN"}
    Start-Sleep -Seconds 2
}
```

### 3. 数据验证
检查IoTDB中的快照数据：

```sql
-- 查看快照时间序列
SHOW TIMESERIES root.f88540622c5245c9856061bfe11f133c.*.snapshot.*

-- 查询快照数据
SELECT * FROM root.f88540622c5245c9856061bfe11f133c.device001.snapshot
WHERE time >= '2025-07-22 12:00:00' AND time <= '2025-07-22 13:00:00'
```

### 4. 日志验证
查看应用日志，确认：
- 快照数据保存成功：`保存设备 {} 产品快照到IoTDB`
- 快照查询优先使用：`快照查询产量: {}`
- 回退机制正常：`快照查询失败，回退到原始数据查询`

## 数据迁移和部署

### 1. 现有数据兼容
- ✅ 新系统完全兼容现有数据
- ✅ 原有MySQL和IoTDB数据继续有效
- ✅ 渐进式升级，无需数据迁移

### 2. 部署步骤
1. **部署新代码**：包含快照存储和查询逻辑
2. **验证MQTT处理**：确认快照数据正常保存
3. **测试查询接口**：验证历史数据一致性
4. **监控系统运行**：观察性能和稳定性

### 3. 回滚方案
如果出现问题，可以快速回滚：
- 关闭快照查询，回退到原始数据查询
- 保持MySQL查询作为最终备份
- 不影响现有生产数据

## 技术架构

### 数据流程
```
MQTT消息 → 解析产品状态 → 保存快照数据 → 查询接口使用快照
    ↓           ↓              ↓              ↓
  原始数据   → MySQL记录   → IoTDB存储    → 一致性保证
```

### 存储结构
```
IoTDB时间序列结构：
root.{enterprise}.{device}.snapshot.{product}_status  # 产品状态
root.{enterprise}.{device}.snapshot.{product}_count   # 累计产量
root.{enterprise}.{device}.raw.counter               # 原始计数器
root.{enterprise}.{device}.raw.current_product       # 当前产品
root.{enterprise}.{device}.raw.status               # 生产状态
```

## 相关文件

### 核心修改文件
- `ruoyi-zhenghe/src/main/java/com/ruoyi/util/iotdb/IoTDBUtil.java` - 新增快照存储和查询方法
- `ruoyi-zhenghe/src/main/java/com/ruoyi/util/mq/MQTTReceiveCallback.java` - 修改MQTT处理逻辑
- `ruoyi-zhenghe/src/main/java/com/ruoyi/zhenghe/service/impl/ZhDeviceProductionRecordServiceImpl.java` - 更新查询逻辑

### 测试文件
- `ruoyi-zhenghe/src/test/java/com/ruoyi/zhenghe/IoTDBSnapshotConsistencyTest.java` - 新的一致性测试

### 文档文件
- `docs/历史数据一致性修复说明.md` - 本文档
