package com.ruoyi.util;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

public class DataTimeUtil {

    /**
     * 检查给定的字符串是否符合指定的时间格式。
     * 则返回true，否则返回false。
     *
     * @param time 需要检查的时间字符串，格式应为"yyyy-MM-dd HH:mm:ss"。
     * @return 如果字符串符合指定格式，返回true；否则返回false。
     */
    public static boolean isValidTimeFormat(String time) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sdf.parse(time);
            return true;
        } catch (Exception e) {
            // 如果解析失败（即格式不符合），返回false
            return false;
        }
    }

    /**
     * 将时间字符串转换为毫秒时间戳
     *
     * @param timeStr  时间字符串，例如 "2025-05-13 10:00:00"
     * @param pattern  格式，例如 "yyyy-MM-dd HH:mm:ss"
     * @param zoneId   时区，例如 "Asia/Shanghai"
     * @return         毫秒时间戳（long）
     */
    public static long toTimestamp(String timeStr, String pattern, String zoneId) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        LocalDateTime localDateTime = LocalDateTime.parse(timeStr, formatter);
        return localDateTime
                .atZone(ZoneId.of(zoneId))
                .toInstant()
                .toEpochMilli();
    }

    /**
     * 重载方法，默认使用 "yyyy-MM-dd HH:mm:ss" 格式和系统默认时区
     */
    public static long toTimestamp(String timeStr) {
        return toTimestamp(timeStr, "yyyy-MM-dd HH:mm:ss", ZoneId.systemDefault().getId());
    }
}
