package com.ruoyi.zhenghe.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import java.util.Date;
import java.util.Map;

/**
 * 检测记录保存请求数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
public class DetectionSaveRequestDto {

    /** 产品ID */
    @NotNull(message = "产品ID不能为空")
    @ApiModelProperty(value = "产品ID", required = true)
    private Long productId;

    /** 批次号 */
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /** 总数量 */
    @NotNull(message = "总数量不能为空")
    @Min(value = 1, message = "总数量必须大于0")
    @ApiModelProperty(value = "总数量", required = true)
    private Integer totalCount;

    /** 瑕疵数据Map */
    @ApiModelProperty(value = "瑕疵数据Map，key为瑕疵名称，value为瑕疵数量")
    private Map<String, Integer> defectDataMap;

    /** 检测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "检测时间，格式：yyyy-MM-dd HH:mm:ss")
    private Date detectionTime;

    /** 操作员 */
    @ApiModelProperty(value = "操作员")
    private String operator;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 计算瑕疵总数
     * @return 瑕疵总数
     */
    public Integer calculateDefectCount() {
        if (defectDataMap == null || defectDataMap.isEmpty()) {
            return 0;
        }
        return defectDataMap.values().stream().mapToInt(Integer::intValue).sum();
    }

    /**
     * 计算合格数量
     * @return 合格数量
     */
    public Integer calculateQualifiedCount() {
        if (totalCount == null) {
            return 0;
        }
        Integer defects = calculateDefectCount();
        return totalCount - defects;
    }
}
