package com.ruoyi.zhenghe.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.zhenghe.domain.ZhProduct;
import com.ruoyi.zhenghe.domain.ZhProductDetection;
import com.ruoyi.zhenghe.domain.dto.*;
import com.ruoyi.zhenghe.service.IZhProductDetectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品检测记录Controller
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@Api(tags = "产品检测记录管理")
@RestController
@RequestMapping("/zhenghe/detection")
public class ZhProductDetectionController extends BaseController
{
    @Autowired
    private IZhProductDetectionService zhProductDetectionService;

    /**
     * 查询产品检测记录列表（每条记录一行）
     */
    @ApiOperation(value = "查询产品检测记录列表", response = ZhProductDetection.class)
    //@PreAuthorize("@ss.hasPermi('zhenghe:detection:list')")
    @GetMapping("/list")
    public TableDataInfo list(ZhProductDetection zhProductDetection)
    {
        startPage();
        List<ZhProductDetection> list = zhProductDetectionService.selectZhProductDetectionList(zhProductDetection);
        return getDataTable(list);
    }

    /**
     * 查询产品检测记录分组列表（按日期分组）
     */
    @ApiOperation(value = "查询产品检测记录分组列表", response = DetectionSummaryDto.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productModel", value = "产品型号", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "pieceCount", value = "片数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "sectionCount", value = "节数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "timingMark", value = "正时标记", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "beginTime", value = "开始时间", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", paramType = "query", dataType = "string")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:detection:list')")
    @GetMapping("/group/list")
    public TableDataInfo groupList(@RequestParam(value = "productModel", required = false) String productModel,
                                  @RequestParam(value = "customerName", required = false) String customerName,
                                  @RequestParam(value = "machineType", required = false) String machineType,
                                  @RequestParam(value = "timingMark", required = false) String timingMark,
                                  @RequestParam(value = "printMark", required = false) String printMark,
                                  @RequestParam(value = "beginTime", required = false) String beginTime,
                                  @RequestParam(value = "endTime", required = false) String endTime)
    {
        // 构建查询对象
        ZhProductDetection zhProductDetection = new ZhProductDetection();

        // 设置产品筛选条件
        ZhProduct product = new ZhProduct();
        boolean hasProductFilter = false;

        if (productModel != null && !productModel.trim().isEmpty()) {
            product.setProductModel(productModel.trim());
            hasProductFilter = true;
        }
        if (customerName != null) {
            product.setCustomerName(customerName);
            hasProductFilter = true;
        }
        if (machineType != null) {
            product.setMachineType(machineType);
            hasProductFilter = true;
        }
        if (timingMark != null && !timingMark.trim().isEmpty()) {
            product.setTimingMark(timingMark.trim());
            hasProductFilter = true;
        }
        if (printMark != null && !printMark.trim().isEmpty()) {
            product.setPrintMark(printMark.trim());
            hasProductFilter = true;
        }

        if (hasProductFilter) {
            zhProductDetection.setProduct(product);
        }

        // 设置时间筛选条件
        Map<String, Object> params = new HashMap<>();
        if (beginTime != null && !beginTime.trim().isEmpty()) {
            params.put("beginTime", beginTime.trim());
        }
        if (endTime != null && !endTime.trim().isEmpty()) {
            params.put("endTime", endTime.trim());
        }
        zhProductDetection.setParams(params);

        startPage();
        List<DetectionSummaryDto> list = zhProductDetectionService.selectZhProductDetectionGroupList(zhProductDetection);
        return getDataTable(list);
    }

    /**
     * 导出产品检测记录列表
     */
    @ApiOperation(value = "导出产品检测记录列表")
    //@PreAuthorize("@ss.hasPermi('zhenghe:detection:export')")
    @Log(title = "产品检测记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhProductDetection zhProductDetection)
    {
        List<ZhProductDetection> list = zhProductDetectionService.selectZhProductDetectionList(zhProductDetection);
        ExcelUtil<ZhProductDetection> util = new ExcelUtil<ZhProductDetection>(ZhProductDetection.class);
        util.exportExcel(response, list, "产品检测记录数据");
    }

    /**
     * 获取产品检测记录详细信息
     */
    @ApiOperation(value = "获取产品检测记录详细信息", response = ZhProductDetection.class)
    @ApiImplicitParam(name = "id", value = "检测记录ID", required = true, paramType = "path", dataType = "long")
    //@PreAuthorize("@ss.hasPermi('zhenghe:detection:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(zhProductDetectionService.selectZhProductDetectionById(id));
    }

    /**
     * 新增产品检测记录
     */
    @ApiOperation(value = "新增产品检测记录")
    //@PreAuthorize("@ss.hasPermi('zhenghe:detection:add')")
    @Log(title = "产品检测记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZhProductDetection zhProductDetection)
    {
        return toAjax(zhProductDetectionService.insertZhProductDetection(zhProductDetection));
    }

    /**
     * 修改产品检测记录
     */
    @ApiOperation(value = "修改产品检测记录")
    //@PreAuthorize("@ss.hasPermi('zhenghe:detection:edit')")
    @Log(title = "产品检测记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhProductDetection zhProductDetection)
    {
        return toAjax(zhProductDetectionService.updateZhProductDetection(zhProductDetection));
    }

    /**
     * 删除产品检测记录
     */
    @ApiOperation(value = "删除产品检测记录")
    @ApiImplicitParam(name = "ids", value = "检测记录ID数组", required = true, paramType = "path", dataType = "long[]")
    //@PreAuthorize("@ss.hasPermi('zhenghe:detection:remove')")
    @Log(title = "产品检测记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(zhProductDetectionService.deleteZhProductDetectionByIds(ids));
    }

    /**
     * 获取柱状图数据
     */
    @ApiOperation(value = "获取柱状图数据", response = ProductDetectionChartDto.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productModel", value = "产品型号", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "pieceCount", value = "片数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "sectionCount", value = "节数", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "timingMark", value = "正时标记", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", paramType = "query", dataType = "string")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:detection:list')")
    @GetMapping("/chart")
    public AjaxResult getChartData(@RequestParam(value = "productModel", required = false) String productModel,
                                   @RequestParam(value = "customerName", required = false) String customerName,
                                   @RequestParam(value = "machineType", required = false) String machineType,
                                   @RequestParam(value = "timingMark", required = false) String timingMark,
                                   @RequestParam(value = "printMark", required = false) String printMark,
                                  @RequestParam(value = "startTime", required = false) String startTime,
                                  @RequestParam(value = "endTime", required = false) String endTime)


    {
        try {
            java.util.Date start = startTime != null ? com.ruoyi.common.utils.DateUtils.parseDate(startTime) : null;
            java.util.Date end = endTime != null ? com.ruoyi.common.utils.DateUtils.parseDate(endTime) : null;

            List<ProductDetectionChartDto> chartData = zhProductDetectionService.getChartData(customerName,
                    productModel, machineType, printMark, timingMark, start, end);

            return success(chartData);
        } catch (Exception e) {
            logger.error("获取柱状图数据失败", e);
            return error("获取柱状图数据失败：" + e.getMessage());
        }
    }

    /**
     * 保存检测结果（专用接口）
     */
    @ApiOperation(value = "保存检测结果")
    @Log(title = "保存检测结果", businessType = BusinessType.INSERT)
    @PostMapping("/saveResult")
    public AjaxResult saveResult(@Valid @RequestBody DetectionSaveRequestDto requestDto)
    {
        try {
            // 转换DTO为实体
            ZhProductDetection detection = convertToEntity(requestDto);
            
            // 保存检测结果
            int result = zhProductDetectionService.saveDetectionResult(detection);
            
            if (result > 0) {
                return success("检测结果保存成功");
            } else {
                return error("检测结果保存失败");
            }
        } catch (Exception e) {
            logger.error("保存检测结果失败", e);
            return error("保存检测结果失败：" + e.getMessage());
        }
    }

    /**
     * 根据产品ID查询检测记录列表
     */
    @ApiOperation(value = "根据产品ID查询检测记录列表", response = ZhProductDetection.class)
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, paramType = "path", dataType = "long")
    //@PreAuthorize("@ss.hasPermi('zhenghe:detection:list')")
    @GetMapping("/product/{productId}")
    public AjaxResult getByProductId(@PathVariable("productId") Long productId)
    {
        List<ZhProductDetection> list = zhProductDetectionService.selectZhProductDetectionByProductId(productId);
        return success(list);
    }

    /**
     * 根据产品ID查询检测统计数据
     */
    @ApiOperation(value = "根据产品ID查询检测统计数据", response = DetectionSummaryDto.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品ID", required = true, paramType = "path", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", paramType = "query", dataType = "string")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:detection:list')")
    @GetMapping("/statistics/product/{productId}")
    public AjaxResult getStatisticsByProductId(@PathVariable("productId") Long productId,
                                              @RequestParam(value = "startTime", required = false) String startTime,
                                              @RequestParam(value = "endTime", required = false) String endTime)
    {
        try {
            java.util.Date start = startTime != null ? com.ruoyi.common.utils.DateUtils.parseDate(startTime) : null;
            java.util.Date end = endTime != null ? com.ruoyi.common.utils.DateUtils.parseDate(endTime) : null;
            
            List<DetectionSummaryDto> list = zhProductDetectionService.selectDetectionStatisticsByProductId(productId, start, end);
            return success(list);
        } catch (Exception e) {
            logger.error("查询检测统计数据失败", e);
            return error("查询检测统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取产品检测分析数据
     */
    @ApiOperation(value = "获取产品检测分析数据", response = DetectionAnalysisDto.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品ID", required = true, paramType = "path", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", paramType = "query", dataType = "string")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:detection:list')")
    @GetMapping("/analysis/product/{productId}")
    public AjaxResult getAnalysisByProductId(@PathVariable("productId") Long productId,
                                           @RequestParam(value = "startTime", required = false) String startTime,
                                           @RequestParam(value = "endTime", required = false) String endTime)
    {
        try {
            java.util.Date start = startTime != null ? com.ruoyi.common.utils.DateUtils.parseDate(startTime) : null;
            java.util.Date end = endTime != null ? com.ruoyi.common.utils.DateUtils.parseDate(endTime) : null;
            
            DetectionAnalysisDto analysisDto = zhProductDetectionService.getDetectionAnalysisData(productId, start, end);
            
            if (analysisDto == null) {
                return error("未找到相关检测数据");
            }
            
            return success(analysisDto);
        } catch (Exception e) {
            logger.error("获取检测分析数据失败", e);
            return error("获取检测分析数据失败：" + e.getMessage());
        }
    }

    /**
     * 查询瑕疵细分数据
     */
    @ApiOperation(value = "查询瑕疵细分数据", response = DefectDetailResponseDto.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品ID", required = true, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "detectionDate", value = "检测日期", required = true, paramType = "query", dataType = "string")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:detection:list')")
    @GetMapping("/defectDetails")
    public TableDataInfo getDefectDetails(@RequestParam("productId") Long productId,
                                         @RequestParam("detectionDate") String detectionDate,
                                         @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                         @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize)
    {
        try {
            java.util.Date date = com.ruoyi.common.utils.DateUtils.parseDate(detectionDate);

            // 查询该产品该日期的所有检测记录
            List<ZhProductDetection> detectionList = zhProductDetectionService.selectDefectDetailsByProductIdAndDate(productId, date);

            // 合并瑕疵数据
            Map<String, Integer> mergedDefectData = new HashMap<>();
            for (ZhProductDetection detection : detectionList) {
                Map<String, Integer> defectMap = detection.getDefectDataMap();
                if (defectMap != null) {
                    for (Map.Entry<String, Integer> entry : defectMap.entrySet()) {
                        mergedDefectData.merge(entry.getKey(), entry.getValue(), Integer::sum);
                    }
                }
            }

            // 转换为响应DTO列表
            List<DefectDetailResponseDto> responseList = mergedDefectData.entrySet().stream()
                    .map(entry -> new DefectDetailResponseDto(entry.getKey(), entry.getValue()))
                    .sorted((a, b) -> b.getDefectCount().compareTo(a.getDefectCount()))
                    .collect(Collectors.toList());

            // 手动分页处理
            return manualPagination(responseList, pageNum, pageSize);

        } catch (Exception e) {
            logger.error("查询瑕疵细分数据失败", e);
            return getDataTable(new ArrayList<>());
        }
    }

    /**
     * 根据时间范围查询检测汇总数据
     */
    @ApiOperation(value = "根据时间范围查询检测汇总数据", response = DetectionSummaryDto.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", paramType = "query", dataType = "string")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:detection:list')")
    @GetMapping("/summary")
    public TableDataInfo getSummaryByDateRange(@RequestParam(value = "startTime", required = false) String startTime,
                                              @RequestParam(value = "endTime", required = false) String endTime)
    {
        try {
            java.util.Date start = startTime != null ? com.ruoyi.common.utils.DateUtils.parseDate(startTime) : null;
            java.util.Date end = endTime != null ? com.ruoyi.common.utils.DateUtils.parseDate(endTime) : null;

            startPage();
            List<DetectionSummaryDto> list = zhProductDetectionService.selectDetectionSummaryByDateRange(start, end);
            return getDataTable(list);
        } catch (Exception e) {
            logger.error("查询检测汇总数据失败", e);
            return getDataTable(new ArrayList<>());
        }
    }

    /**
     * 将DTO转换为实体对象
     *
     * @param requestDto 请求DTO
     * @return 实体对象
     */
    private ZhProductDetection convertToEntity(DetectionSaveRequestDto requestDto) {
        ZhProductDetection detection = new ZhProductDetection();
        detection.setProductId(requestDto.getProductId());
        detection.setBatchNo(requestDto.getBatchNo());
        detection.setTotalCount(requestDto.getTotalCount());
        detection.setDefectDataMap(requestDto.getDefectDataMap());
        detection.setDetectionTime(requestDto.getDetectionTime());
        detection.setOperator(requestDto.getOperator());
        detection.setRemark(requestDto.getRemark());

        return detection;
    }

    /**
     * 手动分页处理
     *
     * @param list 数据列表
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    private <T> TableDataInfo manualPagination(List<T> list, Integer pageNum, Integer pageSize) {
        if (list == null || list.isEmpty()) {
            return getDataTable(new ArrayList<>());
        }

        // 计算总数
        int total = list.size();

        // 计算分页参数
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);

        // 获取当前页数据
        List<T> pageData;
        if (startIndex >= total) {
            pageData = new ArrayList<>();
        } else {
            pageData = list.subList(startIndex, endIndex);
        }

        // 构建分页结果
        TableDataInfo dataTable = new TableDataInfo();
        dataTable.setCode(200);
        dataTable.setMsg("查询成功");
        dataTable.setTotal(total);
        dataTable.setRows(pageData);

        return dataTable;
    }
}
