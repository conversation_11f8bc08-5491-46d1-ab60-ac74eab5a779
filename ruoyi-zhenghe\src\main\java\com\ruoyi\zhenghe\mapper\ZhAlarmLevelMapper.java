package com.ruoyi.zhenghe.mapper;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhAlarmLevel;

/**
 * 告警等级管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface ZhAlarmLevelMapper 
{
    /**
     * 查询告警等级管理
     * 
     * @param id 告警等级管理主键
     * @return 告警等级管理
     */
    public ZhAlarmLevel selectZhAlarmLevelById(Long id);

    /**
     * 查询告警等级管理列表
     * 
     * @param zhAlarmLevel 告警等级管理
     * @return 告警等级管理集合
     */
    public List<ZhAlarmLevel> selectZhAlarmLevelList(ZhAlarmLevel zhAlarmLevel);

    /**
     * 新增告警等级管理
     * 
     * @param zhAlarmLevel 告警等级管理
     * @return 结果
     */
    public int insertZhAlarmLevel(ZhAlarmLevel zhAlarmLevel);

    /**
     * 修改告警等级管理
     * 
     * @param zhAlarmLevel 告警等级管理
     * @return 结果
     */
    public int updateZhAlarmLevel(ZhAlarmLevel zhAlarmLevel);

    /**
     * 删除告警等级管理
     * 
     * @param id 告警等级管理主键
     * @return 结果
     */
    public int deleteZhAlarmLevelById(Long id);

    /**
     * 批量删除告警等级管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZhAlarmLevelByIds(Long[] ids);
}
