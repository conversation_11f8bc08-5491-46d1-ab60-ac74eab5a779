package com.ruoyi.zhenghe.domain.query;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * IoT设备查询参数对象
 * 
 * 继承BaseEntity以支持@DataScope数据权限注解
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ZhIotEquipmentQuery extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 部门ID数组 */
    private Long[] deptId;

    /** 车间ID数组 */
    private Long[] workshopId;

    /** 设备类型ID数组 */
    private Long[] deviceTypeId;

    /** 工序ID数组 */
    private Long[] processId;

    /** 设备名称 */
    private String equipmentName;

    public Long[] getDeptId() {
        return deptId;
    }

    public void setDeptId(Long[] deptId) {
        this.deptId = deptId;
    }

    public Long[] getWorkshopId() {
        return workshopId;
    }

    public void setWorkshopId(Long[] workshopId) {
        this.workshopId = workshopId;
    }

    public Long[] getDeviceTypeId() {
        return deviceTypeId;
    }

    public void setDeviceTypeId(Long[] deviceTypeId) {
        this.deviceTypeId = deviceTypeId;
    }

    public Long[] getProcessId() {
        return processId;
    }

    public void setProcessId(Long[] processId) {
        this.processId = processId;
    }

    public String getEquipmentName() {
        return equipmentName;
    }

    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    @Override
    public String toString() {
        return "ZhIotEquipmentQuery{" +
                "deptId=" + java.util.Arrays.toString(deptId) +
                ", workshopId=" + java.util.Arrays.toString(workshopId) +
                ", deviceTypeId=" + java.util.Arrays.toString(deviceTypeId) +
                ", processId=" + java.util.Arrays.toString(processId) +
                ", equipmentName='" + equipmentName + '\'' +
                '}';
    }
}
