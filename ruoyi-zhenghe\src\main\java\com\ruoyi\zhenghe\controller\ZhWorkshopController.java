package com.ruoyi.zhenghe.controller;


import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.zhenghe.domain.DepartmentWorkshopTree;
import com.ruoyi.zhenghe.domain.ZhWorkshop;
import com.ruoyi.zhenghe.service.IZhWorkshopService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 车间管理Controller
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Api(tags = "车间管理")
@RestController
@RequestMapping("/zhenghe/workshop")
public class ZhWorkshopController extends BaseController {
    @Autowired
    private IZhWorkshopService zhWorkshopService;
    @Autowired
    private ISysDeptService deptService;

    /**
     * 查询车间管理列表
     */
    @ApiOperation(value = "查询车间管理列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "workshopName", value = "工厂车间名称", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "workshopCode", value = "车间编码", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, paramType = "query", dataType = "integer"),
            @ApiImplicitParam(name = "pageSize", value = "条数", required = true, paramType = "query", dataType = "integer"),
            @ApiImplicitParam(name = "parentId", value = "父级id 父级为0", required = true, paramType = "query", dataType = "integer")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:workshop:list')")
    //@DataScope(deptAlias = "w")
    @GetMapping("/list")
    public TableDataInfo list(ZhWorkshop zhWorkshop) {
        startPage();
        List<ZhWorkshop> list = zhWorkshopService.selectZhWorkshopList(zhWorkshop);
        return getDataTable(list);
    }

    @ApiOperation(value = "查询部门车间")
    @PreAuthorize("@ss.hasPermi('zhenghe:workshop:list')")
    @GetMapping("/list/tree")
    public AjaxResult treeList(ZhWorkshop zhWorkshop) {
        SysDept sysDept = new SysDept();
        // 不设置部门ID，让数据权限控制返回哪些部门

        List<DepartmentWorkshopTree> ans = new ArrayList<>();
        final List<SysDept> sysDepts = deptService.selectDeptList(sysDept);

        if (sysDepts != null) {
            for (SysDept oneDept : sysDepts) {
                if (oneDept.getParentId() == 0L || oneDept.getParentId() == 100L) {
                    continue;
                }

                DepartmentWorkshopTree oneDeptTree = new DepartmentWorkshopTree();
                oneDeptTree.setDepartmentId(oneDept.getDeptId() + "");
                oneDeptTree.setDepartmentName(oneDept.getDeptName());

                PageUtils.startPage(1, 999);
                zhWorkshop.setDeptId(oneDept.getDeptId());
                List<ZhWorkshop> list = zhWorkshopService.selectOnlyZhWorkshopList(zhWorkshop);

                if (list != null && list.size() > 0) {
                    List<DepartmentWorkshopTree> children = new ArrayList<>();
                    for (int i = 0; i < list.size(); i++) {
                        DepartmentWorkshopTree workshop = new DepartmentWorkshopTree();
                        workshop.setWorkshopId(list.get(i).getId() + "");
                        workshop.setWorkshopName(list.get(i).getWorkshopName());
                        children.add(workshop);
                    }
                    oneDeptTree.setChildren(children);
                }

                ans.add(oneDeptTree);
            }
        }

        return AjaxResult.success(ans);
    }

    /**
     * 导出车间管理列表
     */
    @ApiOperation(value = "导出车间管理列表")
    @PreAuthorize("@ss.hasPermi('zhenghe:workshop:export')")
    @Log(title = "车间管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhWorkshop zhWorkshop) {
        List<ZhWorkshop> list = zhWorkshopService.selectZhWorkshopList(zhWorkshop);
        ExcelUtil<ZhWorkshop> util = new ExcelUtil<ZhWorkshop>(ZhWorkshop.class);
        util.exportExcel(response, list, "车间管理数据");
    }

    /**
     * 导入基础数据-车间管理列表
     */
    @ApiOperation(value = "导入基础数据-车间管理列表")
    //@PreAuthorize("@ss.hasPermi('zhenghe:workshop:import')")
    @Log(title = "基础数据-车间", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws IOException {
        ExcelUtil<ZhWorkshop> util = new ExcelUtil<ZhWorkshop>(ZhWorkshop.class);
        List<ZhWorkshop> zhWorkshopList = util.importExcel(file.getInputStream());
        if (StringUtils.isNull(zhWorkshopList) || zhWorkshopList.size() == 0) {
            throw new ServiceException("导入车间数据不能为空");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ZhWorkshop zhWorkshop : zhWorkshopList) {
            try {
                zhWorkshop.setDeptId(SecurityUtils.getDeptId());
                zhWorkshopService.insertZhWorkshop(zhWorkshop);
                successNum++;
                successMsg.append("<br/>" + successNum + "、车间 " + zhWorkshop.getWorkshopName() + " 导入成功");
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、车间 " + zhWorkshop.getWorkshopName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return AjaxResult.success(successMsg.toString());
    }

    /**
     * 导出车间管理列表模板
     */
    @ApiOperation(value = "导出车间管理模板")
    @Log(title = "车间管理模板", businessType = BusinessType.EXPORT)
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response, ZhWorkshop zhWorkshop) {
        ExcelUtil<ZhWorkshop> util = new ExcelUtil<ZhWorkshop>(ZhWorkshop.class);
        util.exportExcel(response, new ArrayList<>(), "车间管理数据");
    }


    /**
     * 获取车间管理详细信息
     */
    @ApiOperation(value = "获取车间管理详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "工厂车间id", required = true, paramType = "path", dataType = "long")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:workshop:query')")
    @GetMapping(value = "/{id}")
    public R<ZhWorkshop> getInfo(@PathVariable("id") Long id) {
        return R.ok(zhWorkshopService.selectZhWorkshopById(id));
    }

    /**
     * 新增车间管理
     */
    @ApiOperation(value = "新增车间管理")
    @PreAuthorize("@ss.hasPermi('zhenghe:workshop:add')")
    @Log(title = "车间管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZhWorkshop zhWorkshop) {
        return toAjax(zhWorkshopService.insertZhWorkshop(zhWorkshop));
    }

    /**
     * 修改车间管理
     */
    @ApiOperation(value = "修改车间管理")
    @PreAuthorize("@ss.hasPermi('zhenghe:workshop:edit')")
    @Log(title = "车间管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhWorkshop zhWorkshop) {
        return toAjax(zhWorkshopService.updateZhWorkshop(zhWorkshop));
    }

    /**
     * 删除车间管理
     */
    @ApiOperation(value = "删除车间管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "工厂车间id", required = true, paramType = "path", dataType = "long")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:workshop:remove')")
    @Log(title = "车间管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(zhWorkshopService.deleteZhWorkshopByIds(ids));
    }
}
