package com.ruoyi.zhenghe.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.zhenghe.utils.DefectDataUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 产品检测记录对象 zh_product_detection
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
public class ZhProductDetection extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 产品ID */
    @ApiModelProperty(value = "产品ID")
    private Long productId;

    /** 批次号 */
    @Excel(name = "批次号", sort = 1)
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /** 总数量 */
    @Excel(name = "总数量", sort = 2)
    @ApiModelProperty(value = "总数量")
    private Integer totalCount;

    /** 合格数量 */
    @Excel(name = "合格数量", sort = 3)
    @ApiModelProperty(value = "合格数量")
    private Integer qualifiedCount;

    /** 瑕疵数量 */
    @Excel(name = "瑕疵数量", sort = 4)
    @ApiModelProperty(value = "瑕疵数量")
    private Integer defectCount;

    /** 瑕疵数据(JSON格式) */
    @ApiModelProperty(value = "瑕疵数据(JSON格式)")
    private String defectData;

    /** 检测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "检测时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 5)
    @ApiModelProperty(value = "检测时间")
    private Date detectionTime;

    /** 操作员 */
    @Excel(name = "操作员", sort = 6)
    @ApiModelProperty(value = "操作员")
    private String operator;

    /** 状态（0-禁用，1-启用） */
    @Excel(name = "状态", sort = 7, readConverterExp = "0=禁用,1=启用")
    @ApiModelProperty(value = "状态（0-禁用，1-启用）")
    private Integer status;

    /** 关联的产品信息 */
    @ApiModelProperty(value = "关联的产品信息")
    private ZhProduct product;

    /** 瑕疵数据Map（前端展示用） */
    @ApiModelProperty(value = "瑕疵数据Map（前端展示用）")
    private Map<String, Integer> defectDataMap;

    /**
     * 获取瑕疵数据Map
     * @return 瑕疵数据Map
     */
    public Map<String, Integer> getDefectDataMap() {
        if (defectDataMap == null && defectData != null) {
            defectDataMap = DefectDataUtils.jsonToMap(defectData);
        }
        return defectDataMap;
    }

    /**
     * 设置瑕疵数据Map，同时更新JSON字符串
     * @param defectDataMap 瑕疵数据Map
     */
    public void setDefectDataMap(Map<String, Integer> defectDataMap) {
        this.defectDataMap = defectDataMap;
        if (defectDataMap != null) {
            this.defectData = DefectDataUtils.mapToJson(defectDataMap);
        }
    }

    /**
     * 计算瑕疵总数
     * @return 瑕疵总数
     */
    public Integer calculateDefectCount() {
        Map<String, Integer> dataMap = getDefectDataMap();
        if (dataMap == null || dataMap.isEmpty()) {
            return 0;
        }
        return dataMap.values().stream().mapToInt(Integer::intValue).sum();
    }

    /**
     * 计算合格数量
     * @return 合格数量
     */
    public Integer calculateQualifiedCount() {
        if (totalCount == null) {
            return 0;
        }
        Integer defects = calculateDefectCount();
        return totalCount - defects;
    }

    /**
     * 自动计算并设置合格数量和瑕疵数量
     */
    public void autoCalculateCounts() {
        this.defectCount = calculateDefectCount();
        this.qualifiedCount = calculateQualifiedCount();
    }
}
