package com.ruoyi.zhenghe.service;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhProduct;
import com.ruoyi.zhenghe.domain.dto.ProductOptionsTreeDto;

/**
 * 产品信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface IZhProductService 
{
    /**
     * 查询产品信息
     * 
     * @param id 产品信息主键
     * @return 产品信息
     */
    public ZhProduct selectZhProductById(Long id);

    /**
     * 查询产品信息列表
     * 
     * @param zhProduct 产品信息
     * @return 产品信息集合
     */
    public List<ZhProduct> selectZhProductList(ZhProduct zhProduct);

    /**
     * 新增产品信息
     * 
     * @param zhProduct 产品信息
     * @return 结果
     */
    public int insertZhProduct(ZhProduct zhProduct);

    /**
     * 修改产品信息
     * 
     * @param zhProduct 产品信息
     * @return 结果
     */
    public int updateZhProduct(ZhProduct zhProduct);

    /**
     * 批量删除产品信息
     * 
     * @param ids 需要删除的产品信息主键集合
     * @return 结果
     */
    public int deleteZhProductByIds(Long[] ids);

    /**
     * 删除产品信息信息
     * 
     * @param id 产品信息主键
     * @return 结果
     */
    public int deleteZhProductById(Long id);



    /**
     * 获取所有产品型号列表
     *
     * @return 产品型号集合
     */
    public List<String> selectAllProductModels(String productModel);

    /**
     * 获取所有片数列表
     *
     * @return 片数集合
     */
    public List<Integer> selectAllPieceCounts();

    /**
     * 获取所有节数列表
     *
     * @return 节数集合
     */
    public List<Integer> selectAllSectionCounts();

    /**
     * 获取所有正时标记列表
     *
     * @return 正时标记集合
     */
    public List<String> selectAllTimingMarks();

    /**
     * 根据产品型号获取片数下拉选项
     * @param productModel 产品型号
     * @return 片数列表
     */
    public List<Integer> selectPieceCountsByProductModel(String productModel);

    /**
     * 获取客户名称下拉选项
     * @param customerName 客户名称筛选条件（支持中文或拼音）
     * @return 客户名称列表
     */
    List<String> selectPieceCustomer(String customerName);

    /**
     * 根据产品型号和片数获取节数下拉选项
     * @param productModel 产品型号
     * @param pieceCount 片数
     * @return 节数列表
     */
    public List<Integer> selectSectionCountsByProductModelAndPieceCount(String productModel, Integer pieceCount);

    /**
     * 根据产品型号、片数和节数获取正时标记下拉选项
     * @param productModel 产品型号
     * @param pieceCount 片数
     * @param sectionCount 节数
     * @return 正时标记列表
     */
    public List<String> selectTimingMarksByProductModelAndPieceCountAndSectionCount(String productModel, Integer pieceCount, Integer sectionCount);

    /**
     * 根据组合条件查询产品
     *
     * @param productModel 产品型号
     * @param pieceCount 片数
     * @param sectionCount 节数
     * @param timingMark 正时标记
     * @return 产品信息
     */
    public ZhProduct selectProductByCombination(String productModel, Integer pieceCount, Integer sectionCount, String timingMark);

    /**
     * 获取产品选项树状结构
     *
     * @return 产品选项树状结构
     */
    public List<ProductOptionsTreeDto> getProductOptionsTree();
}
