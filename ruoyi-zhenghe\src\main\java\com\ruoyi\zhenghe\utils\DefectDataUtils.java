package com.ruoyi.zhenghe.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 瑕疵数据工具类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public class DefectDataUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将瑕疵数据Map转换为JSON字符串
     * 
     * @param defectMap 瑕疵数据Map
     * @return JSON字符串
     */
    public static String mapToJson(Map<String, Integer> defectMap) {
        if (defectMap == null || defectMap.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(defectMap);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("瑕疵数据转换为JSON失败", e);
        }
    }

    /**
     * 将JSON字符串转换为瑕疵数据Map
     * 
     * @param jsonStr JSON字符串
     * @return 瑕疵数据Map
     */
    public static Map<String, Integer> jsonToMap(String jsonStr) {
        if (!StringUtils.hasText(jsonStr)) {
            return new HashMap<>();
        }
        try {
            return objectMapper.readValue(jsonStr, new TypeReference<Map<String, Integer>>() {});
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON转换为瑕疵数据失败", e);
        }
    }

    /**
     * 合并多个瑕疵数据JSON字符串（用于分组查询时的数据聚合）
     * 
     * @param defectDataList 多个瑕疵数据JSON字符串，用|分隔
     * @return 合并后的瑕疵数据Map
     */
    public static Map<String, Integer> mergeDefectData(String defectDataList) {
        Map<String, Integer> mergedMap = new HashMap<>();
        
        if (!StringUtils.hasText(defectDataList)) {
            return mergedMap;
        }
        
        String[] dataArray = defectDataList.split("\\|");
        for (String data : dataArray) {
            if (StringUtils.hasText(data)) {
                Map<String, Integer> currentMap = jsonToMap(data);
                for (Map.Entry<String, Integer> entry : currentMap.entrySet()) {
                    mergedMap.merge(entry.getKey(), entry.getValue(), Integer::sum);
                }
            }
        }
        
        return mergedMap;
    }

    /**
     * 创建瑕疵数据Map
     * 
     * @return 空的瑕疵数据Map
     */
    public static Map<String, Integer> createDefectMap() {
        return new HashMap<>();
    }

    /**
     * 添加瑕疵数据
     * 
     * @param defectMap 瑕疵数据Map
     * @param defectType 瑕疵类型
     * @param count 数量
     */
    public static void addDefect(Map<String, Integer> defectMap, String defectType, Integer count) {
        if (defectMap != null && StringUtils.hasText(defectType) && count != null && count > 0) {
            defectMap.merge(defectType, count, Integer::sum);
        }
    }
}
