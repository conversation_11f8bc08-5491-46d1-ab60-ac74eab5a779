package com.ruoyi.zhenghe.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.zhenghe.mapper.ZhPeriodMapper;
import com.ruoyi.zhenghe.domain.ZhPeriod;
import com.ruoyi.zhenghe.service.IZhPeriodService;

/**
 * 时段Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
@Service
public class ZhPeriodServiceImpl implements IZhPeriodService 
{
    @Autowired
    private ZhPeriodMapper zhPeriodMapper;

    /**
     * 查询时段
     * 
     * @param id 时段主键
     * @return 时段
     */
    @Override
    public ZhPeriod selectZhPeriodById(Long id)
    {
        return zhPeriodMapper.selectZhPeriodById(id);
    }

    /**
     * 查询时段列表
     * 
     * @param zhPeriod 时段
     * @return 时段
     */
    @Override
    public List<ZhPeriod> selectZhPeriodList(ZhPeriod zhPeriod)
    {
        return zhPeriodMapper.selectZhPeriodList(zhPeriod);
    }

    /**
     * 新增时段
     * 
     * @param zhPeriod 时段
     * @return 结果
     */
    @Override
    public int insertZhPeriod(ZhPeriod zhPeriod)
    {
        zhPeriod.setCreateTime(DateUtils.getNowDate());
        return zhPeriodMapper.insertZhPeriod(zhPeriod);
    }

    /**
     * 修改时段
     * 
     * @param zhPeriod 时段
     * @return 结果
     */
    @Override
    public int updateZhPeriod(ZhPeriod zhPeriod)
    {
        zhPeriod.setUpdateTime(DateUtils.getNowDate());
        return zhPeriodMapper.updateZhPeriod(zhPeriod);
    }

    /**
     * 批量删除时段
     * 
     * @param ids 需要删除的时段主键
     * @return 结果
     */
    @Override
    public int deleteZhPeriodByIds(Long[] ids)
    {
        return zhPeriodMapper.deleteZhPeriodByIds(ids);
    }

    /**
     * 删除时段信息
     * 
     * @param id 时段主键
     * @return 结果
     */
    @Override
    public int deleteZhPeriodById(Long id)
    {
        return zhPeriodMapper.deleteZhPeriodById(id);
    }
}
