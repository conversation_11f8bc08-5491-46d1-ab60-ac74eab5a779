package com.ruoyi.zhenghe.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.sql.SqlUtil;
import com.ruoyi.util.DataTimeUtil;
import com.ruoyi.util.iotdb.IoTDBUtil;
import com.ruoyi.zhenghe.domain.PeriodMaintainData;
import com.ruoyi.zhenghe.domain.ZhDeviceTypeAttr;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.domain.ZhWorkshop;
import com.ruoyi.zhenghe.domain.vo.ZhWorkshopVo;
import com.ruoyi.zhenghe.mapper.ZhDeviceTypeAttrMapper;
import com.ruoyi.zhenghe.mapper.ZhIotEquipmentMapper;
import com.ruoyi.zhenghe.mapper.ZhPeriodMaintainMapper;
import com.ruoyi.zhenghe.mapper.ZhWorkshopMapper;
import com.ruoyi.zhenghe.service.IZhIotEquipmentService;
import com.ruoyi.zhenghe.service.IZhWorkshopService;
import com.ruoyi.zhenghe.util.ElectricQuantityCalculator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 数据分析能耗Controller
 *
 * <AUTHOR>
 * @date 2025-03-19
 */
@Api(tags = "数据分析能耗Controller")
@RestController
@RequestMapping("/zhenghe/electricQuantity")
public class IotElectricQuantityController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(IotElectricQuantityController.class);

    @Autowired
    private ZhIotEquipmentMapper zhIotEquipmentMapper;
    private static final String TENANT = Constants.TENANT;
    @Autowired
    private IoTDBUtil ioTDBUtil;
    @Autowired
    private ZhWorkshopMapper zhWorkshopMapper;
    @Autowired
    private ZhPeriodMaintainMapper periodMaintainMapper;
    @Autowired
    private IZhWorkshopService zhWorkshopService;
    @Autowired
    private IZhIotEquipmentService zhIotEquipmentService;
    @Resource
    private ZhDeviceTypeAttrMapper attrMapper;
    @Autowired
    private ElectricQuantityCalculator electricQuantityCalculator;


    /**
     * 查询电量列表
     */
    @ApiOperation(value = "查询电量列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "事业部id", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "id", value = "车间id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, paramType = "query", dataType = "integer"),
            @ApiImplicitParam(name = "pageSize", value = "条数", required = true, paramType = "query", dataType = "integer")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:workshop:list')")
    @GetMapping("/electricQuantityList")
    public TableDataInfo electricQuantityList(ZhWorkshop zhWorkshop) throws StatementExecutionException, IoTDBConnectionException {

        //查询车间列表
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = 1;
        Integer pageSize = 1000;
        String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        Boolean reasonable = pageDomain.getReasonable();
        PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);

        List<ZhWorkshop> zhWorkshopList = zhWorkshopService.selectOnlyZhWorkshopList(zhWorkshop);

        if (ObjectUtil.isNull(zhWorkshopList)) {
            throw new ServiceException("车间为空");
        }
        // 如果未提供开始时间或结束时间，则默认查询过去24小时的数据
        if (ObjectUtil.isNull(zhWorkshop.getStartTime()) || ObjectUtil.isNull(zhWorkshop.getEndTime())) {
            zhWorkshop.setStartTime(DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyy-MM-dd HH:mm:ss"));
            zhWorkshop.setEndTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }
        // 校验时间格式
        if (!DataTimeUtil.isValidTimeFormat(zhWorkshop.getStartTime()) || !DataTimeUtil.isValidTimeFormat(zhWorkshop.getEndTime())) {
            throw new IllegalArgumentException("时间格式不正确，应为 'yyyy-MM-dd HH:mm:ss'");
        }

        if (zhWorkshop.getStartTime().compareTo(zhWorkshop.getEndTime()) > 0) {
            throw new IllegalArgumentException("开始时间不能大于结束时间");
        }

        zhWorkshopList = parseWorkshopPower(zhWorkshop, zhWorkshopList);

        for (int i = 0; i < zhWorkshopList.size(); i++) {
            final ZhWorkshop workshop = zhWorkshopList.get(i);
            if (workshop.getWorkshopPower() == null || workshop.getWorkshopPower() == 0) {
                zhWorkshopList.remove(i);
                i--;
            }
        }
        final TableDataInfo dataTable = getDataTable(zhWorkshopList);
        dataTable.setTotal(zhWorkshopList.size());
        return dataTable;
    }


    /**
     * 查询尖峰平谷电量列表
     */
    @ApiOperation(value = "查询尖峰平谷电量列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "事业部id", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "id", value = "车间id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, paramType = "query", dataType = "integer"),
            @ApiImplicitParam(name = "pageSize", value = "条数", required = true, paramType = "query", dataType = "integer")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:workshop:list')")
    @GetMapping("/periodMaintainDataPowerList")
    public TableDataInfo periodMaintainDataPowerList(ZhWorkshop zhWorkshop) throws StatementExecutionException, IoTDBConnectionException {
        // 如果未提供开始时间或结束时间，则默认查询过去24小时的数据
        if (ObjectUtil.isNull(zhWorkshop.getStartTime()) || ObjectUtil.isNull(zhWorkshop.getEndTime())) {
            zhWorkshop.setStartTime(DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyy-MM-dd HH:mm:ss"));
            zhWorkshop.setEndTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }
        // 校验时间格式
        if (!DataTimeUtil.isValidTimeFormat(zhWorkshop.getStartTime()) || !DataTimeUtil.isValidTimeFormat(zhWorkshop.getEndTime())) {
            throw new IllegalArgumentException("时间格式不正确，应为 'yyyy-MM-dd HH:mm:ss'");
        }

        if (zhWorkshop.getStartTime().compareTo(zhWorkshop.getEndTime()) > 0) {
            throw new IllegalArgumentException("开始时间不能大于结束时间");
        }
        //查询车间列表
        startPage();
        List<ZhWorkshop> zhWorkshopList = zhWorkshopService.selectZhWorkshopList(zhWorkshop);
        if (ObjectUtil.isNull(zhWorkshopList)) {
            throw new ServiceException("车间为空");
        }
        zhWorkshopList = parseWorkshopPower(zhWorkshop, zhWorkshopList);

        return getDataTable(zhWorkshopList);
    }


    private List<ZhWorkshop> parseWorkshopPower(ZhWorkshop zhWorkshop, List<ZhWorkshop> zhWorkshopList) {
        // 使用统一的电量计算工具
        for (ZhWorkshop workshop : zhWorkshopList) {
            Double workshopPower = 0.0;
            //平段电量
            Double flatPower = 0.0;
            //谷段电量
            Double valleyPower = 0.0;
            //峰段电量
            Double crestPower = 0.0;
            //深谷电量
            Double barrancaPower = 0.0;
            //尖峰电量
            Double spikePower = 0.0;

            try {
                // 计算分时段电量（保持原有逻辑）
                ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
                zhIotEquipment.setStartTime(zhWorkshop.getStartTime());
                zhIotEquipment.setEndTime(zhWorkshop.getEndTime());
                zhIotEquipment.setWorkshopId(workshop.getId());
                if (ObjectUtil.isNotNull(zhWorkshop.getDeviceTypeId())) {
                    zhIotEquipment.setDeviceTypeId(zhWorkshop.getDeviceTypeId());
                }

                //查询该车间绑定的电表类型设备
                List<ZhIotEquipment> zhIotEquipmentList = zhIotEquipmentMapper.selectZhIotEquipmentList(zhIotEquipment);
                if (ObjectUtil.isNotNull(zhIotEquipmentList) && zhIotEquipmentList.size() > 0) {
                    for (ZhIotEquipment iotEquipment : zhIotEquipmentList) {
                        // 设备筛选：只处理电表设备
                        if (!isElectricMeterDevice(iotEquipment)) {
                            continue;
                        }

                        Double customMultiple = 1.0;
                        try {
                            ZhDeviceTypeAttr zhDeviceTypeAttr = new ZhDeviceTypeAttr();
                            zhDeviceTypeAttr.setTslId(iotEquipment.getDeviceTypeId());
                            zhDeviceTypeAttr.setAttrCode("TotalPower");
                            final List<ZhDeviceTypeAttr> attrs = attrMapper.selectZhDeviceTypeAttrList(zhDeviceTypeAttr);
                            if (ObjectUtil.isNotNull(attrs) && attrs.size() > 0) {
                                final ZhDeviceTypeAttr zhDeviceTypeAttr1 = attrs.get(0);
                                customMultiple = zhDeviceTypeAttr1.getAttrMultiple().doubleValue();
                            }
                        } catch (Exception e) {
                            // 忽略异常，使用默认倍数
                        }

                        List<PeriodMaintainData> periodMaintainDataList = ioTDBUtil.queryIotEquipmentPeriodMaintain(TENANT, iotEquipment.getEquipmentCode(), "TotalPower", zhIotEquipment.getStartTime(), zhIotEquipment.getEndTime());
                        //如果电量值不为空
                        if (ObjectUtil.isNotNull(periodMaintainDataList) && periodMaintainDataList.size() > 0) {
                            for (PeriodMaintainData periodMaintainData : periodMaintainDataList) {
                                String periodName = periodMaintainMapper.selectZhPeriodMaintain(periodMaintainData.getFiledName(), periodMaintainData.getYearTime(), periodMaintainData.getPeriod());
                                if (StringUtils.isNotEmpty(periodName)) {
                                    final Double val = periodMaintainData.getVal();
                                    if (periodName.equals("平段")) {
                                        flatPower = flatPower + val * customMultiple;
                                    }
                                    if (periodName.equals("谷段")) {
                                        valleyPower = valleyPower + val * customMultiple;
                                    }
                                    if (periodName.equals("峰段")) {
                                        crestPower = crestPower + val * customMultiple;
                                    }
                                    if (periodName.equals("深谷")) {
                                        barrancaPower = barrancaPower + val * customMultiple;
                                    }
                                    if (periodName.equals("尖峰")) {
                                        spikePower = spikePower + val * customMultiple;
                                    }
                                }
                            }
                        }
                    }
                }

                // 计算总电量：各时间段电量之和
                // 这样确保总电量与分时段电量一致
                workshopPower = flatPower + valleyPower + crestPower + barrancaPower + spikePower;

                log.debug("车间 {} 电量计算 - 平段:{}, 谷段:{}, 峰段:{}, 深谷:{}, 尖峰:{}, 总计:{}",
                    workshop.getWorkshopName(), flatPower, valleyPower, crestPower, barrancaPower, spikePower, workshopPower);

            } catch (Exception e) {
                log.error("计算车间电量失败 - 车间: {}", workshop.getWorkshopName(), e);
                workshopPower = 0.0;
            }

            workshop.setWorkshopPower(Double.parseDouble(String.format("%.3f", workshopPower)));
            workshop.setFlatPower((Double.parseDouble(String.format("%.3f", flatPower))));
            workshop.setValleyPower(Double.parseDouble(String.format("%.3f", valleyPower)));
            workshop.setCrestPower(Double.parseDouble(String.format("%.3f", crestPower)));
            workshop.setBarrancaPower(Double.parseDouble(String.format("%.3f", barrancaPower)));
            workshop.setSpikePower(Double.parseDouble(String.format("%.3f", spikePower)));
            workshop.setStartTime(zhWorkshop.getStartTime());
            workshop.setEndTime(zhWorkshop.getEndTime());
        }
        return zhWorkshopList;
    }

    /**
     * 导出尖峰平谷电量列表
     */
    @ApiOperation(value = "导出尖峰平谷电量列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "事业部id", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "id", value = "车间id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, paramType = "query", dataType = "integer"),
            @ApiImplicitParam(name = "pageSize", value = "条数", required = true, paramType = "query", dataType = "integer")
    })
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhWorkshop zhWorkshop) throws StatementExecutionException, IoTDBConnectionException {
        // 如果未提供开始时间或结束时间，则默认查询过去24小时的数据
        if (ObjectUtil.isNull(zhWorkshop.getStartTime()) || ObjectUtil.isNull(zhWorkshop.getEndTime())) {
            zhWorkshop.setStartTime(DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyy-MM-dd HH:mm:ss"));
            zhWorkshop.setEndTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }
        // 校验时间格式
        if (!DataTimeUtil.isValidTimeFormat(zhWorkshop.getStartTime()) || !DataTimeUtil.isValidTimeFormat(zhWorkshop.getEndTime())) {
            throw new IllegalArgumentException("时间格式不正确，应为 'yyyy-MM-dd HH:mm:ss'");
        }

        if (zhWorkshop.getStartTime().compareTo(zhWorkshop.getEndTime()) > 0) {
            throw new IllegalArgumentException("开始时间不能大于结束时间");
        }
        //查询车间列表
        startPage();
        List<ZhWorkshopVo> zhWorkshopList = zhWorkshopMapper.selectZhWorkshopVoList(zhWorkshop);
        if (ObjectUtil.isNull(zhWorkshopList)) {
            throw new ServiceException("车间为空");
        }
        // 使用统一的电量计算工具
        for (ZhWorkshopVo workshop : zhWorkshopList) {
            Double workshopPower = 0.0;
            //平段电量
            Double flatPower = 0.0;
            //谷段电量
            Double valleyPower = 0.0;
            //峰段电量
            Double crestPower = 0.0;
            //深谷电量
            Double barrancaPower = 0.0;
            //尖峰电量
            Double spikePower = 0.0;

            try {
                // 计算分时段电量（保持原有逻辑，但添加设备筛选）
                ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
                zhIotEquipment.setStartTime(zhWorkshop.getStartTime());
                zhIotEquipment.setEndTime(zhWorkshop.getEndTime());
                zhIotEquipment.setWorkshopId(workshop.getId());
                if (ObjectUtil.isNotNull(zhWorkshop.getDeviceTypeId())) {
                    zhIotEquipment.setDeviceTypeId(zhWorkshop.getDeviceTypeId());
                }

                //查询该车间绑定的电表类型设备
                List<ZhIotEquipment> zhIotEquipmentList = zhIotEquipmentMapper.selectZhIotEquipmentList(zhIotEquipment);
                if (ObjectUtil.isNotNull(zhIotEquipmentList) && zhIotEquipmentList.size() > 0) {
                    for (ZhIotEquipment iotEquipment : zhIotEquipmentList) {
                        // 设备筛选：只处理电表设备
                        if (!isElectricMeterDevice(iotEquipment)) {
                            continue;
                        }

                        Double customMultiple = 1.0;
                        try {
                            ZhDeviceTypeAttr zhDeviceTypeAttr = new ZhDeviceTypeAttr();
                            zhDeviceTypeAttr.setTslId(iotEquipment.getDeviceTypeId());
                            zhDeviceTypeAttr.setAttrCode("TotalPower");
                            final List<ZhDeviceTypeAttr> attrs = attrMapper.selectZhDeviceTypeAttrList(zhDeviceTypeAttr);
                            if (ObjectUtil.isNotNull(attrs) && attrs.size() > 0) {
                                final ZhDeviceTypeAttr zhDeviceTypeAttr1 = attrs.get(0);
                                customMultiple = zhDeviceTypeAttr1.getAttrMultiple().doubleValue();
                            }
                        } catch (Exception e) {
                            // 忽略异常，使用默认倍数
                        }

                        List<PeriodMaintainData> periodMaintainDataList = ioTDBUtil.queryIotEquipmentPeriodMaintain(TENANT, iotEquipment.getEquipmentCode(), "TotalPower", zhIotEquipment.getStartTime(), zhIotEquipment.getEndTime());
                        //如果电量值不为空
                        if (ObjectUtil.isNotNull(periodMaintainDataList) && periodMaintainDataList.size() > 0) {
                            for (PeriodMaintainData periodMaintainData : periodMaintainDataList) {
                                String periodName = periodMaintainMapper.selectZhPeriodMaintain(periodMaintainData.getFiledName(), periodMaintainData.getYearTime(), periodMaintainData.getPeriod());
                                if (StringUtils.isNotEmpty(periodName)) {
                                    final Double val = periodMaintainData.getVal();
                                    if (periodName.equals("平段")) {
                                        flatPower = flatPower + val * customMultiple;
                                    }
                                    if (periodName.equals("谷段")) {
                                        valleyPower = valleyPower + val * customMultiple;
                                    }
                                    if (periodName.equals("峰段")) {
                                        crestPower = crestPower + val * customMultiple;
                                    }
                                    if (periodName.equals("深谷")) {
                                        barrancaPower = barrancaPower + val * customMultiple;
                                    }
                                    if (periodName.equals("尖峰")) {
                                        spikePower = spikePower + val * customMultiple;
                                    }
                                }
                            }
                        }
                    }
                }

                // 计算总电量：各时间段电量之和
                // 这样确保总电量与分时段电量一致
                workshopPower = flatPower + valleyPower + crestPower + barrancaPower + spikePower;

                log.debug("车间 {} 电量计算 - 平段:{}, 谷段:{}, 峰段:{}, 深谷:{}, 尖峰:{}, 总计:{}",
                    workshop.getWorkshopName(), flatPower, valleyPower, crestPower, barrancaPower, spikePower, workshopPower);

            } catch (Exception e) {
                log.error("计算车间电量失败 - 车间: {}", workshop.getWorkshopName(), e);
                workshopPower = 0.0;
                flatPower = 0.0;
                valleyPower = 0.0;
                crestPower = 0.0;
                barrancaPower = 0.0;
                spikePower = 0.0;
            }

            workshop.setWorkshopPower(workshopPower);
            workshop.setFlatPower(flatPower);
            workshop.setValleyPower(valleyPower);
            workshop.setCrestPower(crestPower);
            workshop.setBarrancaPower(barrancaPower);
            workshop.setSpikePower(spikePower);
            workshop.setStartTime(zhWorkshop.getStartTime());
            workshop.setEndTime(zhWorkshop.getEndTime());
        }
        ExcelUtil<ZhWorkshopVo> util = new ExcelUtil<>(ZhWorkshopVo.class);
        util.exportExcel(response, zhWorkshopList, "尖峰平谷电量列表");
    }

    /**
     * 查询尖峰平谷电量饼图
     */
    @ApiOperation(value = "查询尖峰平谷电量饼图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "事业部id", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "id", value = "车间id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, paramType = "query", dataType = "integer"),
            @ApiImplicitParam(name = "pageSize", value = "条数", required = true, paramType = "query", dataType = "integer")
    })
    @GetMapping("/periodMaintainDataPowerPie")
    @PreAuthorize("@ss.hasPermi('zhenghe:workshop:list')")

    public Map<String, Double> periodMaintainDataPowerPie(ZhWorkshop zhWorkshop) throws StatementExecutionException, IoTDBConnectionException {
        // 如果未提供开始时间或结束时间，则默认查询过去24小时的数据
        if (ObjectUtil.isNull(zhWorkshop.getStartTime()) || ObjectUtil.isNull(zhWorkshop.getEndTime())) {
            zhWorkshop.setStartTime(DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyy-MM-dd HH:mm:ss"));
            zhWorkshop.setEndTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }
        // 校验时间格式
        if (!DataTimeUtil.isValidTimeFormat(zhWorkshop.getStartTime()) || !DataTimeUtil.isValidTimeFormat(zhWorkshop.getEndTime())) {
            throw new IllegalArgumentException("时间格式不正确，应为 'yyyy-MM-dd HH:mm:ss'");
        }

        if (zhWorkshop.getStartTime().compareTo(zhWorkshop.getEndTime()) > 0) {
            throw new IllegalArgumentException("开始时间不能大于结束时间");
        }
        //查询车间列表
        startPage();
        List<ZhWorkshop> zhWorkshopList = zhWorkshopService.selectZhWorkshopList(zhWorkshop);
        if (ObjectUtil.isNull(zhWorkshopList)) {
            throw new ServiceException("车间为空");
        }
        // 使用统一的电量计算工具
        Double workshopPower = 0.0;
        //平段电量
        Double flatPower = 0.0;
        //谷段电量
        Double valleyPower = 0.0;
        //峰段电量
        Double crestPower = 0.0;
        //深谷电量
        Double barrancaPower = 0.0;
        //尖峰电量
        Double spikePower = 0.0;
        zhWorkshopList = parseWorkshopPower(zhWorkshop, zhWorkshopList);
        for (ZhWorkshop one : zhWorkshopList) {
            workshopPower = workshopPower + one.getWorkshopPower();
            flatPower = flatPower + one.getFlatPower();
            valleyPower = valleyPower + one.getValleyPower();
            crestPower = crestPower + one.getCrestPower();
            barrancaPower = barrancaPower + one.getBarrancaPower();
            spikePower = spikePower + one.getSpikePower();
        }
        HashMap<String, Double> resultMap = new HashMap<>();
        resultMap.put("workshopPower", Double.valueOf(String.format("%.3f", workshopPower)));
        resultMap.put("flatPower", Double.valueOf(String.format("%.3f", flatPower)));
        resultMap.put("valleyPower", Double.valueOf(String.format("%.3f", valleyPower)));
        resultMap.put("crestPower", Double.valueOf(String.format("%.3f", crestPower)));
        resultMap.put("barrancaPower", Double.valueOf(String.format("%.3f", barrancaPower)));
        resultMap.put("spikePower", Double.valueOf(String.format("%.3f", spikePower)));
        return resultMap;
    }

    /**
     * 验证总电量与分时段电量一致性
     * 用于调试和验证电量统计的准确性
     * 对比两种计算方式：1.ElectricQuantityCalculator计算的总电量 2.分时段电量之和
     */
    @ApiOperation(value = "验证总电量与分时段电量一致性")
    @GetMapping("/validatePowerConsistency")
    @PreAuthorize("@ss.hasPermi('zhenghe:workshop:list')")
    public Map<String, Object> validatePowerConsistency(ZhWorkshop zhWorkshop) throws StatementExecutionException, IoTDBConnectionException {

        // 设置默认时间
        if (ObjectUtil.isNull(zhWorkshop.getStartTime()) || ObjectUtil.isNull(zhWorkshop.getEndTime())) {
            zhWorkshop.setStartTime(DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyy-MM-dd HH:mm:ss"));
            zhWorkshop.setEndTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }

        // 查询车间列表
        List<ZhWorkshop> zhWorkshopList = zhWorkshopService.selectZhWorkshopList(zhWorkshop);

        Map<String, Object> result = new HashMap<>();
        result.put("startTime", zhWorkshop.getStartTime());
        result.put("endTime", zhWorkshop.getEndTime());

        if (ObjectUtil.isNull(zhWorkshopList) || zhWorkshopList.isEmpty()) {
            result.put("error", "未找到车间数据");
            return result;
        }

        try {
            List<Map<String, Object>> workshopDetails = new ArrayList<>();
            double totalPowerByCalculator = 0.0;
            double totalPowerByPeriodSum = 0.0;

            for (ZhWorkshop workshop : zhWorkshopList) {
                Map<String, Object> detail = new HashMap<>();
                detail.put("workshopName", workshop.getWorkshopName());

                // 方法1：使用ElectricQuantityCalculator计算总电量
                double powerByCalculator = electricQuantityCalculator.calculateWorkshopPower(
                        workshop.getId(),
                        zhWorkshop.getDeviceTypeId(),
                        zhWorkshop.getStartTime(),
                        zhWorkshop.getEndTime(),
                        true  // 只统计电表设备
                );

                // 方法2：计算分时段电量之和
                double flatPower = 0.0, valleyPower = 0.0, crestPower = 0.0, barrancaPower = 0.0, spikePower = 0.0;

                ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
                zhIotEquipment.setStartTime(zhWorkshop.getStartTime());
                zhIotEquipment.setEndTime(zhWorkshop.getEndTime());
                zhIotEquipment.setWorkshopId(workshop.getId());
                if (ObjectUtil.isNotNull(zhWorkshop.getDeviceTypeId())) {
                    zhIotEquipment.setDeviceTypeId(zhWorkshop.getDeviceTypeId());
                }

                List<ZhIotEquipment> zhIotEquipmentList = zhIotEquipmentMapper.selectZhIotEquipmentList(zhIotEquipment);
                if (ObjectUtil.isNotNull(zhIotEquipmentList) && zhIotEquipmentList.size() > 0) {
                    for (ZhIotEquipment iotEquipment : zhIotEquipmentList) {
                        if (!isElectricMeterDevice(iotEquipment)) {
                            continue;
                        }

                        Double customMultiple = 1.0;
                        try {
                            ZhDeviceTypeAttr zhDeviceTypeAttr = new ZhDeviceTypeAttr();
                            zhDeviceTypeAttr.setTslId(iotEquipment.getDeviceTypeId());
                            zhDeviceTypeAttr.setAttrCode("TotalPower");
                            final List<ZhDeviceTypeAttr> attrs = attrMapper.selectZhDeviceTypeAttrList(zhDeviceTypeAttr);
                            if (ObjectUtil.isNotNull(attrs) && attrs.size() > 0) {
                                final ZhDeviceTypeAttr zhDeviceTypeAttr1 = attrs.get(0);
                                customMultiple = zhDeviceTypeAttr1.getAttrMultiple().doubleValue();
                            }
                        } catch (Exception e) {
                            // 忽略异常，使用默认倍数
                        }

                        List<PeriodMaintainData> periodMaintainDataList = ioTDBUtil.queryIotEquipmentPeriodMaintain(TENANT, iotEquipment.getEquipmentCode(), "TotalPower", zhIotEquipment.getStartTime(), zhIotEquipment.getEndTime());
                        if (ObjectUtil.isNotNull(periodMaintainDataList) && periodMaintainDataList.size() > 0) {
                            for (PeriodMaintainData periodMaintainData : periodMaintainDataList) {
                                String periodName = periodMaintainMapper.selectZhPeriodMaintain(periodMaintainData.getFiledName(), periodMaintainData.getYearTime(), periodMaintainData.getPeriod());
                                if (StringUtils.isNotEmpty(periodName)) {
                                    final Double val = periodMaintainData.getVal();
                                    if (periodName.equals("平段")) {
                                        flatPower = flatPower + val * customMultiple;
                                    }
                                    if (periodName.equals("谷段")) {
                                        valleyPower = valleyPower + val * customMultiple;
                                    }
                                    if (periodName.equals("峰段")) {
                                        crestPower = crestPower + val * customMultiple;
                                    }
                                    if (periodName.equals("深谷")) {
                                        barrancaPower = barrancaPower + val * customMultiple;
                                    }
                                    if (periodName.equals("尖峰")) {
                                        spikePower = spikePower + val * customMultiple;
                                    }
                                }
                            }
                        }
                    }
                }

                double powerByPeriodSum = flatPower + valleyPower + crestPower + barrancaPower + spikePower;

                detail.put("powerByCalculator", String.format("%.3f", powerByCalculator));
                detail.put("powerByPeriodSum", String.format("%.3f", powerByPeriodSum));
                detail.put("flatPower", String.format("%.3f", flatPower));
                detail.put("valleyPower", String.format("%.3f", valleyPower));
                detail.put("crestPower", String.format("%.3f", crestPower));
                detail.put("barrancaPower", String.format("%.3f", barrancaPower));
                detail.put("spikePower", String.format("%.3f", spikePower));
                detail.put("difference", String.format("%.3f", Math.abs(powerByCalculator - powerByPeriodSum)));
                detail.put("isConsistent", Math.abs(powerByCalculator - powerByPeriodSum) < 0.001);

                workshopDetails.add(detail);
                totalPowerByCalculator += powerByCalculator;
                totalPowerByPeriodSum += powerByPeriodSum;
            }

            result.put("totalPowerByCalculator", String.format("%.3f", totalPowerByCalculator));
            result.put("totalPowerByPeriodSum", String.format("%.3f", totalPowerByPeriodSum));
            result.put("totalDifference", String.format("%.3f", Math.abs(totalPowerByCalculator - totalPowerByPeriodSum)));
            result.put("isOverallConsistent", Math.abs(totalPowerByCalculator - totalPowerByPeriodSum) < 0.001);
            result.put("workshopDetails", workshopDetails);
            result.put("explanation", "powerByCalculator使用电量差值计算(结束值-开始值)，powerByPeriodSum使用分时段累计电量之和");

        } catch (Exception e) {
            log.error("验证电量计算一致性失败", e);
            result.put("error", "计算失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 验证三个接口的电量计算一致性
     * 用于调试和验证电量统计的准确性
     * 包括：electricQuantityList、periodMaintainDataPowerList、periodMaintainDataPowerPie
     */
    @ApiOperation(value = "验证电量计算一致性")
    @GetMapping("/validateElectricQuantity")
    @PreAuthorize("@ss.hasPermi('zhenghe:workshop:list')")
    public Map<String, Object> validateElectricQuantity(ZhWorkshop zhWorkshop) throws StatementExecutionException, IoTDBConnectionException {

        // 设置默认时间
        if (ObjectUtil.isNull(zhWorkshop.getStartTime()) || ObjectUtil.isNull(zhWorkshop.getEndTime())) {
            zhWorkshop.setStartTime(DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyy-MM-dd HH:mm:ss"));
            zhWorkshop.setEndTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }

        // 查询车间列表
        List<ZhWorkshop> zhWorkshopList = zhWorkshopService.selectZhWorkshopList(zhWorkshop);

        Map<String, Object> result = new HashMap<>();
        result.put("startTime", zhWorkshop.getStartTime());
        result.put("endTime", zhWorkshop.getEndTime());

        if (ObjectUtil.isNull(zhWorkshopList) || zhWorkshopList.isEmpty()) {
            result.put("error", "未找到车间数据");
            return result;
        }

        try {
            // 方法1：只统计电表设备（与第一个接口一致）
            double totalPowerMethod1 = 0.0;
            for (ZhWorkshop workshop : zhWorkshopList) {
                double workshopPower = electricQuantityCalculator.calculateWorkshopPower(
                        workshop.getId(),
                        zhWorkshop.getDeviceTypeId(),
                        zhWorkshop.getStartTime(),
                        zhWorkshop.getEndTime(),
                        true  // 只统计电表设备
                );
                totalPowerMethod1 += workshopPower;
            }

            // 方法2：统计所有设备（与第二个接口一致）
            double totalPowerMethod2 = 0.0;
            for (ZhWorkshop workshop : zhWorkshopList) {
                double workshopPower = electricQuantityCalculator.calculateWorkshopPower(
                        workshop.getId(),
                        zhWorkshop.getDeviceTypeId(),
                        zhWorkshop.getStartTime(),
                        zhWorkshop.getEndTime(),
                        false  // 统计所有设备
                );
                totalPowerMethod2 += workshopPower;
            }

            result.put("method1_onlyElectricMeter", String.format("%.3f", totalPowerMethod1));
            result.put("method2_allDevices", String.format("%.3f", totalPowerMethod2));
            result.put("difference", String.format("%.3f", Math.abs(totalPowerMethod1 - totalPowerMethod2)));
            result.put("isConsistent", Math.abs(totalPowerMethod1 - totalPowerMethod2) < 0.001);

            // 详细的车间对比
            List<Map<String, Object>> workshopDetails = new ArrayList<>();
            for (ZhWorkshop workshop : zhWorkshopList) {
                Map<String, Object> detail = new HashMap<>();
                detail.put("workshopName", workshop.getWorkshopName());

                double power1 = electricQuantityCalculator.calculateWorkshopPower(
                        workshop.getId(), zhWorkshop.getDeviceTypeId(),
                        zhWorkshop.getStartTime(), zhWorkshop.getEndTime(), true);

                double power2 = electricQuantityCalculator.calculateWorkshopPower(
                        workshop.getId(), zhWorkshop.getDeviceTypeId(),
                        zhWorkshop.getStartTime(), zhWorkshop.getEndTime(), false);

                detail.put("onlyElectricMeter", String.format("%.3f", power1));
                detail.put("allDevices", String.format("%.3f", power2));
                detail.put("difference", String.format("%.3f", Math.abs(power1 - power2)));

                workshopDetails.add(detail);
            }
            result.put("workshopDetails", workshopDetails);

        } catch (Exception e) {
            log.error("验证电量计算一致性失败", e);
            result.put("error", "计算失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 判断是否为电表设备
     *
     * @param equipment 设备信息
     * @return true=电表设备，false=非电表设备
     */
    private boolean isElectricMeterDevice(ZhIotEquipment equipment) {
        if (equipment.getEquipmentName() == null) {
            return false;
        }

        String equipmentName = equipment.getEquipmentName();
        return equipmentName.contains("电表") || equipmentName.contains("总电");
    }

}
