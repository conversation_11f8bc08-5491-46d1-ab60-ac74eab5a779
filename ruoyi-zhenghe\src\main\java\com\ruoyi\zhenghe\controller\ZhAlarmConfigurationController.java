package com.ruoyi.zhenghe.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.zhenghe.domain.ZhAlarmConfiguration;
import com.ruoyi.zhenghe.service.IZhAlarmConfigurationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 告警配置Controller
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Api(tags = "告警配置Controller")
@RestController
@RequestMapping("/zhenghe/configuration")
public class ZhAlarmConfigurationController extends BaseController
{
    @Autowired
    private IZhAlarmConfigurationService zhAlarmConfigurationService;

    /**
     * 查询告警配置列表
     */
    @ApiOperation(value = "查询告警配置列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "equipmentName",value = "设备名称",required = false,paramType = "query",dataType = "String"),
            @ApiImplicitParam(name = "attrName",value = "采集项名称",required = false,paramType = "query",dataType = "String"),
            @ApiImplicitParam(name = "alarmLevelName",value = "告警等级名称",required = false,paramType = "query",dataType = "String"),
            @ApiImplicitParam(name = "attrId",value = "采集项id",required = false,paramType = "query",dataType = "long"),
            @ApiImplicitParam(name = "alarmLevelId",value = "告警等级id",required = false,paramType = "query",dataType = "long"),
            @ApiImplicitParam(name = "pageNum",value = "页数",required = true,paramType = "query",dataType = "integer"),
            @ApiImplicitParam(name = "pageSize",value = "条数",required = true,paramType = "query",dataType = "integer")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:configuration:list')")
    @GetMapping("/list")
    public TableDataInfo list(ZhAlarmConfiguration zhAlarmConfiguration)
    {
        startPage();
        List<ZhAlarmConfiguration> list = zhAlarmConfigurationService.selectZhAlarmConfigurationList(zhAlarmConfiguration);
        return getDataTable(list);
    }

    /**
     * 导出告警配置列表
     */
    @ApiOperation(value = "导出告警配置列表")
    @PreAuthorize("@ss.hasPermi('zhenghe:configuration:export')")
    @Log(title = "告警配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhAlarmConfiguration zhAlarmConfiguration)
    {
        List<ZhAlarmConfiguration> list = zhAlarmConfigurationService.selectZhAlarmConfigurationList(zhAlarmConfiguration);
        ExcelUtil<ZhAlarmConfiguration> util = new ExcelUtil<ZhAlarmConfiguration>(ZhAlarmConfiguration.class);
        util.exportExcel(response, list, "告警配置数据");
    }

    /**
     * 获取告警配置详细信息
     */
    @ApiOperation(value = "获取告警配置详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "告警配置id",required = true,paramType = "path",dataType = "long")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:configuration:query')")
    @GetMapping(value = "/{id}")
    public R<ZhAlarmConfiguration> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(zhAlarmConfigurationService.selectZhAlarmConfigurationById(id));
    }

    /**
     * 新增告警配置
     */
    @ApiOperation(value = "新增告警配置")
    @PreAuthorize("@ss.hasPermi('zhenghe:configuration:add')")
    @Log(title = "告警配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZhAlarmConfiguration zhAlarmConfiguration)
    {
        return toAjax(zhAlarmConfigurationService.insertZhAlarmConfiguration(zhAlarmConfiguration));
    }

    /**
     * 修改告警配置
     */
    @ApiOperation(value = "修改告警配置")
    @PreAuthorize("@ss.hasPermi('zhenghe:configuration:edit')")
    @Log(title = "告警配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhAlarmConfiguration zhAlarmConfiguration)
    {
        return toAjax(zhAlarmConfigurationService.updateZhAlarmConfiguration(zhAlarmConfiguration));
    }

    /**
     * 删除告警配置
     */
    @ApiOperation(value = "删除告警配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids",value = "告警配置id",required = true,paramType = "path",dataType = "long")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:configuration:remove')")
    @Log(title = "告警配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(zhAlarmConfigurationService.deleteZhAlarmConfigurationByIds(ids));
    }
}
