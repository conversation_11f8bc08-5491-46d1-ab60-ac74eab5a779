package com.ruoyi.zhenghe.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.zhenghe.domain.ZhProductDefect;
import com.ruoyi.zhenghe.service.IZhProductDefectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 产品瑕疵Controller
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Api(tags = "产品瑕疵管理")
@RestController
@RequestMapping("/zhenghe/product/defect")
public class ZhProductDefectController extends BaseController {
    @Autowired
    private IZhProductDefectService zhProductDefectService;

    /**
     * 查询产品瑕疵列表
     */
    @ApiOperation(value = "查询产品瑕疵列表", response = ZhProductDefect.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "zhProductDefect", value = "产品瑕疵", required = true, paramType = "body", dataType = "ZhProductDefect")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:defect:list')")
    @GetMapping("/list")
    public TableDataInfo list(ZhProductDefect zhProductDefect) {
        startPage();
        List<ZhProductDefect> list = zhProductDefectService.selectZhProductDefectList(zhProductDefect);
        return getDataTable(list);
    }

    /**
     * 根据产品ID查询瑕疵列表
     */
    @ApiOperation(value = "根据产品ID查询瑕疵列表", response = ZhProductDefect.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品ID", required = true, paramType = "path", dataType = "Long")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:defect:list')")
    @GetMapping("/product/{productId}")
    public AjaxResult listByProductId(@PathVariable("productId") Long productId) {
        List<ZhProductDefect> list = zhProductDefectService.selectZhProductDefectListByProductId(productId);
        return success(list);
    }

    /**
     * 导出产品瑕疵列表
     */
    @ApiOperation(value = "导出产品瑕疵列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "zhProductDefect", value = "产品瑕疵", required = true, paramType = "body", dataType = "ZhProductDefect")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:defect:export')")
    @Log(title = "产品瑕疵", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhProductDefect zhProductDefect) {
        List<ZhProductDefect> list = zhProductDefectService.selectZhProductDefectList(zhProductDefect);
        ExcelUtil<ZhProductDefect> util = new ExcelUtil<>(ZhProductDefect.class);
        util.exportExcel(response, list, "产品瑕疵数据");
    }

    /**
     * 导入瑕疵模板下载
     */
    @ApiOperation(value = "导入瑕疵模板下载")
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:defect:import')")
    @PostMapping("/download/template")
    public void downloadTemplate(HttpServletResponse response) {
        List<ZhProductDefect> list = new ArrayList<>();
        ExcelUtil<ZhProductDefect> util = new ExcelUtil<>(ZhProductDefect.class);
        util.exportExcel(response, list, "产品瑕疵");
    }

    /**
     * 导入瑕疵表
     */
    @ApiOperation(value = "导入瑕疵表")
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:defect:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws IOException {
        ExcelUtil<ZhProductDefect> util = new ExcelUtil<>(ZhProductDefect.class);
        List<ZhProductDefect> defectList = util.importExcel(file.getInputStream());

        if (StringUtils.isNull(defectList) || defectList.size() == 0) {
            throw new ServiceException("导入瑕疵数据不能为空");
        }
        int successNum = 0;
        int failureNum = 0;

        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (ZhProductDefect defect : defectList) {
            if (defect.getProductId() == null || defect.getDefectName() == null || defect.getDefectName().trim().isEmpty()) {
                failureNum++;
                failureMsg.append("<br/>").append(failureNum).append("、产品ID和瑕疵名称不能为空");
                continue;
            }
            try {
                zhProductDefectService.insertZhProductDefect(defect);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、瑕疵 ").append(defect.getDefectName()).append(" 导入成功");
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、瑕疵 " + defect.getDefectName() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return AjaxResult.success(successMsg.toString());
    }

    /**
     * 获取产品瑕疵详细信息
     */
    @ApiOperation(value = "获取产品瑕疵详细信息", response = ZhProductDefect.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "产品瑕疵id", required = true, paramType = "path", dataType = "long")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:defect:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(zhProductDefectService.selectZhProductDefectById(id));
    }

    /**
     * 新增产品瑕疵
     */
    @ApiOperation(value = "新增产品瑕疵")
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:defect:add')")
    @Log(title = "产品瑕疵", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZhProductDefect zhProductDefect) {
        if (zhProductDefect.getProductId() == null) {
            return error("产品ID不能为空");
        }
        if (StringUtils.isEmpty(zhProductDefect.getDefectName())) {
            return error("瑕疵名称不能为空");
        }
        return toAjax(zhProductDefectService.insertZhProductDefect(zhProductDefect));
    }

    /**
     * 修改产品瑕疵
     */
    @ApiOperation(value = "修改产品瑕疵")
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:defect:edit')")
    @Log(title = "产品瑕疵", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhProductDefect zhProductDefect) {
        if (StringUtils.isEmpty(zhProductDefect.getDefectName())) {
            return error("瑕疵名称不能为空");
        }
        return toAjax(zhProductDefectService.updateZhProductDefect(zhProductDefect));
    }

    /**
     * 删除产品瑕疵
     */
    @ApiOperation(value = "删除产品瑕疵by ids")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "主键ID串", required = true, paramType = "path", dataType = "Long"),
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:defect:remove')")
    @Log(title = "产品瑕疵", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(zhProductDefectService.deleteZhProductDefectByIds(ids));
    }

    /**
     * 启用瑕疵
     */
    @ApiOperation(value = "启用瑕疵")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "主键ID串", required = true, paramType = "path", dataType = "Long"),
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:defect:edit')")
    @Log(title = "产品瑕疵", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{ids}")
    public AjaxResult enable(@PathVariable Long[] ids) {
        return toAjax(zhProductDefectService.enableZhProductDefect(ids));
    }

    /**
     * 禁用瑕疵
     */
    @ApiOperation(value = "禁用瑕疵")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "主键ID串", required = true, paramType = "path", dataType = "Long"),
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:defect:edit')")
    @Log(title = "产品瑕疵", businessType = BusinessType.UPDATE)
    @PutMapping("/disable/{ids}")
    public AjaxResult disable(@PathVariable Long[] ids) {
        return toAjax(zhProductDefectService.disableZhProductDefect(ids));
    }

    /**
     * 获取产品下一个排序号
     */
    @ApiOperation(value = "获取产品下一个排序号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品ID", required = true, paramType = "path", dataType = "Long")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:defect:query')")
    @GetMapping("/nextSort/{productId}")
    public AjaxResult getNextSortOrder(@PathVariable("productId") Long productId) {
        Integer nextSortOrder = zhProductDefectService.getNextSortOrder(productId);
        return success(nextSortOrder);
    }
}
