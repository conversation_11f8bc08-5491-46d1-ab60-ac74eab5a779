<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhDeviceProductionRecordMapper">
    
    <resultMap type="ZhDeviceProductionRecord" id="ZhDeviceProductionRecordResult">
        <result property="id"    column="id"    />
        <result property="deviceCode"    column="device_code"    />
        <result property="productName"    column="product_name"    />
        <result property="cumulativeCount"    column="cumulative_count"    />
        <result property="productionCount"    column="production_count"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="productionDuration"    column="production_duration"    />
        <result property="status"    column="status"    />
        <result property="recordTime"    column="record_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="deviceName"    column="device_name"    />
        <result property="businessUnit"    column="business_unit"    />
        <result property="workshopName"    column="workshop_name"    />
        <result property="originalProductionCount"    column="original_production_count"    />
        <result property="queryBeginTime"    column="query_begin_time"    />
        <result property="queryEndTime"    column="query_end_time"    />
    </resultMap>

    <sql id="selectZhDeviceProductionRecordVo">
        select id, device_code, product_name, cumulative_count, production_count, start_time, end_time, production_duration, status, record_time, create_by, create_time, update_by, update_time, remark from zh_device_production_record
    </sql>

    <select id="selectZhDeviceProductionRecordList" parameterType="ZhDeviceProductionRecord" resultMap="ZhDeviceProductionRecordResult">
        <include refid="selectZhDeviceProductionRecordVo"/>
        <where>  
            <if test="deviceCode != null  and deviceCode != ''"> and device_code like concat('%', #{deviceCode}, '%')</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="cumulativeCount != null "> and cumulative_count = #{cumulativeCount}</if>
            <if test="productionCount != null "> and production_count = #{productionCount}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="productionDuration != null "> and production_duration = #{productionDuration}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="recordTime != null "> and record_time = #{recordTime}</if>
        </where>
        order by record_time desc
    </select>
    
    <select id="selectZhDeviceProductionRecordById" parameterType="Long" resultMap="ZhDeviceProductionRecordResult">
        <include refid="selectZhDeviceProductionRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectCurrentProductionRecord" resultMap="ZhDeviceProductionRecordResult">
        <include refid="selectZhDeviceProductionRecordVo"/>
        where device_code = #{deviceCode} and product_name = #{productName} and status = 1
        order by record_time desc
        limit 1
    </select>

    <select id="selectLatestProductionRecord" resultMap="ZhDeviceProductionRecordResult">
        <include refid="selectZhDeviceProductionRecordVo"/>
        where device_code = #{deviceCode} and product_name = #{productName}
        order by record_time desc
        limit 1
    </select>

    <select id="selectProductionSummary" resultMap="ZhDeviceProductionRecordResult">
        SELECT 
            device_code,
            product_name,
            SUM(production_count) as production_count,
            COUNT(*) as total_records
        FROM zh_device_production_record
        WHERE device_code = #{deviceCode}
        <if test="beginTime != null and beginTime != ''">
            AND record_time >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND record_time &lt;= #{endTime}
        </if>
        GROUP BY device_code, product_name
        ORDER BY production_count DESC
    </select>
        
    <insert id="insertZhDeviceProductionRecord" parameterType="ZhDeviceProductionRecord" useGeneratedKeys="true" keyProperty="id">
        insert into zh_device_production_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceCode != null and deviceCode != ''">device_code,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="cumulativeCount != null">cumulative_count,</if>
            <if test="productionCount != null">production_count,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="productionDuration != null">production_duration,</if>
            <if test="status != null">status,</if>
            <if test="recordTime != null">record_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceCode != null and deviceCode != ''">#{deviceCode},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="cumulativeCount != null">#{cumulativeCount},</if>
            <if test="productionCount != null">#{productionCount},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="productionDuration != null">#{productionDuration},</if>
            <if test="status != null">#{status},</if>
            <if test="recordTime != null">#{recordTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateZhDeviceProductionRecord" parameterType="ZhDeviceProductionRecord">
        update zh_device_production_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceCode != null and deviceCode != ''">device_code = #{deviceCode},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="cumulativeCount != null">cumulative_count = #{cumulativeCount},</if>
            <if test="productionCount != null">production_count = #{productionCount},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="productionDuration != null">production_duration = #{productionDuration},</if>
            <if test="status != null">status = #{status},</if>
            <if test="recordTime != null">record_time = #{recordTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="finishProductionRecord">
        update zh_device_production_record
        set status = 0, end_time = #{endTime}, production_duration = #{productionDuration}, update_time = now()
        where id = #{id}
    </update>

    <select id="selectActiveProductionRecords" resultMap="ZhDeviceProductionRecordResult">
        <include refid="selectZhDeviceProductionRecordVo"/>
        where device_code = #{deviceCode} and status = 1
        order by record_time desc
    </select>

    <delete id="deleteZhDeviceProductionRecordById" parameterType="Long">
        delete from zh_device_production_record where id = #{id}
    </delete>

    <delete id="deleteZhDeviceProductionRecordByIds" parameterType="String">
        delete from zh_device_production_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询产品数量展示界面数据 -->
    <select id="selectProductQuantityList" resultMap="ZhDeviceProductionRecordResult">
        SELECT
            zpr.product_name,
            GROUP_CONCAT(DISTINCT zpr.device_code SEPARATOR ',') as device_code,
            COUNT(DISTINCT zpr.device_code) as cumulative_count,
            SUM(
                CASE
                    -- 已完成记录：完全在查询时间范围内
                    WHEN zpr.end_time IS NOT NULL AND zpr.start_time >= #{beginTime} AND zpr.end_time &lt;= #{endTime} THEN
                        zpr.production_count
                    -- 已完成记录：与查询时间范围有重叠，按时间比例计算
                    WHEN zpr.end_time IS NOT NULL AND zpr.start_time &lt; #{endTime} AND zpr.end_time &gt; #{beginTime} THEN
                        CASE
                            WHEN TIMESTAMPDIFF(SECOND, zpr.start_time, zpr.end_time) > 0 THEN
                                ROUND(
                                    zpr.production_count *
                                    (
                                        TIMESTAMPDIFF(SECOND,
                                            GREATEST(zpr.start_time, #{beginTime}),
                                            LEAST(zpr.end_time, #{endTime})
                                        ) /
                                        TIMESTAMPDIFF(SECOND, zpr.start_time, zpr.end_time)
                                    )
                                )
                            ELSE zpr.production_count
                        END
                    -- 进行中记录：开始时间在查询范围内，假设持续到查询结束时间
                    WHEN zpr.end_time IS NULL AND zpr.start_time >= #{beginTime} AND zpr.start_time &lt; #{endTime} THEN
                        CASE
                            WHEN TIMESTAMPDIFF(SECOND, zpr.start_time, LEAST(NOW(), #{endTime})) > 0 THEN
                                ROUND(
                                    zpr.production_count *
                                    (
                                        TIMESTAMPDIFF(SECOND, zpr.start_time, LEAST(NOW(), #{endTime})) /
                                        GREATEST(TIMESTAMPDIFF(SECOND, zpr.start_time, NOW()), 1)
                                    )
                                )
                            ELSE zpr.production_count
                        END
                    -- 进行中记录：开始时间在查询范围之前，按比例计算查询范围内的部分
                    WHEN zpr.end_time IS NULL AND zpr.start_time &lt; #{beginTime} THEN
                        CASE
                            WHEN TIMESTAMPDIFF(SECOND, zpr.start_time, NOW()) > 0 THEN
                                ROUND(
                                    zpr.production_count *
                                    (
                                        TIMESTAMPDIFF(SECOND, #{beginTime}, LEAST(NOW(), #{endTime})) /
                                        GREATEST(TIMESTAMPDIFF(SECOND, zpr.start_time, NOW()), 1)
                                    )
                                )
                            ELSE zpr.production_count
                        END
                    ELSE 0
                END
            ) as production_count
        FROM zh_device_production_record zpr
        WHERE 1=1
        <if test="productName != null and productName != ''">
            AND zpr.product_name LIKE CONCAT('%', #{productName}, '%')
        </if>
        AND (
            -- 已完成的记录：与查询时间范围有重叠
            (zpr.end_time IS NOT NULL AND zpr.start_time &lt; #{endTime} AND zpr.end_time &gt; #{beginTime}) OR
            -- 进行中的记录：开始时间在查询结束时间之前
            (zpr.end_time IS NULL AND zpr.start_time &lt; #{endTime})
        )
        GROUP BY zpr.product_name
        ORDER BY zpr.product_name
    </select>

    <!-- 查询产品数量展示界面数据（用于导出） -->
    <select id="selectProductQuantityForExport" resultType="com.ruoyi.zhenghe.domain.dto.ProductQuantityDto">
        SELECT
            zpr.product_name as productName,
            COUNT(DISTINCT zpr.device_code) as deviceCount,
            SUM(
                CASE
                    -- 已完成记录：完全在查询时间范围内
                    WHEN zpr.end_time IS NOT NULL AND zpr.start_time >= #{beginTime} AND zpr.end_time &lt;= #{endTime} THEN
                        zpr.production_count
                    -- 已完成记录：与查询时间范围有重叠，按时间比例计算
                    WHEN zpr.end_time IS NOT NULL AND zpr.start_time &lt; #{endTime} AND zpr.end_time &gt; #{beginTime} THEN
                        ROUND(
                            zpr.production_count *
                            (
                                TIMESTAMPDIFF(SECOND,
                                    GREATEST(zpr.start_time, #{beginTime}),
                                    LEAST(zpr.end_time, #{endTime})
                                ) /
                                TIMESTAMPDIFF(SECOND, zpr.start_time, zpr.end_time)
                            )
                        )
                    -- 进行中记录：开始时间在查询范围内，假设持续到查询结束时间
                    WHEN zpr.end_time IS NULL AND zpr.start_time >= #{beginTime} AND zpr.start_time &lt; #{endTime} THEN
                        ROUND(
                            zpr.production_count *
                            (
                                TIMESTAMPDIFF(SECOND, zpr.start_time, #{endTime}) /
                                TIMESTAMPDIFF(SECOND, zpr.start_time, NOW())
                            )
                        )
                    -- 进行中记录：开始时间在查询范围之前，按比例计算查询范围内的部分
                    WHEN zpr.end_time IS NULL AND zpr.start_time &lt; #{beginTime} THEN
                        ROUND(
                            zpr.production_count *
                            (
                                TIMESTAMPDIFF(SECOND, #{beginTime}, #{endTime}) /
                                TIMESTAMPDIFF(SECOND, zpr.start_time, NOW())
                            )
                        )
                    ELSE 0
                END
            ) as productionCount
        FROM zh_device_production_record zpr
        WHERE 1=1
        <if test="productName != null and productName != ''">
            AND zpr.product_name LIKE CONCAT('%', #{productName}, '%')
        </if>
        AND (
            -- 已完成的记录：与查询时间范围有重叠
            (zpr.end_time IS NOT NULL AND zpr.start_time &lt; #{endTime} AND zpr.end_time &gt; #{beginTime}) OR
            -- 进行中的记录：开始时间在查询结束时间之前
            (zpr.end_time IS NULL AND zpr.start_time &lt; #{endTime})
        )
        GROUP BY zpr.product_name
        ORDER BY zpr.product_name
    </select>

    <!-- 查询产品生产明细 -->
    <select id="selectProductDetail" resultMap="ZhDeviceProductionRecordResult">
        SELECT
            zpr.id,
            zpr.device_code,
            COALESCE(zie.equipment_name, '未知设备') as device_name,
            COALESCE(sd.dept_name, '未知事业部') as business_unit,
            COALESCE(zw.workshop_name, '未知车间') as workshop_name,
            zpr.product_name,
            -- 使用与productQuantity接口相同的时间比例计算逻辑
            CASE
                -- 已完成记录：完全在查询时间范围内
                WHEN zpr.end_time IS NOT NULL AND zpr.start_time >= #{beginTime} AND zpr.end_time &lt;= #{endTime} THEN
                    zpr.production_count
                -- 已完成记录：与查询时间范围有重叠，按时间比例计算
                WHEN zpr.end_time IS NOT NULL AND zpr.start_time &lt; #{endTime} AND zpr.end_time &gt; #{beginTime} THEN
                    CASE
                        WHEN TIMESTAMPDIFF(SECOND, zpr.start_time, zpr.end_time) > 0 THEN
                            ROUND(
                                zpr.production_count *
                                (
                                    TIMESTAMPDIFF(SECOND,
                                        GREATEST(zpr.start_time, #{beginTime}),
                                        LEAST(zpr.end_time, #{endTime})
                                    ) /
                                    TIMESTAMPDIFF(SECOND, zpr.start_time, zpr.end_time)
                                )
                            )
                        ELSE zpr.production_count  -- 如果时间差为0，返回原始数量
                    END
                -- 进行中记录：开始时间在查询范围内，假设持续到查询结束时间
                WHEN zpr.end_time IS NULL AND zpr.start_time >= #{beginTime} AND zpr.start_time &lt; #{endTime} THEN
                    CASE
                        WHEN TIMESTAMPDIFF(SECOND, zpr.start_time, LEAST(NOW(), #{endTime})) > 0 THEN
                            ROUND(
                                zpr.production_count *
                                (
                                    TIMESTAMPDIFF(SECOND, zpr.start_time, LEAST(NOW(), #{endTime})) /
                                    GREATEST(TIMESTAMPDIFF(SECOND, zpr.start_time, NOW()), 1)
                                )
                            )
                        ELSE zpr.production_count
                    END
                -- 进行中记录：开始时间在查询范围之前，按比例计算查询范围内的部分
                WHEN zpr.end_time IS NULL AND zpr.start_time &lt; #{beginTime} THEN
                    CASE
                        WHEN TIMESTAMPDIFF(SECOND, zpr.start_time, NOW()) > 0 THEN
                            ROUND(
                                zpr.production_count *
                                (
                                    TIMESTAMPDIFF(SECOND, #{beginTime}, LEAST(NOW(), #{endTime})) /
                                    GREATEST(TIMESTAMPDIFF(SECOND, zpr.start_time, NOW()), 1)
                                )
                            )
                        ELSE zpr.production_count
                    END
                ELSE 0
            END as production_count,
            zpr.production_count as original_production_count, -- 保留原始数量用于参考
            zpr.start_time,
            zpr.end_time,
            zpr.record_time,
            zpr.remark,
            #{beginTime} as query_begin_time, -- 查询开始时间
            #{endTime} as query_end_time      -- 查询结束时间
        FROM zh_device_production_record zpr
        LEFT JOIN zh_iot_equipment zie ON BINARY zpr.device_code = BINARY zie.equipment_code
        LEFT JOIN zh_workshop zw ON zie.workshop_id = zw.id
        LEFT JOIN sys_dept sd ON CAST(zie.dept_id AS CHAR) = CAST(sd.dept_id AS CHAR)
        WHERE zpr.product_name = #{productName}
        AND (
            (zpr.end_time IS NOT NULL AND zpr.start_time &lt; #{endTime} AND zpr.end_time &gt; #{beginTime}) OR
            (zpr.end_time IS NULL AND zpr.start_time &lt; #{endTime})
        )
        ORDER BY zpr.record_time DESC
    </select>

</mapper>
