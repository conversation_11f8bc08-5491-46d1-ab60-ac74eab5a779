package com.ruoyi.zhenghe.service;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhPeriod;

/**
 * 时段Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface IZhPeriodService 
{
    /**
     * 查询时段
     * 
     * @param id 时段主键
     * @return 时段
     */
    public ZhPeriod selectZhPeriodById(Long id);

    /**
     * 查询时段列表
     * 
     * @param zhPeriod 时段
     * @return 时段集合
     */
    public List<ZhPeriod> selectZhPeriodList(ZhPeriod zhPeriod);

    /**
     * 新增时段
     * 
     * @param zhPeriod 时段
     * @return 结果
     */
    public int insertZhPeriod(ZhPeriod zhPeriod);

    /**
     * 修改时段
     * 
     * @param zhPeriod 时段
     * @return 结果
     */
    public int updateZhPeriod(ZhPeriod zhPeriod);

    /**
     * 批量删除时段
     * 
     * @param ids 需要删除的时段主键集合
     * @return 结果
     */
    public int deleteZhPeriodByIds(Long[] ids);

    /**
     * 删除时段信息
     * 
     * @param id 时段主键
     * @return 结果
     */
    public int deleteZhPeriodById(Long id);
}
