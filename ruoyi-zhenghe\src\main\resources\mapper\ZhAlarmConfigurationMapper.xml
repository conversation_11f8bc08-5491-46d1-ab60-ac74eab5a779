<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhAlarmConfigurationMapper">

    <resultMap type="ZhAlarmConfiguration" id="ZhAlarmConfigurationResult">
        <result property="id"    column="id"    />
        <result property="equipmentId"    column="equipment_id"    />
        <result property="attrId"    column="attr_id"    />
        <result property="alarmLevelId"    column="alarm_level_id"    />
        <result property="oneAlarmOperator"    column="one_alarm_operator"    />
        <result property="oneAlarmValue"    column="one_alarm_value"    />
        <result property="twoAlarmOperator"    column="two_alarm_operator"    />
        <result property="twoAlarmValue"    column="two_alarm_value"    />
        <result property="alarmJudgeExpression"    column="alarm_judge_expression"    />
        <result property="alarmContent"    column="alarm_content"    />
        <result property="alarmSuggestion"    column="alarm_suggestion"    />
        <result property="remark"    column="remark"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="alarmJudgeEcho"    column="alarm_judge_echo"    />
    </resultMap>

    <sql id="selectZhAlarmConfigurationVo">
        select id, equipment_id, attr_id, alarm_level_id, one_alarm_operator, one_alarm_value, two_alarm_operator, two_alarm_value, alarm_judge_expression, alarm_content, alarm_suggestion, remark, dept_id, create_by, create_time, update_by, update_time from zh_alarm_configuration
    </sql>

    <sql id="selectZhAlarmConfigurationVoLian">
        select t1.*,t2.equipment_name as equipmentName,
        t3.attr_name as attrName,
        t4.alarm_name as alarmLevelName,
        t2.equipment_code as equipmentCode,
        t3.attr_code as attrCode,
        t3.attr_type as attrType
        from zh_alarm_configuration t1
        left join zh_iot_equipment t2 on t1.equipment_id = t2.id
        left join zh_device_type_attr t3 on t1.attr_id = t3.id
        left join zh_alarm_level t4 on  t1.alarm_level_id = t4.id
    </sql>

    <select id="selectZhAlarmConfigurationList" parameterType="ZhAlarmConfiguration" resultMap="ZhAlarmConfigurationResult">
        <include refid="selectZhAlarmConfigurationVoLian"/>
        <where>
            1 = 1
            <if test="equipmentId != null "> and t1.equipment_id = #{equipmentId}</if>
            <if test="attrId != null "> and t1.attr_id = #{attrId}</if>
            <if test="alarmLevelId != null "> and t1.alarm_level_id = #{alarmLevelId}</if>
            <if test="oneAlarmOperator != null  and oneAlarmOperator != ''"> and t1.one_alarm_operator = #{oneAlarmOperator}</if>
            <if test="oneAlarmValue != null "> and t1.one_alarm_value = #{oneAlarmValue}</if>
            <if test="twoAlarmOperator != null  and twoAlarmOperator != ''"> and t1.two_alarm_operator = #{twoAlarmOperator}</if>
            <if test="twoAlarmValue != null "> and t1.two_alarm_value = #{twoAlarmValue}</if>
            <if test="alarmJudgeExpression != null  and alarmJudgeExpression != ''"> and t1.alarm_judge_expression = #{alarmJudgeExpression}</if>
            <if test="alarmContent != null  and alarmContent != ''"> and t1.alarm_content = #{alarmContent}</if>
            <if test="alarmSuggestion != null  and alarmSuggestion != ''"> and t1.alarm_suggestion = #{alarmSuggestion}</if>
            <if test="deptId != null "> and t1.dept_id = #{deptId}</if>
            <if test="equipmentName != null  and equipmentName != ''"> and t2.equipment_name like concat('%', #{equipmentName}, '%')</if>
        </where>
        ${params.dataScope}
        order by
        t1.create_time
        desc
    </select>

    <select id="selectZhAlarmConfigurationById" parameterType="Long" resultMap="ZhAlarmConfigurationResult">
        <include refid="selectZhAlarmConfigurationVoLian"/>
        where t1.id = #{id}
    </select>

    <insert id="insertZhAlarmConfiguration" parameterType="ZhAlarmConfiguration" useGeneratedKeys="true" keyProperty="id">
        insert into zh_alarm_configuration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipmentId != null">equipment_id,</if>
            <if test="attrId != null">attr_id,</if>
            <if test="alarmLevelId != null">alarm_level_id,</if>
            <if test="oneAlarmOperator != null">one_alarm_operator,</if>
            <if test="oneAlarmValue != null">one_alarm_value,</if>
            <if test="twoAlarmOperator != null">two_alarm_operator,</if>
            <if test="twoAlarmValue != null">two_alarm_value,</if>
            <if test="alarmJudgeExpression != null">alarm_judge_expression,</if>
            <if test="alarmContent != null">alarm_content,</if>
            <if test="alarmSuggestion != null">alarm_suggestion,</if>
            <if test="remark != null">remark,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="alarmJudgeEcho != null">alarm_judge_echo,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="equipmentId != null">#{equipmentId},</if>
            <if test="attrId != null">#{attrId},</if>
            <if test="alarmLevelId != null">#{alarmLevelId},</if>
            <if test="oneAlarmOperator != null">#{oneAlarmOperator},</if>
            <if test="oneAlarmValue != null">#{oneAlarmValue},</if>
            <if test="twoAlarmOperator != null">#{twoAlarmOperator},</if>
            <if test="twoAlarmValue != null">#{twoAlarmValue},</if>
            <if test="alarmJudgeExpression != null">#{alarmJudgeExpression},</if>
            <if test="alarmContent != null">#{alarmContent},</if>
            <if test="alarmSuggestion != null">#{alarmSuggestion},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="alarmJudgeEcho != null">#{alarmJudgeEcho},</if>
        </trim>
    </insert>

    <update id="updateZhAlarmConfiguration" parameterType="ZhAlarmConfiguration">
        update zh_alarm_configuration
        <trim prefix="SET" suffixOverrides=",">
            <if test="equipmentId != null">equipment_id = #{equipmentId},</if>
            <if test="attrId != null">attr_id = #{attrId},</if>
            <if test="alarmLevelId != null">alarm_level_id = #{alarmLevelId},</if>
            <if test="oneAlarmOperator != null">one_alarm_operator = #{oneAlarmOperator},</if>
            <if test="oneAlarmValue != null">one_alarm_value = #{oneAlarmValue},</if>
            <if test="twoAlarmOperator != null">two_alarm_operator = #{twoAlarmOperator},</if>
            <if test="twoAlarmValue != null">two_alarm_value = #{twoAlarmValue},</if>
            <if test="alarmJudgeExpression != null">alarm_judge_expression = #{alarmJudgeExpression},</if>
            <if test="alarmContent != null">alarm_content = #{alarmContent},</if>
            <if test="alarmSuggestion != null">alarm_suggestion = #{alarmSuggestion},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="alarmJudgeEcho != null">alarm_judge_echo = #{alarmJudgeEcho},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhAlarmConfigurationById" parameterType="Long">
        delete from zh_alarm_configuration where id = #{id}
    </delete>

    <delete id="deleteZhAlarmConfigurationByIds" parameterType="String">
        delete from zh_alarm_configuration where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectZhAlarmConfigurationByEquipmentId" parameterType="long" resultMap="ZhAlarmConfigurationResult">
        <include refid="selectZhAlarmConfigurationVoLian"/>
        where t1.equipment_id = #{equipmentId}
    </select>
</mapper>