package com.ruoyi.zhenghe.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 瑕疵细分响应数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
public class DefectDetailResponseDto {

    /** 瑕疵名称 */
    @ApiModelProperty(value = "瑕疵名称")
    private String defectName;

    /** 瑕疵数量 */
    @ApiModelProperty(value = "瑕疵数量")
    private Integer defectCount;

    /** 瑕疵图片 */
    @ApiModelProperty(value = "瑕疵图片URL")
    private String defectImage;

    public DefectDetailResponseDto() {
    }

    public DefectDetailResponseDto(String defectName, Integer defectCount) {
        this.defectName = defectName;
        this.defectCount = defectCount;
    }

    public DefectDetailResponseDto(String defectName, Integer defectCount, String defectImage) {
        this.defectName = defectName;
        this.defectCount = defectCount;
        this.defectImage = defectImage;
    }
}
