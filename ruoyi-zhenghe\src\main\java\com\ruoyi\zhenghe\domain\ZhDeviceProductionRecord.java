package com.ruoyi.zhenghe.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 设备产品生产记录对象 zh_device_production_record
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@ApiModel(value = "ZhDeviceProductionRecord", description = "设备产品生产记录对象")
public class ZhDeviceProductionRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 设备编码 */
    @Excel(name = "设备编码", sort = 1)
    @ApiModelProperty(value = "设备编码")
    private String deviceCode;

    /** 产品名称 */
    @Excel(name = "产品编号", sort = 2)
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /** 累计产量 */
    @Excel(name = "生产设备数量", sort = 3)
    @ApiModelProperty(value = "累计产量")
    private Integer cumulativeCount;

    /** 本次生产数量 */
    @Excel(name = "生产产品计数", sort = 4)
    @ApiModelProperty(value = "本次生产数量")
    private Integer productionCount;

    /** 原始生产数量（用于明细查询时显示计算前的原始值） */
    @ApiModelProperty(value = "原始生产数量")
    private Integer originalProductionCount;

    /** 查询开始时间（用于明细查询时显示查询的时间范围） */
    @ApiModelProperty(value = "查询开始时间")
    private Date queryBeginTime;

    /** 查询结束时间（用于明细查询时显示查询的时间范围） */
    @ApiModelProperty(value = "查询结束时间")
    private Date queryEndTime;

    /** 生产开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生产开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 5)
    @ApiModelProperty(value = "生产开始时间")
    private Date startTime;

    /** 生产结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生产结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 6)
    @ApiModelProperty(value = "生产结束时间")
    private Date endTime;

    /** 生产时长(分钟) */
    @Excel(name = "生产时长(分钟)", sort = 7)
    @ApiModelProperty(value = "生产时长(分钟)")
    private Integer productionDuration;

    /** 状态（0-结束，1-进行中） */
    @Excel(name = "状态", sort = 8, readConverterExp = "0=结束,1=进行中")
    @ApiModelProperty(value = "状态（0-结束，1-进行中）")
    private Integer status;

    /** 记录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "记录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 9)
    @ApiModelProperty(value = "记录时间")
    private Date recordTime;

    /** 设备名称 */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    /** 所属事业部 */
    @ApiModelProperty(value = "所属事业部")
    private String businessUnit;

    /** 所属车间 */
    @ApiModelProperty(value = "所属车间")
    private String workshopName;

}
