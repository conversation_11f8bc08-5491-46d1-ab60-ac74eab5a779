package com.ruoyi.zhenghe.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 尖峰平谷时段维护对象 zh_period_maintain
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
@ApiModel(value = "ZhPeriodMaintain",description = "尖峰平谷时段维护对象")
public class ZhPeriodMaintain extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 年 */
    @ApiModelProperty(value = "年")
    @Excel(name = "年",sort = 1)
    private String yearTime;

    /** 时段 从0开始 0为00:00 - 01:00 */
    @ApiModelProperty(value = "时段")
    @Excel(name = "时段 从1开始 1为00:00 - 01:00",sort = 2)
    private Long period;

    /** 一月时段名称 */
    @ApiModelProperty(value = "一月时段名称")
    @Excel(name = "一月时段名称",sort = 3)
    private String onePeriodName;

    /** 一月时段颜色 */
    @ApiModelProperty(value = "一月时段颜色")
    //@Excel(name = "一月时段颜色",sort = 4)
    private String onePeriodColor;

    /** 二月时段名称 */
    @ApiModelProperty(value = "二月时段名称")
    @Excel(name = "二月时段名称",sort = 5)
    private String twoPeriodName;

    /** 二月时段颜色 */
    @ApiModelProperty(value = "二月时段颜色")
    //@Excel(name = "二月时段颜色",sort = 6)
    private String twoPeriodColor;

    /** 三月时段名称 */
    @ApiModelProperty(value = "三月时段名称")
    @Excel(name = "三月时段名称",sort = 7)
    private String threePeriodName;

    /** 三月时段颜色 */
    @ApiModelProperty(value = "三月时段颜色")
    //@Excel(name = "三月时段颜色",sort = 8)
    private String threePeriodColor;

    /** 四月时段名称 */
    @ApiModelProperty(value = "四月时段名称")
    @Excel(name = "四月时段名称",sort = 9)
    private String fourPeriodName;

    /** 四月时段颜色 */
    @ApiModelProperty(value = "四月时段颜色")
    //@Excel(name = "四月时段颜色",sort = 10)
    private String fourPeriodColor;

    /** 五月时段名称 */
    @ApiModelProperty(value = "五月时段名称")
    @Excel(name = "五月时段名称",sort = 11)
    private String fivePeriodName;

    /** 五月时段颜色 */
    @ApiModelProperty(value = "五月时段颜色")
    //@Excel(name = "五月时段颜色",sort = 12)
    private String fivePeriodColor;

    /** 六月时段名称 */
    @ApiModelProperty(value = "六月时段名称")
    @Excel(name = "六月时段名称",sort = 13)
    private String sixPeriodName;

    /** 六月时段颜色 */
    @ApiModelProperty(value = "六月时段颜色")
    //@Excel(name = "六月时段颜色",sort = 14)
    private String sixPeriodColor;

    /** 七月时段名称 */
    @ApiModelProperty(value = "七月时段名称")
    @Excel(name = "七月时段名称",sort = 15)
    private String sevenPeriodName;

    /** 七月时段颜色 */
    @ApiModelProperty(value = "七月时段颜色")
    //@Excel(name = "七月时段颜色",sort = 16)
    private String sevenPeriodColor;

    /** 八月时段名称 */
    @ApiModelProperty(value = "八月时段名称")
    @Excel(name = "八月时段名称",sort = 17)
    private String eightPeriodName;

    /** 八月时段颜色 */
    @ApiModelProperty(value = "八月时段颜色")
    //@Excel(name = "八月时段颜色",sort = 18)
    private String eightPeriodColor;

    /** 九月时段名称 */
    @ApiModelProperty(value = "九月时段名称")
    @Excel(name = "九月时段名称",sort = 19)
    private String ninePeriodName;

    /** 九月时段颜色 */
    @ApiModelProperty(value = "九月时段颜色")
    //@Excel(name = "九月时段颜色",sort = 20)
    private String ninePeriodColor;

    /** 十月时段名称 */
    @ApiModelProperty(value = "十月时段名称")
    @Excel(name = "十月时段名称",sort = 21)
    private String tenPeriodName;

    /** 十月时段颜色 */
    @ApiModelProperty(value = "十月时段颜色")
    //@Excel(name = "十月时段颜色",sort = 22)
    private String tenPeriodColor;

    /** 十一月时段名称 */
    @ApiModelProperty(value = "十一月时段名称")
    @Excel(name = "十一月时段名称",sort = 23)
    private String elevenPeriodName;

    /** 十一月时段颜色 */
    @ApiModelProperty(value = "十一月时段颜色")
    //@Excel(name = "十一月时段颜色",sort = 24)
    private String elevenPeriodColor;

    /** 十二月时段名称 */
    @ApiModelProperty(value = "十二月时段名称")
    @Excel(name = "十二月时段名称",sort = 25)
    private String twelvePeriodName;

    /** 十二月时段颜色 */
    @ApiModelProperty(value = "十二月时段颜色")
    //@Excel(name = "十二月时段颜色",sort = 26)
    private String twelvePeriodColor;

    /** 部门id */
    //@Excel(name = "部门id")
    private Long deptId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setYearTime(String yearTime) 
    {
        this.yearTime = yearTime;
    }

    public String getYearTime() 
    {
        return yearTime;
    }

    public void setPeriod(Long period) 
    {
        this.period = period;
    }

    public Long getPeriod() 
    {
        return period;
    }

    public void setOnePeriodName(String onePeriodName) 
    {
        this.onePeriodName = onePeriodName;
    }

    public String getOnePeriodName() 
    {
        return onePeriodName;
    }

    public void setOnePeriodColor(String onePeriodColor) 
    {
        this.onePeriodColor = onePeriodColor;
    }

    public String getOnePeriodColor() 
    {
        return onePeriodColor;
    }

    public void setTwoPeriodName(String twoPeriodName) 
    {
        this.twoPeriodName = twoPeriodName;
    }

    public String getTwoPeriodName() 
    {
        return twoPeriodName;
    }

    public void setTwoPeriodColor(String twoPeriodColor) 
    {
        this.twoPeriodColor = twoPeriodColor;
    }

    public String getTwoPeriodColor() 
    {
        return twoPeriodColor;
    }

    public void setThreePeriodName(String threePeriodName) 
    {
        this.threePeriodName = threePeriodName;
    }

    public String getThreePeriodName() 
    {
        return threePeriodName;
    }

    public void setThreePeriodColor(String threePeriodColor) 
    {
        this.threePeriodColor = threePeriodColor;
    }

    public String getThreePeriodColor() 
    {
        return threePeriodColor;
    }

    public void setFourPeriodName(String fourPeriodName) 
    {
        this.fourPeriodName = fourPeriodName;
    }

    public String getFourPeriodName() 
    {
        return fourPeriodName;
    }

    public void setFourPeriodColor(String fourPeriodColor) 
    {
        this.fourPeriodColor = fourPeriodColor;
    }

    public String getFourPeriodColor() 
    {
        return fourPeriodColor;
    }

    public void setFivePeriodName(String fivePeriodName) 
    {
        this.fivePeriodName = fivePeriodName;
    }

    public String getFivePeriodName() 
    {
        return fivePeriodName;
    }

    public void setFivePeriodColor(String fivePeriodColor) 
    {
        this.fivePeriodColor = fivePeriodColor;
    }

    public String getFivePeriodColor() 
    {
        return fivePeriodColor;
    }

    public void setSixPeriodName(String sixPeriodName) 
    {
        this.sixPeriodName = sixPeriodName;
    }

    public String getSixPeriodName() 
    {
        return sixPeriodName;
    }

    public void setSixPeriodColor(String sixPeriodColor) 
    {
        this.sixPeriodColor = sixPeriodColor;
    }

    public String getSixPeriodColor() 
    {
        return sixPeriodColor;
    }

    public void setSevenPeriodName(String sevenPeriodName) 
    {
        this.sevenPeriodName = sevenPeriodName;
    }

    public String getSevenPeriodName() 
    {
        return sevenPeriodName;
    }

    public void setSevenPeriodColor(String sevenPeriodColor) 
    {
        this.sevenPeriodColor = sevenPeriodColor;
    }

    public String getSevenPeriodColor() 
    {
        return sevenPeriodColor;
    }

    public void setEightPeriodName(String eightPeriodName) 
    {
        this.eightPeriodName = eightPeriodName;
    }

    public String getEightPeriodName() 
    {
        return eightPeriodName;
    }

    public void setEightPeriodColor(String eightPeriodColor) 
    {
        this.eightPeriodColor = eightPeriodColor;
    }

    public String getEightPeriodColor() 
    {
        return eightPeriodColor;
    }

    public void setNinePeriodName(String ninePeriodName) 
    {
        this.ninePeriodName = ninePeriodName;
    }

    public String getNinePeriodName() 
    {
        return ninePeriodName;
    }

    public void setNinePeriodColor(String ninePeriodColor) 
    {
        this.ninePeriodColor = ninePeriodColor;
    }

    public String getNinePeriodColor() 
    {
        return ninePeriodColor;
    }

    public void setTenPeriodName(String tenPeriodName) 
    {
        this.tenPeriodName = tenPeriodName;
    }

    public String getTenPeriodName() 
    {
        return tenPeriodName;
    }

    public void setTenPeriodColor(String tenPeriodColor) 
    {
        this.tenPeriodColor = tenPeriodColor;
    }

    public String getTenPeriodColor() 
    {
        return tenPeriodColor;
    }

    public void setElevenPeriodName(String elevenPeriodName) 
    {
        this.elevenPeriodName = elevenPeriodName;
    }

    public String getElevenPeriodName() 
    {
        return elevenPeriodName;
    }

    public void setElevenPeriodColor(String elevenPeriodColor) 
    {
        this.elevenPeriodColor = elevenPeriodColor;
    }

    public String getElevenPeriodColor() 
    {
        return elevenPeriodColor;
    }

    public void setTwelvePeriodName(String twelvePeriodName) 
    {
        this.twelvePeriodName = twelvePeriodName;
    }

    public String getTwelvePeriodName() 
    {
        return twelvePeriodName;
    }

    public void setTwelvePeriodColor(String twelvePeriodColor) 
    {
        this.twelvePeriodColor = twelvePeriodColor;
    }

    public String getTwelvePeriodColor() 
    {
        return twelvePeriodColor;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("yearTime", getYearTime())
            .append("period", getPeriod())
            .append("onePeriodName", getOnePeriodName())
            .append("onePeriodColor", getOnePeriodColor())
            .append("twoPeriodName", getTwoPeriodName())
            .append("twoPeriodColor", getTwoPeriodColor())
            .append("threePeriodName", getThreePeriodName())
            .append("threePeriodColor", getThreePeriodColor())
            .append("fourPeriodName", getFourPeriodName())
            .append("fourPeriodColor", getFourPeriodColor())
            .append("fivePeriodName", getFivePeriodName())
            .append("fivePeriodColor", getFivePeriodColor())
            .append("sixPeriodName", getSixPeriodName())
            .append("sixPeriodColor", getSixPeriodColor())
            .append("sevenPeriodName", getSevenPeriodName())
            .append("sevenPeriodColor", getSevenPeriodColor())
            .append("eightPeriodName", getEightPeriodName())
            .append("eightPeriodColor", getEightPeriodColor())
            .append("ninePeriodName", getNinePeriodName())
            .append("ninePeriodColor", getNinePeriodColor())
            .append("tenPeriodName", getTenPeriodName())
            .append("tenPeriodColor", getTenPeriodColor())
            .append("elevenPeriodName", getElevenPeriodName())
            .append("elevenPeriodColor", getElevenPeriodColor())
            .append("twelvePeriodName", getTwelvePeriodName())
            .append("twelvePeriodColor", getTwelvePeriodColor())
            .append("deptId", getDeptId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
