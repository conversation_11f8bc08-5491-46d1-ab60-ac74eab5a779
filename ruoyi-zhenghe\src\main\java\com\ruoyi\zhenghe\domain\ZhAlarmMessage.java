package com.ruoyi.zhenghe.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 告警信息对象 zh_alarm_message
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
public class ZhAlarmMessage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 告警配置id */
    //@Excel(name = "告警配置id")
    private Long alarmConfigurationId;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss",sort = 6)
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss",sort = 7)
    private Date endTime;

    /** 持续时间 */
    //@Excel(name = "持续时间")
    private Long sustainTime;

    /** 部门id */
    @Excel(name = "部门id")
    private Long deptId;

    /** 部门名称  查询用*/
    @Excel(name = "事业部",sort = 3)
    private String deptName;

    /** 车间名称  查询用*/
    @Excel(name = "车间",sort = 4)
    private String workshopName;

    /** 车间id  查询用*/
    private String workshopId;

    /** 设备类型名称  查询用*/
    @Excel(name = "设备类型",sort = 5)
    private String deviceTypeName;

    /** 设备类型id  查询用*/
    private Integer deviceTypeId;

    /** 设备类型名称  查询用*/
    @Excel(name = "设备名称",sort = 1)
    private String equipmentName;

    /** 设备类型编码 查询用*/
    @Excel(name = "设备编码",sort = 2)
    private String equipmentCode;

    /** 告警等级名称 查询用*/
    @Excel(name = "告警等级",sort = 8)
    private String alarmName;

    /** 告警内容 查询用*/
    @ApiModelProperty(value = "告警内容")
    @Excel(name = "告警内容",sort = 9)
    private String alarmContent;

    /** 处理建议 查询用*/
    @ApiModelProperty(value = "处理建议")
    @Excel(name = "处理建议",sort = 10)
    private String alarmSuggestion;

    public String getWorkshopId() {
        return workshopId;
    }

    public void setWorkshopId(String workshopId) {
        this.workshopId = workshopId;
    }

    public String getDeviceTypeName() {
        return deviceTypeName;
    }

    public void setDeviceTypeName(String deviceTypeName) {
        this.deviceTypeName = deviceTypeName;
    }

    public String getEquipmentName() {
        return equipmentName;
    }

    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    public String getEquipmentCode() {
        return equipmentCode;
    }

    public void setEquipmentCode(String equipmentCode) {
        this.equipmentCode = equipmentCode;
    }

    public String getAlarmName() {
        return alarmName;
    }

    public void setAlarmName(String alarmName) {
        this.alarmName = alarmName;
    }

    public String getAlarmContent() {
        return alarmContent;
    }

    public void setAlarmContent(String alarmContent) {
        this.alarmContent = alarmContent;
    }

    public String getAlarmSuggestion() {
        return alarmSuggestion;
    }

    public void setAlarmSuggestion(String alarmSuggestion) {
        this.alarmSuggestion = alarmSuggestion;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getWorkshopName() {
        return workshopName;
    }

    public void setWorkshopName(String workshopName) {
        this.workshopName = workshopName;
    }

    public Integer getDeviceTypeId() {
        return deviceTypeId;
    }

    public void setDeviceTypeId(Integer deviceTypeId) {
        this.deviceTypeId = deviceTypeId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setAlarmConfigurationId(Long alarmConfigurationId) 
    {
        this.alarmConfigurationId = alarmConfigurationId;
    }

    public Long getAlarmConfigurationId() 
    {
        return alarmConfigurationId;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }

    public void setSustainTime(Long sustainTime) 
    {
        this.sustainTime = sustainTime;
    }

    public Long getSustainTime() 
    {
        return sustainTime;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("alarmConfigurationId", getAlarmConfigurationId())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("sustainTime", getSustainTime())
            .append("deptId", getDeptId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
