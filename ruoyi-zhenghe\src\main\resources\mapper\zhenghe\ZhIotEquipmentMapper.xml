<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhIotEquipmentMapper">

    <resultMap type="ZhIotEquipment" id="ZhIotEquipmentResult">
        <result property="id" column="id"/>
        <result property="equipmentName" column="equipment_name"/>
        <result property="equipmentCode" column="equipment_code"/>
        <result property="deviceTypeId" column="device_type_id"/>
        <result property="deviceType" column="device_type"/>
        <result property="equipmentImg" column="equipment_img"/>
        <result property="equipmentSort" column="equipment_sort"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="openId" column="open_id"/>
        <result property="workshopId" column="workshop_id"/>
        <result property="workshopName" column="workshop_name"/>
        <result property="process" column="process"/>
        <result property="processId" column="process_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="ex1" column="ex1"/>
        <result property="ex2" column="ex2"/>
        <result property="gatewayCode" column="gateway_code"/>
        <result property="eamCode" column="eam_code"/>
        <result property="theoreticalYield" column="theoretical_yield"/>
        <result property="remark" column="remark"/>
    </resultMap>


    <resultMap type="EquipmentDetail" id="ZhIotEquipmentRealDataResult">
        <result property="id" column="id"/>
        <result property="equipmentName" column="equipment_name"/>
        <result property="equipmentCode" column="equipment_code"/>
        <result property="deviceTypeId" column="device_type_id"/>
        <result property="deviceType" column="device_type"/>
        <result property="equipmentImg" column="equipment_img"/>
        <result property="equipmentSort" column="equipment_sort"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="openId" column="open_id"/>
        <result property="workshopId" column="workshop_id"/>
        <result property="workshopName" column="workshop_name"/>
        <result property="process" column="process"/>
        <result property="processId" column="process_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="ex1" column="ex1"/>
        <result property="ex2" column="ex2"/>
        <result property="gatewayCode" column="gateway_code"/>
        <result property="eamCode" column="eam_code"/>
        <result property="theoreticalYield" column="theoretical_yield"/>
        <result property="remark" column="remark"/>
        <association property="attrList" resultMap="ZhEquipmentPropResult"></association>
    </resultMap>
    <resultMap type="ZhEquipmentProp" id="ZhEquipmentPropResult">
        <result property="id" column="id"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="attrId" column="attr_id"/>
        <result property="attrName" column="attr_name"/>
        <result property="attrCode" column="attr_code"/>
        <result property="faultVal" column="fault_val"/>
        <result property="minVal" column="min_val"/>
        <result property="maxVal" column="max_val"/>
        <result property="enumList" column="enum_list"/>
        <result property="showType" column="show_type"/>
        <result property="autoFault" column="auto_fault"/>
        <result property="faultTime" column="fault_time"/>
        <result property="autoBill" column="auto_bill"/>
        <result property="billTime" column="bill_time"/>
        <result property="billUser" column="bill_user"/>
        <result property="lastVal" column="lastVal"></result>
        <result property="lastUpdateTime" column="lastUpdateTime"></result>
        <result property="customMultiple" column="custom_multiple"/>
    </resultMap>

    <sql id="selectZhIotEquipmentVo">
        SELECT d.id,
               d.equipment_name,
               d.equipment_code,
               d.device_type_id,
               d.equipment_img,
               d.equipment_sort,
               d.create_user_id,
               d.dept_id,
               d.open_id,
               d.workshop_id,
               d.process_id,
               d.create_by,
               d.create_time,
               d.update_by,
               d.update_time,
               d.ex1,
               d.ex2,
               d.gateway_code,
               d.eam_code,
               d.theoretical_yield,
               d.remark,
               d1.tsl_name     AS device_type,
               ws.workshop_name,
               sd.dept_name,
               bp.process_name AS process
        FROM zh_iot_equipment d
                 LEFT JOIN zh_device_type d1 ON d.device_type_id = d1.id
                 LEFT JOIN zh_workshop ws ON d.workshop_id = ws.id
                 LEFT JOIN sys_dept sd ON d.dept_id = sd.dept_id
                 LEFT JOIN zh_base_process bp ON d.process_id = bp.id
    </sql>

    <select id="selectZhIotEquipmentList" parameterType="ZhIotEquipment" resultMap="ZhIotEquipmentResult">
        <include refid="selectZhIotEquipmentVo"/>

        WHERE 1 = 1
        <if test="id != null ">and d.id = #{id}</if>
        <if test="equipmentName != null  and equipmentName != ''">and equipment_name like concat('%',
            #{equipmentName}, '%')
        </if>
        <if test="equipmentCode != null  and equipmentCode != ''">and d.equipment_code = #{equipmentCode}</if>
        <if test="deviceTypeId != null ">and d.device_type_id = #{deviceTypeId}</if>
        <if test="equipmentSort != null ">and d.equipment_sort = #{equipmentSort}</if>
        <if test="createUserId != null ">and d.create_user_id = #{createUserId}</if>
        <if test="deptId != null  and deptId != ''">and d.dept_id = #{deptId}</if>
        <if test="openId != null  and openId != ''">and d.open_id = #{openId}</if>
        <if test="workshopId != null ">and d.workshop_id = #{workshopId}</if>
        <if test="processId != null ">and d.process_id = #{processId}</if>
        <if test="ex1 != null  and ex1 != ''">and d.ex1 = #{ex1}</if>
        <if test="ex2 != null  and ex2 != ''">and d.ex2 = #{ex2}</if>
        <if test="remark != null  and remark != ''">and d.remark like concat('%', #{remark}, '%')</if>
        ${params.dataScope}
        order by
        d.id desc
    </select>
    <select id="selectZhIotEquipmentListNoScope" parameterType="ZhIotEquipment" resultMap="ZhIotEquipmentResult">
        <include refid="selectZhIotEquipmentVo"/>

        WHERE 1 = 1
        <if test="id != null ">and d.id = #{id}</if>
        <if test="equipmentName != null  and equipmentName != ''">and equipment_name like concat('%',
            #{equipmentName}, '%')
        </if>
        <if test="equipmentCode != null  and equipmentCode != ''">and d.equipment_code = #{equipmentCode}</if>
        <if test="deviceTypeId != null ">and d.device_type_id = #{deviceTypeId}</if>
        <if test="equipmentSort != null ">and d.equipment_sort = #{equipmentSort}</if>
        <if test="createUserId != null ">and d.create_user_id = #{createUserId}</if>
        <if test="deptId != null  and deptId != ''">and d.dept_id = #{deptId}</if>
        <if test="openId != null  and openId != ''">and d.open_id = #{openId}</if>
        <if test="workshopId != null ">and d.workshop_id = #{workshopId}</if>
        <if test="processId != null ">and d.process_id = #{processId}</if>
        <if test="ex1 != null  and ex1 != ''">and d.ex1 = #{ex1}</if>
        <if test="ex2 != null  and ex2 != ''">and d.ex2 = #{ex2}</if>
        <if test="remark != null  and remark != ''">and d.remark like concat('%', #{remark}, '%')</if>
        ${params.dataScope}
        order by
        d.create_time desc
    </select>

    <select id="selectZhIotEquipmentById" parameterType="Long" resultMap="ZhIotEquipmentResult">
        <include refid="selectZhIotEquipmentVo"/>
        where d.id = #{id}
    </select>

    <!--    获取设备最新属性-->
    <select id="selectZhIotEquipmentDetailById" resultMap="ZhIotEquipmentRealDataResult">
        SELECT
            t1.*,
            t2.attr_name,
            t2.id AS attr_id,
            t2.attr_code,
            t2.attr_desc,
            t2.attr_unit,
            t2.attr_order,
            t2.attr_type,
            t2.attr_class,
            l2.min_val AS minVal,
            l2.max_val AS maxVal,
            l2.enum_list,
            l2.fault_val,
            l2.show_type AS showType,
            m2.val AS lastVal,
            m2.update_time AS lastUpdateTime
        FROM zh_iot_equipment t1
                 LEFT JOIN zh_device_type_attr t2
                           ON t1.device_type_id = t2.tsl_id
                 LEFT JOIN zh_equipment_prop l2
                           ON t1.id = l2.equipment_id
                               AND t2.id = l2.attr_id
                 LEFT JOIN iot_real_data m2
                           ON t1.equipment_code = m2.device_code
                               AND t2.attr_code = REPLACE(REPLACE(m2.tag, '\t', ''), ' ', '')
        WHERE t1.id = #{id}
        ORDER BY
            CASE WHEN t1.equipment_sort IS NULL THEN 1 ELSE 0 END,
            t1.equipment_sort,
            t1.id,
            t2.attr_order ASC
    </select>

    <insert id="insertZhIotEquipment" parameterType="ZhIotEquipment" useGeneratedKeys="true" keyProperty="id">
        insert into zh_iot_equipment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipmentName != null">equipment_name,</if>
            <if test="equipmentCode != null">equipment_code,</if>
            <if test="deviceTypeId != null">device_type_id,</if>
            <if test="equipmentImg != null">equipment_img,</if>
            <if test="equipmentSort != null">equipment_sort,</if>
            <if test="createUserId != null">create_user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="openId != null">open_id,</if>
            <if test="workshopId != null">workshop_id,</if>
            <if test="processId != null">process_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="ex1 != null">ex1,</if>
            <if test="ex2 != null">ex2,</if>
            <if test="gatewayCode != null">gateway_code,</if>
            <if test="eamCode != null">eam_code,</if>
            <if test="theoreticalYield != null">theoretical_yield,</if>
            <if test="remark != null">remark,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="equipmentName != null">#{equipmentName},</if>
            <if test="equipmentCode != null">#{equipmentCode},</if>
            <if test="deviceTypeId != null">#{deviceTypeId},</if>
            <if test="equipmentImg != null">#{equipmentImg},</if>
            <if test="equipmentSort != null">#{equipmentSort},</if>
            <if test="createUserId != null">#{createUserId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="openId != null">#{openId},</if>
            <if test="workshopId != null">#{workshopId},</if>
            <if test="processId != null">#{processId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="ex1 != null">#{ex1},</if>
            <if test="ex2 != null">#{ex2},</if>
            <if test="gatewayCode != null">#{gatewayCode},</if>
            <if test="eamCode != null">#{eamCode},</if>
            <if test="theoreticalYield != null">#{theoreticalYield},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateZhIotEquipment" parameterType="ZhIotEquipment">
        update zh_iot_equipment
        <trim prefix="SET" suffixOverrides=",">
            <if test="equipmentName != null">equipment_name = #{equipmentName},</if>
            <if test="equipmentCode != null">equipment_code = #{equipmentCode},</if>
            <if test="deviceTypeId != null">device_type_id = #{deviceTypeId},</if>
            <if test="equipmentImg != null">equipment_img = #{equipmentImg},</if>
            <if test="equipmentSort != null">equipment_sort = #{equipmentSort},</if>
            <if test="createUserId != null">create_user_id = #{createUserId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="workshopId != null">workshop_id = #{workshopId},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="ex1 != null">ex1 = #{ex1},</if>
            <if test="ex2 != null">ex2 = #{ex2},</if>
            <if test="gatewayCode != null">gateway_code = #{gatewayCode},</if>
            <if test="eamCode != null">eam_code = #{eamCode},</if>
            <if test="theoreticalYield != null">theoretical_yield = #{theoreticalYield},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhIotEquipmentById" parameterType="Long">
        delete
        from zh_iot_equipment
        where id = #{id}
    </delete>

    <delete id="deleteZhIotEquipmentByIds" parameterType="String">
        delete from zh_iot_equipment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectZhIotEquipmentByEquipmentCode" parameterType="string" resultMap="ZhIotEquipmentResult">
        <include refid="selectZhIotEquipmentVo"/>
        where d.equipment_code = #{equipmentCode}
    </select>


    <select id="selectZhIotEquipmentList2" parameterType="ZhIotEquipmentQuery" resultMap="ZhIotEquipmentResult">
        <include refid="selectZhIotEquipmentVo"/>

        WHERE 1 = 1
        <if test="deptId != null and deptId.length > 0">
            and d.dept_id in
            <foreach collection="deptId" item="departmentId" open="(" separator="," close=")">
                #{departmentId}
            </foreach>
        </if>
        <if test="workshopId != null and workshopId.length > 0">
            and d.workshop_id in
            <foreach collection="workshopId" item="workshopId" open="(" separator="," close=")">
                #{workshopId}
            </foreach>
        </if>
        <if test="deviceTypeId != null and deviceTypeId.length > 0">
            and d.device_type_id in
            <foreach collection="deviceTypeId" item="deviceTypeId" open="(" separator="," close=")">
                #{deviceTypeId}
            </foreach>
        </if>
        <if test="processId != null and processId.length > 0">
            and d.process_id in
            <foreach collection="processId" item="processId" open="(" separator="," close=")">
                #{processId}
            </foreach>
        </if>
        <if test="equipmentName != null and equipmentName != ''">
            and d.equipment_name like concat('%',#{equipmentName},'%')
        </if>
        <if test="params != null and params.dataScope != null">
            ${params.dataScope}
        </if>
        order by d.create_time desc
    </select>

</mapper>