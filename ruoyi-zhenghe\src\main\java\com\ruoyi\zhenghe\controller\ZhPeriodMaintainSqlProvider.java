package com.ruoyi.zhenghe.controller;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

import java.util.Map;

public class ZhPeriodMaintainSqlProvider {
    public String getPeriodMaintainSqlProvider(@Param("filedName") String filedName,@Param("yearTime") String yearTime,
                                               @Param("period") String period){
        return "select "+filedName +" from zh_period_maintain where year_time ="+yearTime+" and period ="+period;
    }
}
