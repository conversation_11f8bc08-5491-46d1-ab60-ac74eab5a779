package com.ruoyi.zhenghe.mapper;

import com.ruoyi.zhenghe.domain.ZhDeviceTypeAttr;

import java.util.List;

/**
 * 设备类型属性/物模型属性Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface ZhDeviceTypeAttrMapper 
{
    /**
     * 查询设备类型属性/物模型属性
     * 
     * @param id 设备类型属性/物模型属性主键
     * @return 设备类型属性/物模型属性
     */
    public ZhDeviceTypeAttr selectZhDeviceTypeAttrById(Long id);

    /**
     * 查询设备类型属性/物模型属性列表
     * 
     * @param zhDeviceTypeAttr 设备类型属性/物模型属性
     * @return 设备类型属性/物模型属性集合
     */
    public List<ZhDeviceTypeAttr> selectZhDeviceTypeAttrList(ZhDeviceTypeAttr zhDeviceTypeAttr);

    /**
     * 新增设备类型属性/物模型属性
     * 
     * @param zhDeviceTypeAttr 设备类型属性/物模型属性
     * @return 结果
     */
    public int insertZhDeviceTypeAttr(ZhDeviceTypeAttr zhDeviceTypeAttr);

    /**
     * 修改设备类型属性/物模型属性
     * 
     * @param zhDeviceTypeAttr 设备类型属性/物模型属性
     * @return 结果
     */
    public int updateZhDeviceTypeAttr(ZhDeviceTypeAttr zhDeviceTypeAttr);

    /**
     * 删除设备类型属性/物模型属性
     * 
     * @param id 设备类型属性/物模型属性主键
     * @return 结果
     */
    public int deleteZhDeviceTypeAttrById(Long id);

    /**
     * 批量删除设备类型属性/物模型属性
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZhDeviceTypeAttrByIds(Long[] ids);

    /**
     * 根据设备编号查询设备类型属性列表
     *
     * @param equipmentCode 设备编号
     * @return 设备类型属性列表
     */
    public List<ZhDeviceTypeAttr> selectZhDeviceTypeAttrListByequipmentCode(String equipmentCode);
}
