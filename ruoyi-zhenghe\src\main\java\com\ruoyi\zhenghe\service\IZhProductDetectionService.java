package com.ruoyi.zhenghe.service;

import java.util.List;
import java.util.Date;
import com.ruoyi.zhenghe.domain.ZhProductDetection;
import com.ruoyi.zhenghe.domain.dto.DetectionAnalysisDto;
import com.ruoyi.zhenghe.domain.dto.DetectionSummaryDto;
import com.ruoyi.zhenghe.domain.dto.ProductDetectionChartDto;

/**
 * 产品检测记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface IZhProductDetectionService 
{
    /**
     * 查询产品检测记录
     * 
     * @param id 产品检测记录主键
     * @return 产品检测记录
     */
    public ZhProductDetection selectZhProductDetectionById(Long id);

    /**
     * 查询产品检测记录列表
     * 
     * @param zhProductDetection 产品检测记录
     * @return 产品检测记录集合
     */
    public List<ZhProductDetection> selectZhProductDetectionList(ZhProductDetection zhProductDetection);

    /**
     * 查询产品检测记录分组列表（按日期分组）
     * 
     * @param zhProductDetection 产品检测记录
     * @return 产品检测记录集合
     */
    public List<DetectionSummaryDto> selectZhProductDetectionGroupList(ZhProductDetection zhProductDetection);

    /**
     * 新增产品检测记录
     * 
     * @param zhProductDetection 产品检测记录
     * @return 结果
     */
    public int insertZhProductDetection(ZhProductDetection zhProductDetection);

    /**
     * 修改产品检测记录
     * 
     * @param zhProductDetection 产品检测记录
     * @return 结果
     */
    public int updateZhProductDetection(ZhProductDetection zhProductDetection);

    /**
     * 批量删除产品检测记录
     * 
     * @param ids 需要删除的产品检测记录主键集合
     * @return 结果
     */
    public int deleteZhProductDetectionByIds(Long[] ids);

    /**
     * 删除产品检测记录信息
     * 
     * @param id 产品检测记录主键
     * @return 结果
     */
    public int deleteZhProductDetectionById(Long id);

    /**
     * 保存检测结果（专用接口）
     * 
     * @param zhProductDetection 产品检测记录
     * @return 结果
     */
    public int saveDetectionResult(ZhProductDetection zhProductDetection);

    /**
     * 根据产品ID查询检测记录列表
     * 
     * @param productId 产品ID
     * @return 检测记录集合
     */
    public List<ZhProductDetection> selectZhProductDetectionByProductId(Long productId);

    /**
     * 根据产品ID和时间范围查询检测统计数据
     * 
     * @param productId 产品ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 检测统计数据
     */
    public List<DetectionSummaryDto> selectDetectionStatisticsByProductId(Long productId, Date startTime, Date endTime);

    /**
     * 根据产品ID和检测日期查询瑕疵细分数据
     * 
     * @param productId 产品ID
     * @param detectionDate 检测日期
     * @return 瑕疵细分数据
     */
    public List<ZhProductDetection> selectDefectDetailsByProductIdAndDate(Long productId, Date detectionDate);

    /**
     * 获取产品检测分析数据
     * 
     * @param productId 产品ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 检测分析数据
     */
    public DetectionAnalysisDto getDetectionAnalysisData(Long productId, Date startTime, Date endTime);

    /**
     * 根据时间范围查询所有产品的检测汇总数据（按日期和产品分组）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 检测汇总数据
     */
    public List<DetectionSummaryDto> selectDetectionSummaryByDateRange(Date startTime, Date endTime);

    /**
     * 获取柱状图数据（按产品组合分组统计）
     *
     * @param productModel 产品型号（可选）
     * @param pieceCount 片数（可选）
     * @param sectionCount 节数（可选）
     * @param timingMark 正时标记（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 柱状图数据
     */
    public List<ProductDetectionChartDto> getChartData(String customerName,String productModel, String machineType,
                                                      String printMark, String timingMark,
                                                      Date startTime, Date endTime);
}
