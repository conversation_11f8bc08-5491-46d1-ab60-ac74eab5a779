<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhDeviceTypeMapper">

    <resultMap type="ZhDeviceType" id="ZhDeviceTypeResult">
        <result property="id" column="id"/>
        <result property="tslName" column="tsl_name"/>
        <result property="tslCode" column="tsl_code"/>
        <result property="tslDesc" column="tsl_desc"/>
        <result property="tslImg" column="tsl_img"/>
        <result property="tslSort" column="tsl_sort"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="remark" column="remark"/>
        <result property="ex1" column="ex1"/>
        <result property="mqTopic" column="mq_topic"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectZhDeviceTypeVo">
        SELECT
            d.id,
            d.tsl_name,
            d.tsl_code,
            d.tsl_desc,
            d.tsl_img,
            d.tsl_sort,
            d.dept_id,
            d.remark,
            d.ex1,
            d.mq_topic,
            d.create_by,
            d.create_time,
            d.update_by,
            d.update_time,
            de.dept_name
        FROM
            zh_device_type d
                LEFT JOIN sys_dept de ON d.dept_id = de.dept_id
    </sql>

    <select id="selectZhDeviceTypeList" parameterType="ZhDeviceType" resultMap="ZhDeviceTypeResult">
        <include refid="selectZhDeviceTypeVo"/>
        WHERE 1 = 1
        <if test="tslName != null  and tslName != ''">and d.tsl_name like concat('%', #{tslName}, '%')</if>
        <if test="tslCode != null  and tslCode != ''">and d.tsl_code like concat('%', #{tslCode}, '%')</if>
        <if test="tslDesc != null  and tslDesc != ''">and d.tsl_desc like concat('%', #{tslDesc}, '%')</if>
        <if test="tslImg != null  and tslImg != ''">and d.tsl_img = #{tslImg}</if>
        <if test="deptId != null  and deptId != ''">and d.dept_id = #{deptId}</if>
        <if test="ex1 != null  and ex1 != ''">and d.ex1 = #{ex1}</if>
        <if test="mqTopic != null  and mqTopic != ''">and d.mq_topic like concat('%', #{mqTopic}, '%')</if>
        ${params.dataScope}
        order by
        d.tsl_sort,
        d.create_time
    </select>

    <select id="selectZhDeviceTypeById" parameterType="Long" resultMap="ZhDeviceTypeResult">
        <include refid="selectZhDeviceTypeVo"/>
        where d.id = #{id}
    </select>
    <select id="selectZhDeviceTypeListByEquipmentId" resultType="com.ruoyi.zhenghe.domain.ZhDeviceType">
        <include refid="selectZhDeviceTypeVo"/>
        where d.id in (select distinct device_type_id from zh_equipment_prop where equipment_id = #{deviceId})
    </select>

    <insert id="insertZhDeviceType" parameterType="ZhDeviceType" useGeneratedKeys="true" keyProperty="id">
        insert into zh_device_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tslName != null">tsl_name,</if>
            <if test="tslCode != null">tsl_code,</if>
            <if test="tslDesc != null">tsl_desc,</if>
            <if test="tslImg != null">tsl_img,</if>
            <if test="tslSort != null">tsl_sort,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="remark != null">remark,</if>
            <if test="ex1 != null">ex1,</if>
            <if test="mqTopic != null">mq_topic,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tslName != null">#{tslName},</if>
            <if test="tslCode != null">#{tslCode},</if>
            <if test="tslDesc != null">#{tslDesc},</if>
            <if test="tslImg != null">#{tslImg},</if>
            <if test="tslSort != null">#{tslSort},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="ex1 != null">#{ex1},</if>
            <if test="mqTopic != null">#{mqTopic},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateZhDeviceType" parameterType="ZhDeviceType">
        update zh_device_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="tslName != null">tsl_name = #{tslName},</if>
            <if test="tslCode != null">tsl_code = #{tslCode},</if>
            <if test="tslDesc != null">tsl_desc = #{tslDesc},</if>
            <if test="tslImg != null">tsl_img = #{tslImg},</if>
            <if test="tslSort != null">tsl_sort = #{tslSort},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="ex1 != null">ex1 = #{ex1},</if>
            <if test="mqTopic != null">mq_topic = #{mqTopic},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhDeviceTypeById" parameterType="Long">
        delete
        from zh_device_type
        where id = #{id}
    </delete>

    <delete id="deleteZhDeviceTypeByIds" parameterType="String">
        delete from zh_device_type where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>