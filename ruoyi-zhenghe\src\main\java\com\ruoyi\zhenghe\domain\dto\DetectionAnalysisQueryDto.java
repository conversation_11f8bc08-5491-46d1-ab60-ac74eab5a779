package com.ruoyi.zhenghe.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 检测分析查询请求数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
public class DetectionAnalysisQueryDto {

    /** 产品型号 */
    @ApiModelProperty(value = "产品型号")
    private String productModel;

    /** 片数 */
    @ApiModelProperty(value = "片数")
    private Integer pieceCount;

    /** 节数 */
    @ApiModelProperty(value = "节数")
    private Integer sectionCount;

    /** 正时标记 */
    @ApiModelProperty(value = "正时标记")
    private String timingMark;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "开始时间，格式：yyyy-MM-dd")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "结束时间，格式：yyyy-MM-dd")
    private Date endTime;

    /** 页码 */
    @ApiModelProperty(value = "页码，默认1")
    private Integer pageNum = 1;

    /** 每页大小 */
    @ApiModelProperty(value = "每页大小，默认10")
    private Integer pageSize = 10;
}
