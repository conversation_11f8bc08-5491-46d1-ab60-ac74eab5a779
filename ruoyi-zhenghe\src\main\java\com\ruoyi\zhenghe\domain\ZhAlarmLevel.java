package com.ruoyi.zhenghe.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 告警等级管理对象 zh_alarm_level
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@ApiModel(value = "ZhAlarmLevel",description = "告警等级管理对象")
public class ZhAlarmLevel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 告警等级名称 */
    @ApiModelProperty(value = "告警等级名称")
    @Excel(name = "告警等级名称",sort = 1)
    private String alarmName;

    /** 告警编码 */
    @ApiModelProperty(value = "告警编码")
    @Excel(name = "告警编码",sort = 2)
    private String alarmCode;

    /** 部门id */
    private Long deptId;

    /** 事业部（部门 */
    @ApiModelProperty(value = "事业部（部门）")
    @Excel(name = "事业部（部门）",sort = 3)
    private String deptName;

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setAlarmName(String alarmName) 
    {
        this.alarmName = alarmName;
    }

    public String getAlarmName() 
    {
        return alarmName;
    }

    public void setAlarmCode(String alarmCode) 
    {
        this.alarmCode = alarmCode;
    }

    public String getAlarmCode() 
    {
        return alarmCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("alarmName", getAlarmName())
            .append("alarmCode", getAlarmCode())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
