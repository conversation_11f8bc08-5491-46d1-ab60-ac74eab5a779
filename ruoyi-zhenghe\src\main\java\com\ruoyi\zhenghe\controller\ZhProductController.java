package com.ruoyi.zhenghe.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.zhenghe.domain.ImportZhProduct;
import com.ruoyi.zhenghe.domain.ZhProduct;
import com.ruoyi.zhenghe.service.IZhProductDefectService;
import com.ruoyi.zhenghe.service.IZhProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 产品信息Controller
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Api(tags = "产品管理")
@RestController
@RequestMapping("/zhenghe/product")
public class ZhProductController extends BaseController {
    @Autowired
    private IZhProductService zhProductService;
    @Autowired
    private IZhProductDefectService zhProductDefectService;


    /**
     * 查询产品信息列表
     */
    @ApiOperation(value = "查询产品信息列表", response = ZhProduct.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "zhProduct", value = "产品信息", required = true, paramType = "body", dataType = "ZhProduct")
    })
//    @PreAuthorize("@ss.hasPermi('zhenghe:product:list')")
    @GetMapping("/list")
    public TableDataInfo list(ZhProduct zhProduct) {
        startPage();
        List<ZhProduct> list = zhProductService.selectZhProductList(zhProduct);
        return getDataTable(list);
    }

    /**
     * 导出产品信息列表
     */
    @ApiOperation(value = "导出产品信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "zhProduct", value = "产品信息", required = true, paramType = "body", dataType = "ZhProduct")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:product:export')")
    @Log(title = "产品信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhProduct zhProduct) {
        List<ZhProduct> list = zhProductService.selectZhProductList(zhProduct);
        ExcelUtil<ZhProduct> util = new ExcelUtil<>(ZhProduct.class);
        util.exportExcel(response, list, "产品信息数据");
    }

    /**
     * 导入产品模板下载
     */
    @ApiOperation(value = "导入产品模板下载")
    @PreAuthorize("@ss.hasPermi('zhenghe:product:import')")
    @PostMapping("/download/template")
    public void downloadTemplate(HttpServletResponse response) {
        List<ZhProduct> list = new ArrayList<>();
        ExcelUtil<ZhProduct> util = new ExcelUtil<>(ZhProduct.class);
        util.exportExcel(response, list, "产品信息");
    }

    /**
     * 导入产品表
     */
    @ApiOperation(value = "导入产品表")
    @PreAuthorize("@ss.hasPermi('zhenghe:product:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws IOException {
        ExcelUtil<ZhProduct> util = new ExcelUtil<>(ZhProduct.class);
        List<ZhProduct> productList = util.importExcel(file.getInputStream());

        if (StringUtils.isNull(productList) || productList.size() == 0) {
            throw new ServiceException("导入产品数据不能为空");
        }
        int successNum = 0;
        int failureNum = 0;

        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        //重复的数据
        List<ImportZhProduct> repeatProductList = new ArrayList<>();

        for (ZhProduct product : productList) {
            if (product.getProductModel() == null || product.getProductModel().trim().isEmpty()) {
                failureNum++;
                failureMsg.append("<br/>").append(failureNum).append("、产品型号不能为空");
                continue;
            }
            try {

                //构建查询条件，根据产品型号、客户名称、机型查询
                ZhProduct queryZhProduct = new ZhProduct();
                queryZhProduct.setProductModel(product.getProductModel());
                queryZhProduct.setCustomerName(product.getCustomerName());
                queryZhProduct.setMachineType(product.getMachineType());
                //检查是否已存在，存在则不插入
                final List<ZhProduct> oldProduct = zhProductService.selectZhProductList(queryZhProduct);
                ImportZhProduct importZhProduct = new ImportZhProduct();
                if (oldProduct.size() > 0) {
                    importZhProduct.setNewProduct(product);
                    List<ZhProduct> tempOldProduct = new ArrayList<>();
                    for (ZhProduct oldProductItem : oldProduct) {
                        tempOldProduct.add(oldProductItem);
                    }
                    importZhProduct.setOldProduct(tempOldProduct);
                    repeatProductList.add(importZhProduct);
                    continue;
                }

                // 注意：拼音生成逻辑已在service层的insertZhProduct方法中处理，这里不需要额外处理
                zhProductService.insertZhProduct(product);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、产品 ").append(product.getProductModel()).append(" 导入成功");
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、产品 " + product.getProductModel() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        if (repeatProductList.size() > 0) {
            return AjaxResult.success(successMsg.toString(), repeatProductList);
        }
        return AjaxResult.success(successMsg.toString());
    }

    /**
     * 获取产品信息详细信息
     */
    @ApiOperation(value = "获取产品信息详细信息", response = ZhProduct.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "产品信息id", required = true, paramType = "path", dataType = "long")
    })
//    @PreAuthorize("@ss.hasPermi('zhenghe:product:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(zhProductService.selectZhProductById(id));
    }

    /**
     * 新增产品信息
     */
    @ApiOperation(value = "新增产品")
    @PreAuthorize("@ss.hasPermi('zhenghe:product:add')")
    @Log(title = "产品信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZhProduct zhProduct) {
        return toAjax(zhProductService.insertZhProduct(zhProduct));
    }

    /**
     * 批量新增产品信息
     */
    @ApiOperation(value = "批量新增产品")
    @Log(title = "产品信息", businessType = BusinessType.INSERT)
    @PostMapping("/batch")
    public AjaxResult batchAdd(@RequestBody List<ZhProduct> zhProductList) {
        if (StringUtils.isNull(zhProductList) || zhProductList.isEmpty()) {
            return error("产品列表不能为空");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (ZhProduct product : zhProductList) {
            try {
                // 插入单个产品
                zhProductService.insertZhProduct(product);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、产品 ").append(product.getProductModel()).append(" 新增成功");
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、产品 " + product.getProductModel() + " 新增失败：";
                failureMsg.append(msg).append(e.getMessage());
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，部分产品新增失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            return error(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，所有产品已全部新增成功！共 " + successNum + " 条，数据如下：");
            return success(successMsg.toString());
        }
    }

    /**
     * 修改产品信息
     */
    @ApiOperation(value = "修改产品信息")
    @PreAuthorize("@ss.hasPermi('zhenghe:product:edit')")
    @Log(title = "产品信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhProduct zhProduct) {
        return toAjax(zhProductService.updateZhProduct(zhProduct));
    }

    /**
     * 删除产品信息
     */
    @ApiOperation(value = "删除产品by ids")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "主键ID串", required = true, paramType = "path", dataType = "Long"),
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:product:remove')")
    @Log(title = "产品信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(zhProductService.deleteZhProductByIds(ids));
    }


    /**
     * 获取所有产品型号下拉选项
     */
//    @ApiOperation(value = "获取所有产品编号下拉选项")
//    //@PreAuthorize("@ss.hasPermi('zhenghe:product:list')")
//    @GetMapping("/options/productCodes")
//    public AjaxResult getProductCodeOptions() {
//        List<String> list = zhProductService.selectAllProductCodes();
//        return success(list);
//    }

    /**
     * 获取所有产品型号下拉选项
     */
    @ApiOperation(value = "获取所有产品型号下拉选项")
//    @PreAuthorize("@ss.hasPermi('zhenghe:product:list')")
    @GetMapping("/options/productModels")
    public AjaxResult getProductModelOptions(@RequestParam String productModel) {
        List<String> list = zhProductService.selectAllProductModels(productModel);
        return success(list);
    }

    /**
     * 根据产品型号获取片数下拉选项
     */
    @ApiOperation(value = "根据产品型号获取片数下拉选项")
    @ApiImplicitParam(name = "productModel", value = "产品型号", paramType = "query", dataType = "String", required = true)
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:list')")
    @GetMapping("/options/pieceCounts")
    public AjaxResult getPieceCountOptions(@RequestParam String productModel) {
        List<Integer> list = zhProductService.selectPieceCountsByProductModel(productModel);
        return success(list);
    }

    @ApiOperation(value = "获取客户名称下拉选项")
    @ApiImplicitParam(name = "customerName", value = "客户名称筛选条件（支持中文或拼音）", paramType = "query", dataType = "String", required = false)
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:list')")
    @GetMapping("/options/pieceCustomer")
    public AjaxResult pieceCustomer(@RequestParam(required = false) String customerName) {
        List<String> list = zhProductService.selectPieceCustomer(customerName);
        return success(list);
    }

    /**
     * 根据产品型号和片数获取节数下拉选项
     */
    @ApiOperation(value = "根据产品型号和片数获取节数下拉选项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productModel", value = "产品型号", paramType = "query", dataType = "String", required = true),
            @ApiImplicitParam(name = "pieceCount", value = "片数", paramType = "query", dataType = "Integer", required = true)
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:list')")
    @GetMapping("/options/sectionCounts")
    public AjaxResult getSectionCountOptions(@RequestParam String productModel,
                                             @RequestParam Integer pieceCount) {
        List<Integer> list = zhProductService.selectSectionCountsByProductModelAndPieceCount(productModel, pieceCount);
        return success(list);
    }

    /**
     * 根据产品型号、片数和节数获取正时标记下拉选项
     */
    @ApiOperation(value = "根据产品型号、片数和节数获取正时标记下拉选项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productModel", value = "产品型号", paramType = "query", dataType = "String", required = true),
            @ApiImplicitParam(name = "pieceCount", value = "片数", paramType = "query", dataType = "Integer", required = true),
            @ApiImplicitParam(name = "sectionCount", value = "节数", paramType = "query", dataType = "Integer", required = true)
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:list')")
    @GetMapping("/options/timingMarks")
    public AjaxResult getTimingMarkOptions(@RequestParam String productModel,
                                           @RequestParam Integer pieceCount,
                                           @RequestParam Integer sectionCount) {
        List<String> list = zhProductService.selectTimingMarksByProductModelAndPieceCountAndSectionCount(productModel, pieceCount, sectionCount);
        return success(list);
    }

    /**
     * 根据组合条件查询产品
     */
    @ApiOperation(value = "根据组合条件查询产品", response = ZhProduct.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productModel", value = "产品型号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pieceCount", value = "片数", paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "sectionCount", value = "节数", paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "timingMark", value = "正时标记", paramType = "query", dataType = "String")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:query')")
    @GetMapping("/combination")
    public AjaxResult getProductByCombination(
            @RequestParam("productModel") String productModel,
            @RequestParam(value = "pieceCount", required = false) Integer pieceCount,
            @RequestParam(value = "sectionCount", required = false) Integer sectionCount,
            @RequestParam(value = "timingMark", required = false) String timingMark) {

        ZhProduct product = zhProductService.selectProductByCombination(productModel, pieceCount, sectionCount, timingMark);
        if (product == null) {
            return error("未找到匹配的产品组合，请检查选择的参数");
        }
        return success(product);
    }

    /**
     * 获取产品基本信息（用于检测录入显示）
     */
    @ApiOperation(value = "获取产品基本信息", response = ZhProduct.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "产品ID", required = true, paramType = "path", dataType = "Long")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:query')")
    @GetMapping("/basic/{id}")
    public AjaxResult getBasicInfo(@PathVariable("id") Long id) {
        ZhProduct product = zhProductService.selectZhProductById(id);
        if (product == null) {
            return error("产品不存在");
        }

        // 只返回基本信息，用于检测录入页面显示
        ZhProduct basicInfo = new ZhProduct();
        basicInfo.setId(product.getId());
        basicInfo.setProductModel(product.getProductModel());
        basicInfo.setPieceCount(product.getPieceCount());
        basicInfo.setSectionCount(product.getSectionCount());
        basicInfo.setTimingMark(product.getTimingMark());
        basicInfo.setProductImage(product.getProductImage());

        return success(basicInfo);
    }

    /**
     * 获取产品选项树状结构
     */
    @ApiOperation(value = "获取产品选项树状结构")
    //@PreAuthorize("@ss.hasPermi('zhenghe:product:list')")
    @GetMapping("/options/tree")
    public AjaxResult getProductOptionsTree() {
        return success(zhProductService.getProductOptionsTree());
    }

    @GetMapping("/selectZhProductDefectListByProductIdOpen")
    public AjaxResult selectZhProductDefectListByProductIdOpen(Long id) {
        return success(zhProductDefectService.selectZhProductDefectListByProductIdOpen(id));
    }
}
