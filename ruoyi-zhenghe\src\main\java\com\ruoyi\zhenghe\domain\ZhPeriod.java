package com.ruoyi.zhenghe.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 时段对象 zh_period
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
@ApiModel(value = "ZhPeriod",description = "时段对象")
public class ZhPeriod extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 时段名称 */
    @ApiModelProperty(value = "时段名称")
    @Excel(name = "时段名称")
    private String periodName;

    /** 时段颜色 */
    @ApiModelProperty(value = "时段颜色")
    @Excel(name = "时段颜色")
    private String periodColor;

    /** 部门id */
    @Excel(name = "部门id")
    private Long deptId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setPeriodName(String periodName) 
    {
        this.periodName = periodName;
    }

    public String getPeriodName() 
    {
        return periodName;
    }

    public void setPeriodColor(String periodColor) 
    {
        this.periodColor = periodColor;
    }

    public String getPeriodColor() 
    {
        return periodColor;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("periodName", getPeriodName())
            .append("periodColor", getPeriodColor())
            .append("deptId", getDeptId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
