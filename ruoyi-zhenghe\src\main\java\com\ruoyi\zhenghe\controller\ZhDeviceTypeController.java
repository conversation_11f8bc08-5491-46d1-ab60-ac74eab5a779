package com.ruoyi.zhenghe.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.zhenghe.domain.ZhDeviceType;
import com.ruoyi.zhenghe.service.IZhDeviceTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备类型/Iot物模型Controller
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Api(tags = "设备类型")
@RestController
@RequestMapping("/zhenghe/deviceType")
public class ZhDeviceTypeController extends BaseController {
    @Autowired
    private IZhDeviceTypeService zhDeviceTypeService;

    /**
     * 查询设备类型/Iot物模型列表
     */
    @ApiOperation(value = "查询设备类型列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", required = false, paramType = "query", dataType = "integer"),
            @ApiImplicitParam(name = "pageSize", value = "条数", required = false, paramType = "query", dataType = "integer"),
            @ApiImplicitParam(name = "tslName", value = "设备类型名", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "tslCode", value = "设备类型编码", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "mqTopic", value = "MQ主题", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "deptId", value = "部门id", required = false, paramType = "query", dataType = "String"),
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:deviceType:list')")
    @GetMapping("/list")
    public TableDataInfo list(ZhDeviceType zhDeviceType) {
        startPage();
        List<ZhDeviceType> list = zhDeviceTypeService.selectZhDeviceTypeList(zhDeviceType);
        return getDataTable(list);
    }


    /**
     * 导入基础数据-告警等级列表
     */
    @ApiOperation(value = "导入设备类型列表")
    //@PreAuthorize("@ss.hasPermi('zhenghe:level:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws IOException {
        ExcelUtil<ZhDeviceType> util = new ExcelUtil<>(ZhDeviceType.class);
        List<ZhDeviceType> list = util.importExcel(file.getInputStream());
        if (list == null || list.size() <= 0) {
            throw new ServiceException("导入设备类型数据不能为空");
        }
        int successNum = 0;
        int failureNum = 0;

        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ZhDeviceType deviceType : list) {
            try {
                deviceType.setDeptId(SecurityUtils.getDeptId());
                zhDeviceTypeService.insertZhDeviceType(deviceType);
                successNum++;
                successMsg.append("<br/>" + successNum + "、告警等级 " + deviceType.getTslName() + " 导入成功");
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、告警等级 " + deviceType.getTslName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return AjaxResult.success(successMsg.toString());
    }

    @ApiOperation(value = "导入设备类型模板下载")
    @Log(title = "导入设备类型模板下载", businessType = BusinessType.EXPORT)
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response, ZhDeviceType zhDeviceType) {
        ExcelUtil<ZhDeviceType> util = new ExcelUtil<>(ZhDeviceType.class);
        util.exportExcel(response, new ArrayList<>(), "设备类型");
    }

    /**
     * 导出设备类型/Iot物模型列表
     */
    @ApiOperation(value = "导出设备类型列表模板")
    @Log(title = "设备类型/Iot物模型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhDeviceType zhDeviceType) {
        List<ZhDeviceType> list = zhDeviceTypeService.selectZhDeviceTypeList(zhDeviceType);
        ExcelUtil<ZhDeviceType> util = new ExcelUtil<>(ZhDeviceType.class);
        util.exportExcel(response, list, "设备类型");
    }

    /**
     * 获取设备类型/Iot物模型详细信息
     */
    @ApiOperation(value = "获取设备类型详细信息", response = ZhDeviceType.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键ID", required = true, paramType = "path", dataType = "integer"),
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:deviceType:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(zhDeviceTypeService.selectZhDeviceTypeById(id));
    }

    /**
     * 新增设备类型/Iot物模型
     */
    @ApiOperation(value = "新增设备类型", response = ZhDeviceType.class)
    @PreAuthorize("@ss.hasPermi('zhenghe:deviceType:add')")
    @Log(title = "设备类型/Iot物模型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZhDeviceType zhDeviceType) {
        return toAjax(zhDeviceTypeService.insertZhDeviceType(zhDeviceType));
    }

    /**
     * 修改设备类型/Iot物模型
     */
    @ApiOperation(value = "修改设备类型", response = ZhDeviceType.class)
    @PreAuthorize("@ss.hasPermi('zhenghe:deviceType:edit')")
    @Log(title = "设备类型/Iot物模型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhDeviceType zhDeviceType) {
        return toAjax(zhDeviceTypeService.updateZhDeviceType(zhDeviceType));
    }

    /**
     * 删除设备类型/Iot物模型
     */
    @ApiOperation(value = "删除设备类型", response = ZhDeviceType.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "主键ID串", required = true, paramType = "path", dataType = "Long"),
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:deviceType:remove')")
    @Log(title = "设备类型/Iot物模型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(zhDeviceTypeService.deleteZhDeviceTypeByIds(ids));
    }
}
