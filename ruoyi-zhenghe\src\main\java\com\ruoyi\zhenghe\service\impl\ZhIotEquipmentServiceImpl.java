package com.ruoyi.zhenghe.service.impl;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.domain.ZhIotEquipment2QueryVo;
import com.ruoyi.zhenghe.domain.query.ZhIotEquipmentQuery;
import com.ruoyi.zhenghe.mapper.ZhIotEquipmentMapper;
import com.ruoyi.zhenghe.service.IZhIotEquipmentService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.HashMap;

/**
 * 物联网设备明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Service
public class ZhIotEquipmentServiceImpl implements IZhIotEquipmentService {
    @Autowired
    private ZhIotEquipmentMapper zhIotEquipmentMapper;

    /**
     * 查询物联网设备明细
     *
     * @param id 物联网设备明细主键
     * @return 物联网设备明细
     */
    @Override
    public ZhIotEquipment selectZhIotEquipmentById(Long id) {
        return zhIotEquipmentMapper.selectZhIotEquipmentById(id);
    }

    /**
     * 查询物联网设备明细列表
     *
     * @param zhIotEquipment 物联网设备明细
     * @return 物联网设备明细
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<ZhIotEquipment> selectZhIotEquipmentList(ZhIotEquipment zhIotEquipment) {
        return zhIotEquipmentMapper.selectZhIotEquipmentList(zhIotEquipment);
    }

    @Override
    @DataScope(deptAlias = "d")
    public List<ZhIotEquipment> selectZhIotEquipmentList2(ZhIotEquipment2QueryVo zhIotEquipment2QueryVo) {
        // 转换VO为继承BaseEntity的查询对象，以支持@DataScope注解
        ZhIotEquipmentQuery query = new ZhIotEquipmentQuery();
        query.setDeptId(zhIotEquipment2QueryVo.getDeptId());
        query.setWorkshopId(zhIotEquipment2QueryVo.getWorkshopId());
        query.setDeviceTypeId(zhIotEquipment2QueryVo.getDeviceTypeId());
        query.setProcessId(zhIotEquipment2QueryVo.getProcessId());
        query.setEquipmentName(zhIotEquipment2QueryVo.getEquipmentName());

        // 如果@DataScope没有生效，手动添加数据权限
        if (query.getParams() == null || !query.getParams().containsKey("dataScope")) {
            // 手动构建数据权限SQL
            Long deptId = SecurityUtils.getDeptId();
            if (deptId != null) {
                String dataScopeSQL = " AND (d.dept_id = " + deptId +
                    " OR d.dept_id IN (SELECT dept_id FROM sys_dept WHERE dept_id = " + deptId +
                    " OR find_in_set(" + deptId + ", ancestors)))";

                if (query.getParams() == null) {
                    query.setParams(new HashMap<>());
                }
                query.getParams().put("dataScope", dataScopeSQL);
            }
        }

        return zhIotEquipmentMapper.selectZhIotEquipmentList2(query);
    }

    @Override
    public List<ZhIotEquipment> selectZhIotEquipmentListNoScope(ZhIotEquipment zhIotEquipment) {
        return zhIotEquipmentMapper.selectZhIotEquipmentList(zhIotEquipment);
    }

    /**
     * 新增物联网设备明细
     *
     * @param zhIotEquipment 物联网设备明细
     * @return 结果
     */
    @Override
    public int insertZhIotEquipment(ZhIotEquipment zhIotEquipment) {
        zhIotEquipment.setCreateTime(DateUtils.getNowDate());
        zhIotEquipment.setCreateBy(SecurityUtils.getUsername());
        if (zhIotEquipment.getDeptId() == null) {
            zhIotEquipment.setDeptId(SecurityUtils.getDeptId());
        }
        return zhIotEquipmentMapper.insertZhIotEquipment(zhIotEquipment);
    }

    /**
     * 修改物联网设备明细
     *
     * @param zhIotEquipment 物联网设备明细
     * @return 结果
     */
    @Override
    public int updateZhIotEquipment(ZhIotEquipment zhIotEquipment) {
        zhIotEquipment.setUpdateTime(DateUtils.getNowDate());
        zhIotEquipment.setUpdateBy(SecurityUtils.getUsername());
        return zhIotEquipmentMapper.updateZhIotEquipment(zhIotEquipment);
    }

    /**
     * 批量删除物联网设备明细
     *
     * @param ids 需要删除的物联网设备明细主键
     * @return 结果
     */
    @Override
    public int deleteZhIotEquipmentByIds(Long[] ids) {
        return zhIotEquipmentMapper.deleteZhIotEquipmentByIds(ids);
    }

    /**
     * 删除物联网设备明细信息
     *
     * @param id 物联网设备明细主键
     * @return 结果
     */
    @Override
    public int deleteZhIotEquipmentById(Long id) {
        return zhIotEquipmentMapper.deleteZhIotEquipmentById(id);
    }
}
