package com.ruoyi.zhenghe.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 产品瑕疵对象 zh_product_defect
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
public class ZhProductDefect extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 产品ID */
    @Excel(name = "产品id", sort = 0)
    @ApiModelProperty(value = "产品ID")
    private Long productId;

    /** 瑕疵名称 */
    @Excel(name = "瑕疵名称", sort = 1)
    @ApiModelProperty(value = "瑕疵名称")
    private String defectName;

    /** 瑕疵图片 */
    @ApiModelProperty(value = "瑕疵图片")
    private String defectImage;

    /** 状态（0-禁用，1-启用） */
    @Excel(name = "状态 0=禁用,1=启用", sort = 2, readConverterExp = "0=禁用,1=启用")
    @ApiModelProperty(value = "状态（0-禁用，1-启用）")
    private Integer status;

    /** 排序序号 */
    @Excel(name = "排序序号", sort = 3)
    @ApiModelProperty(value = "排序序号")
    private Integer sortOrder;

    /** 产品型号（关联查询字段） */
//    @Excel(name = "产品型号", sort = 4)
    @ApiModelProperty(value = "产品型号")
    private String productModel;

    /** 产品名称（关联查询字段） */
    @ApiModelProperty(value = "产品名称")
    private String productName;
}
