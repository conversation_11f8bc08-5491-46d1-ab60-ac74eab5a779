package com.ruoyi.zhenghe.service.impl;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.zhenghe.domain.ZhWorkshop;
import com.ruoyi.zhenghe.mapper.ZhWorkshopMapper;
import com.ruoyi.zhenghe.service.IZhWorkshopService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车间管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Service
public class ZhWorkshopServiceImpl implements IZhWorkshopService {
    @Autowired
    private ZhWorkshopMapper zhWorkshopMapper;

    /**
     * 查询车间管理
     *
     * @param id 车间管理主键
     * @return 车间管理
     */
    @Override
    public ZhWorkshop selectZhWorkshopById(Long id) {
        return zhWorkshopMapper.selectZhWorkshopById(id);
    }

    /**
     * 查询车间管理列表
     *
     * @param zhWorkshop 车间管理
     * @return 车间管理
     */
    @Override
    @DataScope(deptAlias = "w")
    public List<ZhWorkshop> selectZhWorkshopList(ZhWorkshop zhWorkshop) {
        List<ZhWorkshop> zhWorkshopList = zhWorkshopMapper.selectZhWorkshopList(zhWorkshop);
        for (ZhWorkshop workshop : zhWorkshopList) {
            List<ZhWorkshop> childrenList = zhWorkshopMapper.selectZhWorkshopByParentId(workshop.getId());
            workshop.setChildren(childrenList);
        }
        return zhWorkshopList;
    }

    @Override
    @DataScope(deptAlias = "w")
    public List<ZhWorkshop> selectOnlyZhWorkshopList(ZhWorkshop zhWorkshop) {

        List<ZhWorkshop> zhWorkshopList = zhWorkshopMapper.selectZhWorkshopList(zhWorkshop);
        return zhWorkshopList;
    }

    /**
     * 新增车间管理
     *
     * @param zhWorkshop 车间管理
     * @return 结果
     */
    @Override
    public int insertZhWorkshop(ZhWorkshop zhWorkshop) {
        if(zhWorkshop.getDeptId()==null){
            zhWorkshop.setDeptId(SecurityUtils.getDeptId());
        }
        zhWorkshop.setCreateTime(DateUtils.getNowDate());
        zhWorkshop.setCreateBy(SecurityUtils.getUsername());
        return zhWorkshopMapper.insertZhWorkshop(zhWorkshop);
    }

    /**
     * 修改车间管理
     *
     * @param zhWorkshop 车间管理
     * @return 结果
     */
    @Override
    public int updateZhWorkshop(ZhWorkshop zhWorkshop) {
        zhWorkshop.setUpdateTime(DateUtils.getNowDate());
        return zhWorkshopMapper.updateZhWorkshop(zhWorkshop);
    }

    /**
     * 批量删除车间管理
     *
     * @param ids 需要删除的车间管理主键
     * @return 结果
     */
    @Override
    public int deleteZhWorkshopByIds(Long[] ids) {
        return zhWorkshopMapper.deleteZhWorkshopByIds(ids);
    }

    /**
     * 删除车间管理信息
     *
     * @param id 车间管理主键
     * @return 结果
     */
    @Override
    public int deleteZhWorkshopById(Long id) {
        return zhWorkshopMapper.deleteZhWorkshopById(id);
    }
}
