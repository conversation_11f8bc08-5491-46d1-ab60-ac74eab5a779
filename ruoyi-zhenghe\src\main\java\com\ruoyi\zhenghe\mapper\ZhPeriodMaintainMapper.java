package com.ruoyi.zhenghe.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.zhenghe.controller.ZhPeriodMaintainSqlProvider;
import com.ruoyi.zhenghe.domain.PeriodMaintainData;
import com.ruoyi.zhenghe.domain.ZhPeriodMaintain;
import com.ruoyi.zhengheiot.domain.HistoryData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;

/**
 * 尖峰平谷时段维护Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface ZhPeriodMaintainMapper 
{
    /**
     * 查询尖峰平谷时段维护
     * 
     * @param id 尖峰平谷时段维护主键
     * @return 尖峰平谷时段维护
     */
    public ZhPeriodMaintain selectZhPeriodMaintainById(Long id);

    /**
     * 查询尖峰平谷时段维护列表
     * 
     * @param zhPeriodMaintain 尖峰平谷时段维护
     * @return 尖峰平谷时段维护集合
     */
    public List<ZhPeriodMaintain> selectZhPeriodMaintainList(ZhPeriodMaintain zhPeriodMaintain);

    /**
     * 新增尖峰平谷时段维护
     * 
     * @param zhPeriodMaintain 尖峰平谷时段维护
     * @return 结果
     */
    public int insertZhPeriodMaintain(ZhPeriodMaintain zhPeriodMaintain);

    /**
     * 修改尖峰平谷时段维护
     * 
     * @param zhPeriodMaintain 尖峰平谷时段维护
     * @return 结果
     */
    public int updateZhPeriodMaintain(ZhPeriodMaintain zhPeriodMaintain);

    /**
     * 删除尖峰平谷时段维护
     * 
     * @param id 尖峰平谷时段维护主键
     * @return 结果
     */
    public int deleteZhPeriodMaintainById(Long id);

    /**
     * 批量删除尖峰平谷时段维护
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZhPeriodMaintainByIds(Long[] ids);

    /**
     * 删除尖峰平谷时段维护根据年
     *
     * @param yearTime 尖峰平谷年
     * @return 结果
     */
    public int deleteZhPeriodMaintainByYearTime(String yearTime);

    @SelectProvider(type = ZhPeriodMaintainSqlProvider.class,method = "getPeriodMaintainSqlProvider")
    String selectZhPeriodMaintain(@Param("filedName") String filedName,@Param("yearTime") String yearTime,
                                  @Param("period") String period);


}
