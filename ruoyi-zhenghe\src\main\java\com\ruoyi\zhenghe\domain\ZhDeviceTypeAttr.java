package com.ruoyi.zhenghe.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 设备类型属性/物模型属性对象 zh_device_type_attr
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public class ZhDeviceTypeAttr extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 设备类型id */
    @Excel(name = "设备类型id",sort = 1)
    private Long tslId;

    /** 属性中文名称 */
    @Excel(name = "属性名称",sort = 2)
    private String attrName;

    /** 属性编码 */
    @Excel(name = "属性编码",sort = 3)
    private String attrCode;

    /** 属性描述 */
    @Excel(name = "属性描述",sort = 10)
    private String attrDesc;

    /** 数据类型 */
    @Excel(name = "数据类型",sort = 5)
    private String attrType;

    /** 数据单位 */
    @Excel(name = "数据单位",sort = 6)
    private String attrUnit;

    /** 数据分类 1数据 2故障 */
    @Excel(name = "数据分类 1数据 2故障",sort = 7)
    private String attrClass;

    /** 图标 */
//    @Excel(name = "图标")
    private String attrIcon;

    /** 序号 */
    @Excel(name = "排序号",sort = 9)
    private Integer attrOrder;

    /** 倍数 */
    @Excel(name = "倍数", sort = 8)
    private BigDecimal attrMultiple;

    /** $column.columnComment */
    private String ex1;

    /** $column.columnComment */
    private String ex2;

    /** 网关属性配置 */
    @Excel(name = "网关属性配置", sort = 11)
    private String gatewayAttr;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setTslId(Long tslId) 
    {
        this.tslId = tslId;
    }

    public Long getTslId() 
    {
        return tslId;
    }

    public void setAttrName(String attrName) 
    {
        this.attrName = attrName;
    }

    public String getAttrName() 
    {
        return attrName;
    }

    public void setAttrCode(String attrCode) 
    {
        this.attrCode = attrCode;
    }

    public String getAttrCode() 
    {
        return attrCode;
    }

    public void setAttrDesc(String attrDesc) 
    {
        this.attrDesc = attrDesc;
    }

    public String getAttrDesc() 
    {
        return attrDesc;
    }

    public void setAttrType(String attrType) 
    {
        this.attrType = attrType;
    }

    public String getAttrType() 
    {
        return attrType;
    }

    public void setAttrUnit(String attrUnit) 
    {
        this.attrUnit = attrUnit;
    }

    public String getAttrUnit() 
    {
        return attrUnit;
    }

    public void setAttrClass(String attrClass) 
    {
        this.attrClass = attrClass;
    }

    public String getAttrClass() 
    {
        return attrClass;
    }

    public void setAttrIcon(String attrIcon) 
    {
        this.attrIcon = attrIcon;
    }

    public String getAttrIcon() 
    {
        return attrIcon;
    }

    public void setAttrOrder(Integer attrOrder) 
    {
        this.attrOrder = attrOrder;
    }

    public Integer getAttrOrder() 
    {
        return attrOrder;
    }

    public void setAttrMultiple(BigDecimal attrMultiple) 
    {
        this.attrMultiple = attrMultiple;
    }

    public BigDecimal getAttrMultiple() 
    {
        return attrMultiple;
    }

    public void setEx1(String ex1) 
    {
        this.ex1 = ex1;
    }

    public String getEx1() 
    {
        return ex1;
    }

    public void setEx2(String ex2) 
    {
        this.ex2 = ex2;
    }

    public String getEx2()
    {
        return ex2;
    }

    public void setGatewayAttr(String gatewayAttr)
    {
        this.gatewayAttr = gatewayAttr;
    }

    public String getGatewayAttr()
    {
        return gatewayAttr;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("tslId", getTslId())
            .append("attrName", getAttrName())
            .append("attrCode", getAttrCode())
            .append("attrDesc", getAttrDesc())
            .append("attrType", getAttrType())
            .append("attrUnit", getAttrUnit())
            .append("attrClass", getAttrClass())
            .append("attrIcon", getAttrIcon())
            .append("attrOrder", getAttrOrder())
            .append("attrMultiple", getAttrMultiple())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("ex1", getEx1())
            .append("ex2", getEx2())
            .append("gatewayAttr", getGatewayAttr())
            .toString();
    }
}
