package com.ruoyi.zhenghe.service.impl;

import java.util.List;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.zhenghe.mapper.ZhClassesMapper;
import com.ruoyi.zhenghe.domain.ZhClasses;
import com.ruoyi.zhenghe.service.IZhClassesService;

/**
 * 班次Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
@Service
public class ZhClassesServiceImpl implements IZhClassesService 
{
    @Autowired
    private ZhClassesMapper zhClassesMapper;

    /**
     * 查询班次
     * 
     * @param id 班次主键
     * @return 班次
     */
    @Override
    public ZhClasses selectZhClassesById(Long id)
    {
        return zhClassesMapper.selectZhClassesById(id);
    }

    /**
     * 查询班次列表
     * 
     * @param zhClasses 班次
     * @return 班次
     */
    @Override
    @DataScope(deptAlias = "t1")
    public List<ZhClasses> selectZhClassesList(ZhClasses zhClasses)
    {
        return zhClassesMapper.selectZhClassesList(zhClasses);
    }

    /**
     * 新增班次
     * 
     * @param zhClasses 班次
     * @return 结果
     */
    @Override
    public int insertZhClasses(ZhClasses zhClasses)
    {
        zhClasses.setDeptId(SecurityUtils.getDeptId());
        zhClasses.setCreateBy(SecurityUtils.getUsername());
        zhClasses.setCreateTime(DateUtils.getNowDate());
        return zhClassesMapper.insertZhClasses(zhClasses);
    }

    /**
     * 修改班次
     * 
     * @param zhClasses 班次
     * @return 结果
     */
    @Override
    public int updateZhClasses(ZhClasses zhClasses)
    {
        zhClasses.setUpdateTime(DateUtils.getNowDate());
        return zhClassesMapper.updateZhClasses(zhClasses);
    }

    /**
     * 批量删除班次
     * 
     * @param ids 需要删除的班次主键
     * @return 结果
     */
    @Override
    public int deleteZhClassesByIds(Long[] ids)
    {
        return zhClassesMapper.deleteZhClassesByIds(ids);
    }

    /**
     * 删除班次信息
     * 
     * @param id 班次主键
     * @return 结果
     */
    @Override
    public int deleteZhClassesById(Long id)
    {
        return zhClassesMapper.deleteZhClassesById(id);
    }
}
