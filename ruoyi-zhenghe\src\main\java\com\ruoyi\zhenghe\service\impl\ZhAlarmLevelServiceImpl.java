package com.ruoyi.zhenghe.service.impl;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.zhenghe.domain.ZhAlarmLevel;
import com.ruoyi.zhenghe.mapper.ZhAlarmLevelMapper;
import com.ruoyi.zhenghe.service.IZhAlarmLevelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 告警等级管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@Service
public class ZhAlarmLevelServiceImpl implements IZhAlarmLevelService 
{
    @Autowired
    private ZhAlarmLevelMapper zhAlarmLevelMapper;

    /**
     * 查询告警等级管理
     * 
     * @param id 告警等级管理主键
     * @return 告警等级管理
     */
    @Override
    public ZhAlarmLevel selectZhAlarmLevelById(Long id)
    {
        return zhAlarmLevelMapper.selectZhAlarmLevelById(id);
    }

    /**
     * 查询告警等级管理列表
     * 
     * @param zhAlarmLevel 告警等级管理
     * @return 告警等级管理
     */
    @Override
    @DataScope(deptAlias = "a")
    public List<ZhAlarmLevel> selectZhAlarmLevelList(ZhAlarmLevel zhAlarmLevel)
    {
        return zhAlarmLevelMapper.selectZhAlarmLevelList(zhAlarmLevel);
    }

    /**
     * 新增告警等级管理
     * 
     * @param zhAlarmLevel 告警等级管理
     * @return 结果
     */
    @Override
    public int insertZhAlarmLevel(ZhAlarmLevel zhAlarmLevel)
    {
        if(zhAlarmLevel.getDeptId()==null){
            zhAlarmLevel.setDeptId(SecurityUtils.getDeptId());
        }
        zhAlarmLevel.setCreateBy(SecurityUtils.getUsername());
        zhAlarmLevel.setCreateTime(DateUtils.getNowDate());
        return zhAlarmLevelMapper.insertZhAlarmLevel(zhAlarmLevel);
    }

    /**
     * 修改告警等级管理
     * 
     * @param zhAlarmLevel 告警等级管理
     * @return 结果
     */
    @Override
    public int updateZhAlarmLevel(ZhAlarmLevel zhAlarmLevel)
    {
        zhAlarmLevel.setUpdateTime(DateUtils.getNowDate());
        return zhAlarmLevelMapper.updateZhAlarmLevel(zhAlarmLevel);
    }

    /**
     * 批量删除告警等级管理
     * 
     * @param ids 需要删除的告警等级管理主键
     * @return 结果
     */
    @Override
    public int deleteZhAlarmLevelByIds(Long[] ids)
    {
        return zhAlarmLevelMapper.deleteZhAlarmLevelByIds(ids);
    }

    /**
     * 删除告警等级管理信息
     * 
     * @param id 告警等级管理主键
     * @return 结果
     */
    @Override
    public int deleteZhAlarmLevelById(Long id)
    {
        return zhAlarmLevelMapper.deleteZhAlarmLevelById(id);
    }
}
