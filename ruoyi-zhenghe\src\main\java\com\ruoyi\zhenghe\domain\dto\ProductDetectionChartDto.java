package com.ruoyi.zhenghe.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 产品检测柱状图数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
public class ProductDetectionChartDto {

    /** 产品组合标识（用于Y轴显示） */
    @ApiModelProperty(value = "产品组合标识")
    private String productCombination;

    /** 产品型号 */
    @ApiModelProperty(value = "产品型号")
    private String productModel;

    /** 片数 */
    @ApiModelProperty(value = "片数")
    private Integer pieceCount;

    /** 节数 */
    @ApiModelProperty(value = "节数")
    private Integer sectionCount;

    /** 正时标记 */
    @ApiModelProperty(value = "正时标记")
    private String timingMark;

    /** 合格数量 */
    @ApiModelProperty(value = "合格数量")
    private Integer qualifiedCount;

    /** 瑕疵数量 */
    @ApiModelProperty(value = "瑕疵数量")
    private Integer defectCount;

    /** 总数量 */
    @ApiModelProperty(value = "总数量")
    private Integer totalCount;

    /** 合格率 */
    @ApiModelProperty(value = "合格率")
    private Double qualifiedRate;

    /** 瑕疵率 */
    @ApiModelProperty(value = "瑕疵率")
    private Double defectRate;

    private String customerName;
    private String machineType;
    private String printMark;
    private String oe;
    private String chainLength;
    private String chainLengthTolerance;

    /**
     * 构造产品组合标识
     * 格式:客户名称/产品型号/机型/正式标记/打印、批次标识拼起
     * 修改为 ：客户名称/产品型号/机型/OE号/链长/链长公差/正时标记/打印、批次标识
     */
    public void buildProductCombination() {
        this.productCombination = String.format("%s/%s/%s/%s/%s/%s/%s/%s",
                customerName!= null? customerName : "",
                productModel != null ? productModel : "",
                machineType!= null? machineType : "",
                oe!= null? oe : "",
                chainLength!= null? chainLength : "",
                chainLengthTolerance!= null? chainLengthTolerance : "",
                timingMark != null ? timingMark : "",
                printMark!= null? printMark : "");
    }

    /**
     * 计算合格率和瑕疵率
     */
    public void calculateRates() {
        if (totalCount != null && totalCount > 0) {
            this.qualifiedRate = qualifiedCount != null ? 
                    Math.round((qualifiedCount.doubleValue() / totalCount) * 10000.0) / 100.0 : 0.0;
            this.defectRate = defectCount != null ? 
                    Math.round((defectCount.doubleValue() / totalCount) * 10000.0) / 100.0 : 0.0;
        } else {
            this.qualifiedRate = 0.0;
            this.defectRate = 0.0;
        }
    }

    public ProductDetectionChartDto() {
    }

    public ProductDetectionChartDto(String productModel, Integer pieceCount, Integer sectionCount, 
                                   String timingMark, Integer qualifiedCount, Integer defectCount, Integer totalCount) {
        this.productModel = productModel;
        this.pieceCount = pieceCount;
        this.sectionCount = sectionCount;
        this.timingMark = timingMark;
        this.qualifiedCount = qualifiedCount;
        this.defectCount = defectCount;
        this.totalCount = totalCount;
        
        buildProductCombination();
        calculateRates();
    }
}
