# 日志配置问题修复说明

## 🚨 发现的问题

### 1. **日志路径问题**
**问题**：
```xml
<property name="log.path" value="/home/<USER>/logs" />
```
- 使用了Linux绝对路径 `/home/<USER>/logs`
- 在Windows环境下无法创建此路径
- 会导致日志文件无法写入

**修复**：
```xml
<property name="log.path" value="./logs" />
```
- 改为相对路径 `./logs`
- 在项目根目录下创建logs文件夹
- 跨平台兼容（Windows/Linux/Mac）

### 2. **重复的root配置**
**问题**：
```xml
<root level="warn">
    <appender-ref ref="console" />
</root>

<root level="warn">
    <appender-ref ref="file_info" />
    <appender-ref ref="file_error" />
</root>
```
- 配置了两个 `<root>` 元素
- 这是无效的XML配置
- 后面的配置会覆盖前面的

**修复**：
```xml
<root level="warn">
    <appender-ref ref="console" />
    <appender-ref ref="file_info" />
    <appender-ref ref="file_error" />
</root>
```
- 合并为一个root配置
- 同时输出到控制台和文件

### 3. **文件日志级别调整**
**问题**：
```xml
<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
    <level>WARN</level>
</filter>
```
- 文件日志只记录WARN及以上级别
- 重要的INFO级别日志无法记录到文件

**修复**：
```xml
<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
    <level>INFO</level>
</filter>
```
- 改为记录INFO及以上级别
- 确保重要的业务日志能被记录

## 📁 日志文件输出路径

修复后，日志文件将输出到以下位置：

### Windows环境
```
项目根目录/logs/
├── sys-info.log              # 系统信息日志（INFO及以上）
├── sys-info.2025-01-25.log   # 按日期滚动的历史日志
├── sys-error.log             # 错误日志（仅ERROR级别）
├── sys-error.2025-01-25.log  # 按日期滚动的错误日志
├── sys-user.log              # 用户操作日志
└── sys-user.2025-01-25.log   # 按日期滚动的用户日志
```

### Linux环境
```
/path/to/project/logs/
├── sys-info.log
├── sys-info.2025-01-25.log
├── sys-error.log
├── sys-error.2025-01-25.log
├── sys-user.log
└── sys-user.2025-01-25.log
```

## 📊 日志级别配置

### 控制台输出
- **级别**：WARN及以上
- **内容**：警告和错误信息
- **格式**：`HH:mm:ss.SSS [thread] LEVEL logger - [method,line] - message`

### 文件输出

#### 1. sys-info.log
- **级别**：INFO及以上（INFO、WARN、ERROR）
- **内容**：系统运行信息、警告、错误
- **滚动**：按天滚动，保留60天

#### 2. sys-error.log
- **级别**：仅ERROR
- **内容**：系统错误信息
- **滚动**：按天滚动，保留60天

#### 3. sys-user.log
- **级别**：INFO及以上
- **内容**：用户操作日志
- **滚动**：按天滚动，保留60天

### 组件日志级别
```xml
com.ruoyi                -> WARN    # 系统业务模块
org.springframework      -> WARN    # Spring框架
org.apache.iotdb         -> WARN    # IoTDB数据库
org.eclipse.paho         -> WARN    # MQTT客户端
org.apache.ibatis        -> WARN    # MyBatis ORM
com.alibaba.druid        -> WARN    # Druid数据源
```

## 🔧 验证修复效果

### 1. 检查日志目录
启动应用后，检查项目根目录是否创建了 `logs` 文件夹：
```bash
# Windows
dir logs

# Linux/Mac
ls -la logs/
```

### 2. 检查日志文件
确认以下文件是否正常创建：
- `logs/sys-info.log`
- `logs/sys-error.log`
- `logs/sys-user.log`

### 3. 测试日志输出
```java
// 在代码中添加测试日志
log.info("测试INFO级别日志 - 应该出现在sys-info.log中");
log.warn("测试WARN级别日志 - 应该出现在控制台和sys-info.log中");
log.error("测试ERROR级别日志 - 应该出现在所有输出中");
```

## 🎯 日志使用建议

### 1. 日志级别使用规范
- **ERROR**：系统错误、异常情况
- **WARN**：警告信息、潜在问题
- **INFO**：重要的业务流程信息
- **DEBUG**：调试信息（生产环境不输出）

### 2. 重要业务日志
对于生产数据处理等重要业务，建议使用INFO级别：
```java
log.info("处理设备 {} 的生产数据: 产品={}, 数量={}", deviceCode, productName, count);
log.info("查询产品数量（基于IoTDB） - 产品名称: {}, 时间范围: {} - {}", productName, beginTime, endTime);
```

### 3. 错误处理日志
```java
try {
    // 业务逻辑
} catch (Exception e) {
    log.error("处理MQTT消息失败: 设备={}, 错误={}", deviceCode, e.getMessage(), e);
}
```

## 🚀 生效方式

修改配置文件后，重启应用即可生效：

1. **停止应用**
2. **重新启动应用**
3. **检查日志输出**

## 📝 监控建议

### 1. 日志文件大小监控
- 定期检查日志文件大小
- 确保磁盘空间充足
- 必要时调整保留天数

### 2. 错误日志监控
- 定期检查 `sys-error.log`
- 关注频繁出现的错误
- 及时处理系统异常

### 3. 日志轮转验证
- 确认日志文件按天正常轮转
- 检查历史日志文件是否正常保留
- 验证超过60天的日志是否正常删除

## 总结

通过这次修复，解决了：
1. ✅ **跨平台兼容性问题** - 使用相对路径
2. ✅ **XML配置错误** - 合并重复的root配置
3. ✅ **日志级别优化** - 文件日志记录INFO级别
4. ✅ **日志输出路径明确** - 项目根目录/logs/

现在日志系统可以正常工作，重要的业务信息会被正确记录到文件中，便于问题排查和系统监控！
