
package com.ruoyi.zhengheiot.domain;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
public class DeviceNesting {
    private Long id;
    private String code;
    private String name;
    private List<DeviceNesting> child = new ArrayList<>();

    // 无参构造方法
    public DeviceNesting() {}

    // 全参数构造方法
    public DeviceNesting(Long id, String code, String name, List<DeviceNesting> child) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.child = child != null ? child : new ArrayList<>();
    }

    // 手动实现 toString 方法，避免递归调用
    @Override
    public String toString() {
        return "DeviceNesting{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", child=" + (child != null ? child.size() : 0) + " items" +
                '}';
    }

    // 手动实现 equals 和 hashCode 方法
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DeviceNesting that = (DeviceNesting) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(code, that.code) &&
                Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, code, name);
    }
}

