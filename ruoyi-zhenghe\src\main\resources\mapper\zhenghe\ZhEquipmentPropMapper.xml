<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhEquipmentPropMapper">

    <resultMap type="ZhEquipmentProp" id="ZhEquipmentPropResult">
        <result property="id" column="id"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="attrId" column="attr_id"/>
        <result property="faultVal" column="fault_val"/>
        <result property="minVal" column="min_val"/>
        <result property="maxVal" column="max_val"/>
        <result property="enumList" column="enum_list"/>
        <result property="showType" column="show_type"/>
        <result property="autoFault" column="auto_fault"/>
        <result property="faultTime" column="fault_time"/>
        <result property="autoBill" column="auto_bill"/>
        <result property="billTime" column="bill_time"/>
        <result property="billUser" column="bill_user"/>
        <result property="customMultiple" column="custom_multiple"/>
        <result property="alarm" column="alarm"/>
    </resultMap>

    <resultMap type="ZhEquipmentProp" id="ZhEquipmentPropLianResult">
        <result property="id" column="id"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="attrId" column="attr_id"/>
        <result property="faultVal" column="fault_val"/>
        <result property="minVal" column="min_val"/>
        <result property="maxVal" column="max_val"/>
        <result property="enumList" column="enum_list"/>
        <result property="showType" column="show_type"/>
        <result property="autoFault" column="auto_fault"/>
        <result property="faultTime" column="fault_time"/>
        <result property="autoBill" column="auto_bill"/>
        <result property="billTime" column="bill_time"/>
        <result property="billUser" column="bill_user"/>
        <result property="customMultiple" column="custom_multiple"/>
        <result property="alarm" column="alarm"/>
        <result property="attrName" column="attr_name"/>
        <result property="arrType" column="attr_type"/>
        <result property="attrClass" column="attr_class"/>
        <result property="attrCode" column="attr_code"/>
    </resultMap>

    <sql id="selectZhEquipmentPropVo">
        select id,
               equipment_id,
               attr_id,
               fault_val,
               min_val,
               max_val,
               enum_list,
               show_type,
               auto_fault,
               fault_time,
               auto_bill,
               bill_time,
               bill_user,
               custom_multiple,
               alarm
        from zh_equipment_prop
    </sql>

    <sql id="selectZhEquipmentPropVoLian">
        select t1.id,
               t1.equipment_id,
               t1.attr_id,
               t1.fault_val,
               t1.min_val,
               t1.max_val,
               t1.enum_list,
               t1.show_type,
               t1.auto_fault,
               t1.fault_time,
               t1.auto_bill,
               t1.bill_time,
               t1.bill_user,
               t1.custom_multiple,
               t1.alarm,
               t2.attr_name,
               t2.attr_type,
               t2.attr_class,
               t2.attr_code
        from zh_equipment_prop t1
        left join zh_device_type_attr t2 on t1.attr_id = t2.id
    </sql>

    <select id="selectZhEquipmentPropList" parameterType="ZhEquipmentProp" resultMap="ZhEquipmentPropResult">
        <include refid="selectZhEquipmentPropVo"/>
        <where>
            <if test="equipmentId != null ">and equipment_id = #{equipmentId}</if>
            <if test="attrId != null ">and attr_id = #{attrId}</if>
            <if test="faultVal != null  and faultVal != ''">and fault_val = #{faultVal}</if>
            <if test="minVal != null  and minVal != ''">and min_val = #{minVal}</if>
            <if test="maxVal != null  and maxVal != ''">and max_val = #{maxVal}</if>
            <if test="enumList != null  and enumList != ''">and enum_list = #{enumList}</if>
            <if test="showType != null  and showType != ''">and show_type = #{showType}</if>
            <if test="autoFault != null  and autoFault != ''">and auto_fault = #{autoFault}</if>
            <if test="faultTime != null ">and fault_time = #{faultTime}</if>
            <if test="autoBill != null ">and auto_bill = #{autoBill}</if>
            <if test="billTime != null ">and bill_time = #{billTime}</if>
            <if test="billUser != null  and billUser != ''">and bill_user = #{billUser}</if>
            <if test="customMultiple != null ">and custom_multiple = #{customMultiple}</if>
            <if test="alarm != null  and alarm != ''">and alarm = #{alarm}</if>
        </where>
    </select>

    <select id="selectZhEquipmentPropById" parameterType="Long" resultMap="ZhEquipmentPropResult">
        <include refid="selectZhEquipmentPropVo"/>
        where id = #{id}
    </select>

    <insert id="insertZhEquipmentProp" parameterType="ZhEquipmentProp" useGeneratedKeys="true" keyProperty="id">
        insert into zh_equipment_prop
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipmentId != null">equipment_id,</if>
            <if test="attrId != null">attr_id,</if>
            <if test="faultVal != null">fault_val,</if>
            <if test="minVal != null">min_val,</if>
            <if test="maxVal != null">max_val,</if>
            <if test="enumList != null">enum_list,</if>
            <if test="showType != null">show_type,</if>
            <if test="autoFault != null">auto_fault,</if>
            <if test="faultTime != null">fault_time,</if>
            <if test="autoBill != null">auto_bill,</if>
            <if test="billTime != null">bill_time,</if>
            <if test="billUser != null">bill_user,</if>
            <if test="customMultiple != null">custom_multiple,</if>
            <if test="alarm != null">alarm,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="equipmentId != null">#{equipmentId},</if>
            <if test="attrId != null">#{attrId},</if>
            <if test="faultVal != null">#{faultVal},</if>
            <if test="minVal != null">#{minVal},</if>
            <if test="maxVal != null">#{maxVal},</if>
            <if test="enumList != null">#{enumList},</if>
            <if test="showType != null">#{showType},</if>
            <if test="autoFault != null">#{autoFault},</if>
            <if test="faultTime != null">#{faultTime},</if>
            <if test="autoBill != null">#{autoBill},</if>
            <if test="billTime != null">#{billTime},</if>
            <if test="billUser != null">#{billUser},</if>
            <if test="customMultiple != null">#{customMultiple},</if>
            <if test="alarm != null">#{alarm},</if>
        </trim>
    </insert>

    <update id="updateZhEquipmentProp" parameterType="ZhEquipmentProp">
        update zh_equipment_prop
        <trim prefix="SET" suffixOverrides=",">
            <if test="equipmentId != null">equipment_id = #{equipmentId},</if>
            <if test="attrId != null">attr_id = #{attrId},</if>
            <if test="faultVal != null">fault_val = #{faultVal},</if>
            <if test="minVal != null">min_val = #{minVal},</if>
            <if test="maxVal != null">max_val = #{maxVal},</if>
            <if test="enumList != null">enum_list = #{enumList},</if>
            <if test="showType != null">show_type = #{showType},</if>
            <if test="autoFault != null">auto_fault = #{autoFault},</if>
            <if test="faultTime != null">fault_time = #{faultTime},</if>
            <if test="autoBill != null">auto_bill = #{autoBill},</if>
            <if test="billTime != null">bill_time = #{billTime},</if>
            <if test="billUser != null">bill_user = #{billUser},</if>
            <if test="customMultiple != null">custom_multiple = #{customMultiple},</if>
            <if test="alarm != null">alarm = #{alarm},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhEquipmentPropById" parameterType="Long">
        delete
        from zh_equipment_prop
        where id = #{id}
    </delete>

    <delete id="deleteZhEquipmentPropByIds" parameterType="String">
        delete from zh_equipment_prop where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectZhEquipmentPropByEquipmentIdAndAlarm" parameterType="Long" resultMap="ZhEquipmentPropLianResult">
        <include refid="selectZhEquipmentPropVoLian"/>
        where t1.equipment_id = #{id}
        and t1.alarm = '1'
    </select>
    <select id="selectZhEquipmentPropByAttrId" parameterType="Long" resultMap="ZhEquipmentPropResult">
        <include refid="selectZhEquipmentPropVo"/>
        where attr_id = #{id}
    </select>
</mapper>