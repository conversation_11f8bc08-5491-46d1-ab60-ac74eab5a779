package com.ruoyi.zhenghe.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.util.PinyinUtil;
import com.ruoyi.zhenghe.domain.ZhProduct;
import com.ruoyi.zhenghe.domain.dto.ProductOptionsTreeDto;
import com.ruoyi.zhenghe.mapper.ZhProductMapper;
import com.ruoyi.zhenghe.service.IZhProductService;
import com.ruoyi.zhenghe.service.IZhProductDefectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Service
public class ZhProductServiceImpl implements IZhProductService {
    @Autowired
    private ZhProductMapper zhProductMapper;

    @Autowired
    private IZhProductDefectService zhProductDefectService;

    /**
     * 查询产品信息
     *
     * @param id 产品信息主键
     * @return 产品信息
     */
    @Override
    public ZhProduct selectZhProductById(Long id) {
        return zhProductMapper.selectZhProductById(id);
    }

    /**
     * 查询产品信息列表
     *
     * @param zhProduct 产品信息
     * @return 产品信息
     */
    @Override
    public List<ZhProduct> selectZhProductList(ZhProduct zhProduct) {
        if(StringUtils.isNotEmpty(zhProduct.getCustomerName())){
            zhProduct.setCustomerName(zhProduct.getCustomerName().toUpperCase());
        }
        return zhProductMapper.selectZhProductList(zhProduct);
    }

    /**
     * 新增产品信息
     *
     * @param zhProduct 产品信息
     * @return 结果
     */
    @Override
    public int insertZhProduct(ZhProduct zhProduct) {
        // 生成客户名称拼音
        generateCustomerNamePinyin(zhProduct);

        zhProduct.setCreateTime(DateUtils.getNowDate());
        zhProduct.setCreateBy(SecurityUtils.getUsername());
        return zhProductMapper.insertZhProduct(zhProduct);
    }

    /**
     * 修改产品信息
     *
     * @param zhProduct 产品信息
     * @return 结果
     */
    @Override
    public int updateZhProduct(ZhProduct zhProduct) {
        // 生成客户名称拼音
        generateCustomerNamePinyin(zhProduct);

        zhProduct.setUpdateTime(DateUtils.getNowDate());
        zhProduct.setUpdateBy(SecurityUtils.getUsername());
        return zhProductMapper.updateZhProduct(zhProduct);
    }

    /**
     * 批量删除产品信息
     *
     * @param ids 需要删除的产品信息主键
     * @return 结果
     */
    @Override
    public int deleteZhProductByIds(Long[] ids) {
        // 先删除相关的瑕疵数据
        for (Long id : ids) {
            zhProductDefectService.deleteZhProductDefectByProductId(id);
        }
        return zhProductMapper.deleteZhProductByIds(ids);
    }

    /**
     * 删除产品信息信息
     *
     * @param id 产品信息主键
     * @return 结果
     */
    @Override
    public int deleteZhProductById(Long id) {
        // 先删除相关的瑕疵数据
        zhProductDefectService.deleteZhProductDefectByProductId(id);
        return zhProductMapper.deleteZhProductById(id);
    }



    /**
     * 获取所有产品型号列表
     *
     * @return 产品型号集合
     */
    @Override
    public List<String> selectAllProductModels(String productModel) {
        return zhProductMapper.selectAllProductModels(productModel);
    }

    /**
     * 获取所有片数列表
     *
     * @return 片数集合
     */
    @Override
    public List<Integer> selectAllPieceCounts() {
        return zhProductMapper.selectAllPieceCounts();
    }

    /**
     * 获取所有节数列表
     *
     * @return 节数集合
     */
    @Override
    public List<Integer> selectAllSectionCounts() {
        return zhProductMapper.selectAllSectionCounts();
    }

    /**
     * 获取所有正时标记列表
     *
     * @return 正时标记集合
     */
    @Override
    public List<String> selectAllTimingMarks() {
        return zhProductMapper.selectAllTimingMarks();
    }

    @Override
    public List<Integer> selectPieceCountsByProductModel(String productModel) {
        return zhProductMapper.selectPieceCountsByProductModel(productModel);
    }

    @Override
    public List<String> selectPieceCustomer(String customerName) {
        return zhProductMapper.selectPieceCustomer(customerName);
    }

    @Override
    public List<Integer> selectSectionCountsByProductModelAndPieceCount(String productModel, Integer pieceCount) {
        return zhProductMapper.selectSectionCountsByProductModelAndPieceCount(productModel, pieceCount);
    }

    @Override
    public List<String> selectTimingMarksByProductModelAndPieceCountAndSectionCount(String productModel, Integer pieceCount, Integer sectionCount) {
        return zhProductMapper.selectTimingMarksByProductModelAndPieceCountAndSectionCount(productModel, pieceCount, sectionCount);
    }

    /**
     * 根据组合条件查询产品
     *
     * @param productModel 产品型号
     * @param pieceCount 片数
     * @param sectionCount 节数
     * @param timingMark 正时标记
     * @return 产品信息
     */
    @Override
    public ZhProduct selectProductByCombination(String productModel, Integer pieceCount, Integer sectionCount, String timingMark) {
        ZhProduct product = zhProductMapper.selectProductByCombination(productModel, pieceCount, sectionCount, timingMark);
        if (product != null) {
            // 查询产品的瑕疵列表
            product.setDefectList(zhProductDefectService.selectZhProductDefectListByProductIdOpen(product.getId()));
        }
        return product;
    }

    /**
     * 获取产品选项树状结构
     *
     * @return 产品选项树状结构
     */
    @Override
    public List<ProductOptionsTreeDto> getProductOptionsTree() {
        // 查询所有有效的产品
        List<ZhProduct> allProducts = zhProductMapper.selectAllProductsForTree();

        // 按产品型号分组
        Map<String, List<ZhProduct>> productModelMap = allProducts.stream()
                .collect(Collectors.groupingBy(ZhProduct::getProductModel));

        List<ProductOptionsTreeDto> result = new ArrayList<>();

        for (Map.Entry<String, List<ZhProduct>> productModelEntry : productModelMap.entrySet()) {
            ProductOptionsTreeDto productModelDto = new ProductOptionsTreeDto();
            productModelDto.setProductModel(productModelEntry.getKey());

            // 按片数分组
            Map<Integer, List<ZhProduct>> pieceCountMap = productModelEntry.getValue().stream()
                    .collect(Collectors.groupingBy(ZhProduct::getPieceCount));

            List<ProductOptionsTreeDto.PieceCountOption> pieceCountOptions = new ArrayList<>();

            for (Map.Entry<Integer, List<ZhProduct>> pieceCountEntry : pieceCountMap.entrySet()) {
                ProductOptionsTreeDto.PieceCountOption pieceCountOption = new ProductOptionsTreeDto.PieceCountOption();
                pieceCountOption.setPieceCount(pieceCountEntry.getKey());

                // 按节数分组
                Map<Integer, List<ZhProduct>> sectionCountMap = pieceCountEntry.getValue().stream()
                        .collect(Collectors.groupingBy(ZhProduct::getSectionCount));

                List<ProductOptionsTreeDto.SectionCountOption> sectionCountOptions = new ArrayList<>();

                for (Map.Entry<Integer, List<ZhProduct>> sectionCountEntry : sectionCountMap.entrySet()) {
                    ProductOptionsTreeDto.SectionCountOption sectionCountOption = new ProductOptionsTreeDto.SectionCountOption();
                    sectionCountOption.setSectionCount(sectionCountEntry.getKey());

                    // 按正时标记分组
                    List<ProductOptionsTreeDto.TimingMarkOption> timingMarkOptions = sectionCountEntry.getValue().stream()
                            .map(product -> {
                                ProductOptionsTreeDto.TimingMarkOption timingMarkOption = new ProductOptionsTreeDto.TimingMarkOption();
                                timingMarkOption.setTimingMark(product.getTimingMark());
                                timingMarkOption.setProductId(product.getId());
                                timingMarkOption.setEnabled(true); // 查询的都是有效产品
                                return timingMarkOption;
                            })
                            .collect(Collectors.toList());

                    sectionCountOption.setTimingMarkOptions(timingMarkOptions);
                    sectionCountOptions.add(sectionCountOption);
                }

                // 按节数排序
                sectionCountOptions.sort(Comparator.comparing(ProductOptionsTreeDto.SectionCountOption::getSectionCount));
                pieceCountOption.setSectionCountOptions(sectionCountOptions);
                pieceCountOptions.add(pieceCountOption);
            }

            // 按片数排序
            pieceCountOptions.sort(Comparator.comparing(ProductOptionsTreeDto.PieceCountOption::getPieceCount));
            productModelDto.setPieceCountOptions(pieceCountOptions);
            result.add(productModelDto);
        }

        // 按产品型号排序
        result.sort(Comparator.comparing(ProductOptionsTreeDto::getProductModel));

        return result;
    }

    /**
     * 生成客户名称拼音
     *
     * @param zhProduct 产品信息
     */
    private void generateCustomerNamePinyin(ZhProduct zhProduct) {
        if (StringUtils.isNotEmpty(zhProduct.getCustomerName())) {
            // 生成客户名称的拼音全拼（不带空格）
            String pinyin = PinyinUtil.toPinyinWithoutSpace(zhProduct.getCustomerName());
            zhProduct.setPinyin(pinyin);
        }
    }
}
