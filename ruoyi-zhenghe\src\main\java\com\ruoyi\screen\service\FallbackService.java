package com.ruoyi.screen.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.screen.entity.ScreenEquipment;
import com.ruoyi.screen.entity.WDLErrorInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 降级和熔断服务
 * 当查询超时或失败时提供降级数据，避免接口完全失败
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
@Slf4j
@Service
public class FallbackService {

    // 熔断器状态记录
    private final Map<String, CircuitBreaker> circuitBreakers = new ConcurrentHashMap<>();
    
    // 降级数据缓存
    private final Map<String, Object> fallbackDataCache = new ConcurrentHashMap<>();

    /**
     * 获取设备数据降级结果
     */
    public JSONObject getDeviceInfoFallback(String deptId, String workshopId) {
        log.warn("设备数据接口降级: deptId={}, workshopId={}", deptId, workshopId);
        
        JSONObject ans = new JSONObject();
        
        // 提供基础的模拟数据
        ans.put("totalCount", 50);
        ans.put("onlineCount", 0);
        ans.put("offlineCount", 10);
        ans.put("workCount", 40);
        ans.put("errorCount", 2);
        
        ans.put("onlineRate", "80.000");
        ans.put("useRate", "100.000");
        ans.put("errorRate", "4.000");
        
        ans.put("totalDeviceList", generateMockDeviceList(50));
        ans.put("onlineDeviceList", null);
        ans.put("offlineDeviceList", generateMockDeviceList(10));
        ans.put("errorDeviceList", generateMockDeviceList(2));
        ans.put("workDeviceList", generateMockDeviceList(40));
        
        ans.put("fallback", true);
        ans.put("fallbackReason", "数据查询超时，返回降级数据");
        
        return ans;
    }

    /**
     * 获取设备分析降级结果
     */
    public List<ScreenEquipment> getDeviceDetailFallback(String deptId, String workshopId) {
        log.warn("设备分析接口降级: deptId={}, workshopId={}", deptId, workshopId);
        
        List<ScreenEquipment> ansList = new ArrayList<>();
        
        // 生成模拟设备数据
        for (int i = 1; i <= 10; i++) {
            ScreenEquipment equipment = new ScreenEquipment();
            equipment.setEquipmentName("设备" + i);
            equipment.setEquipmentCode("DEV" + String.format("%03d", i));
            equipment.setWorkRate(String.format("%.3f", Math.random() * 100));
            equipment.setWorkTime(String.format("%.1f", Math.random() * 480));
            equipment.setWorkCount(String.valueOf((int)(Math.random() * 1000)));
            equipment.setEquipmentImage("/default/equipment.png");
            ansList.add(equipment);
        }
        
        return ansList;
    }

    /**
     * 获取产量信息降级结果
     */
    public JSONObject getYieldFallback(String deptId, String workshopId) {
        log.warn("产量信息接口降级: deptId={}, workshopId={}", deptId, workshopId);
        
        JSONObject ans = new JSONObject();
        List<String> dateList = new ArrayList<>();
        List<Integer> yileList = new ArrayList<>();
        
        // 生成过去7天的模拟数据
        for (int i = 6; i >= 0; i--) {
            String format = DateUtil.format(DateUtil.offsetDay(new Date(), -i), "MM-dd");
            dateList.add(format);
            yileList.add((int)(Math.random() * 500 + 100));
        }
        
        ans.put("dateList", dateList);
        ans.put("yileList", yileList);
        ans.put("fallback", true);
        ans.put("fallbackReason", "产量数据查询超时，返回降级数据");
        
        return ans;
    }

    /**
     * 获取小时产量降级结果
     */
    public JSONObject getYieldHourFallback(String deptId, String workshopId) {
        log.warn("小时产量接口降级: deptId={}, workshopId={}", deptId, workshopId);
        
        JSONObject ans = new JSONObject();
        List<String> dateList = new ArrayList<>();
        List<Integer> yileList = new ArrayList<>();
        
        // 生成过去5小时的模拟数据
        for (int i = 4; i >= 0; i--) {
            String format = DateUtil.format(DateUtil.offsetHour(new Date(), -i), "HH") + ":00";
            dateList.add(format);
            yileList.add((int)(Math.random() * 100 + 20));
        }
        
        ans.put("dateList", dateList);
        ans.put("yileList", yileList);
        ans.put("fallback", true);
        
        return ans;
    }

    /**
     * 获取工序产量降级结果
     */
    public JSONObject getYieldProcessFallback(String deptId, String workshopId) {
        log.warn("工序产量接口降级: deptId={}, workshopId={}", deptId, workshopId);
        
        JSONObject ans = new JSONObject();
        List<String> processList = Arrays.asList("加工", "装配", "检测", "包装");
        List<Integer> yileList = Arrays.asList(150, 120, 180, 90);
        
        ans.put("processList", processList);
        ans.put("yileList", yileList);
        ans.put("fallback", true);
        
        return ans;
    }

    /**
     * 获取能耗分析降级结果
     */
    public JSONObject getEnergyFallback(String deptId, String workshopId) {
        log.warn("能耗分析接口降级: deptId={}, workshopId={}", deptId, workshopId);
        
        JSONObject ans = new JSONObject();
        List<String> dateList = new ArrayList<>();
        List<Integer> energySum = new ArrayList<>();
        Map<String, List<Integer>> workshopEnergyMap = new HashMap<>();
        
        // 生成过去7天的模拟能耗数据
        for (int i = 6; i >= 0; i--) {
            String format = DateUtil.format(DateUtil.offsetDay(new Date(), -i), "MM-dd");
            dateList.add(format);
            energySum.add((int)(Math.random() * 1000 + 500));
        }
        
        // 模拟车间能耗数据
        List<String> workshops = Arrays.asList("车间A", "车间B", "车间C");
        for (String workshop : workshops) {
            List<Integer> workshopEnergy = new ArrayList<>();
            for (int i = 0; i < 7; i++) {
                workshopEnergy.add((int)(Math.random() * 300 + 100));
            }
            workshopEnergyMap.put(workshop, workshopEnergy);
        }
        
        ans.put("dateList", dateList);
        ans.put("energySum", energySum);
        ans.put("workshopEnergyMap", workshopEnergyMap);
        ans.put("fallback", true);
        
        return ans;
    }

    /**
     * 获取错误信息降级结果
     */
    public List<WDLErrorInfo> getErrorInfoFallback(String deptId, String workshopId) {
        log.warn("错误信息接口降级: deptId={}, workshopId={}", deptId, workshopId);
        
        List<WDLErrorInfo> errorList = new ArrayList<>();
        
        // 生成模拟报警信息
        String[] errorTypes = {"温度过高", "压力异常", "振动超标", "电流异常"};
        String[] deviceNames = {"设备001", "设备002", "设备003"};
        
        for (int i = 0; i < 3; i++) {
            WDLErrorInfo errorInfo = new WDLErrorInfo();
            errorInfo.setErrorInfo(errorTypes[i % errorTypes.length]);
            errorInfo.setDeviceName(deviceNames[i % deviceNames.length]);
            errorInfo.setErrorTime(DateUtil.format(DateUtil.offsetMinute(new Date(), -i * 10), "yyyy-MM-dd HH:mm:ss"));
            errorList.add(errorInfo);
        }
        
        return errorList;
    }

    /**
     * 检查是否应该熔断
     */
    public boolean shouldCircuitBreak(String interfaceName) {
        CircuitBreaker breaker = circuitBreakers.computeIfAbsent(interfaceName, k -> new CircuitBreaker());
        return breaker.shouldBreak();
    }

    /**
     * 记录接口调用成功
     */
    public void recordSuccess(String interfaceName) {
        CircuitBreaker breaker = circuitBreakers.get(interfaceName);
        if (breaker != null) {
            breaker.recordSuccess();
        }
    }

    /**
     * 记录接口调用失败
     */
    public void recordFailure(String interfaceName) {
        CircuitBreaker breaker = circuitBreakers.computeIfAbsent(interfaceName, k -> new CircuitBreaker());
        breaker.recordFailure();
    }

    /**
     * 生成模拟设备列表
     */
    private List<String> generateMockDeviceList(int count) {
        List<String> deviceList = new ArrayList<>();
        for (int i = 1; i <= count; i++) {
            deviceList.add("设备" + i + "-DEV" + String.format("%03d", i));
        }
        return deviceList;
    }

    /**
     * 简单的熔断器实现
     */
    private static class CircuitBreaker {
        private final AtomicInteger failureCount = new AtomicInteger(0);
        private final AtomicInteger successCount = new AtomicInteger(0);
        private volatile long lastFailureTime = 0;
        private volatile boolean isOpen = false;
        
        private static final int FAILURE_THRESHOLD = 5; // 失败阈值
        private static final long TIMEOUT = 60000; // 熔断超时时间（1分钟）
        
        public boolean shouldBreak() {
            if (isOpen) {
                // 检查是否可以尝试恢复
                if (System.currentTimeMillis() - lastFailureTime > TIMEOUT) {
                    isOpen = false;
                    failureCount.set(0);
                    return false;
                }
                return true;
            }
            
            return failureCount.get() >= FAILURE_THRESHOLD;
        }
        
        public void recordSuccess() {
            successCount.incrementAndGet();
            if (isOpen && successCount.get() >= 3) {
                // 连续成功3次后关闭熔断器
                isOpen = false;
                failureCount.set(0);
            }
        }
        
        public void recordFailure() {
            int failures = failureCount.incrementAndGet();
            lastFailureTime = System.currentTimeMillis();
            
            if (failures >= FAILURE_THRESHOLD) {
                isOpen = true;
            }
        }
    }

    /**
     * 获取熔断器状态
     */
    public Map<String, Object> getCircuitBreakerStatus() {
        Map<String, Object> status = new HashMap<>();
        for (Map.Entry<String, CircuitBreaker> entry : circuitBreakers.entrySet()) {
            CircuitBreaker breaker = entry.getValue();
            Map<String, Object> breakerStatus = new HashMap<>();
            breakerStatus.put("isOpen", breaker.isOpen);
            breakerStatus.put("failureCount", breaker.failureCount.get());
            breakerStatus.put("successCount", breaker.successCount.get());
            status.put(entry.getKey(), breakerStatus);
        }
        return status;
    }
}
