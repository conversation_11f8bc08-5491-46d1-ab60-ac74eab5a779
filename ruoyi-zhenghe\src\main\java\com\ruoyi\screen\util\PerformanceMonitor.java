package com.ruoyi.screen.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能监控工具类
 * 用于监控接口响应时间和调用次数
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
@Slf4j
@Component
public class PerformanceMonitor {
    
    // 存储各接口的调用统计
    private final ConcurrentHashMap<String, InterfaceStats> statsMap = new ConcurrentHashMap<>();
    
    /**
     * 记录接口调用开始时间
     * 
     * @param interfaceName 接口名称
     * @return 开始时间戳
     */
    public long startTiming(String interfaceName) {
        return System.currentTimeMillis();
    }
    
    /**
     * 记录接口调用结束时间并统计
     * 
     * @param interfaceName 接口名称
     * @param startTime 开始时间戳
     */
    public void endTiming(String interfaceName, long startTime) {
        long duration = System.currentTimeMillis() - startTime;
        
        InterfaceStats stats = statsMap.computeIfAbsent(interfaceName, k -> new InterfaceStats());
        stats.addCall(duration);
        
        // 如果响应时间超过阈值，记录警告日志
        if (duration > getThreshold(interfaceName)) {
            log.warn("接口 {} 响应时间过长: {}ms", interfaceName, duration);
        } else {
            log.info("接口 {} 响应时间: {}ms", interfaceName, duration);
        }
    }
    
    /**
     * 获取接口统计信息
     * 
     * @param interfaceName 接口名称
     * @return 统计信息
     */
    public InterfaceStats getStats(String interfaceName) {
        return statsMap.get(interfaceName);
    }
    
    /**
     * 获取所有接口统计信息
     * 
     * @return 所有统计信息
     */
    public ConcurrentHashMap<String, InterfaceStats> getAllStats() {
        return new ConcurrentHashMap<>(statsMap);
    }
    
    /**
     * 清空统计信息
     */
    public void clearStats() {
        statsMap.clear();
    }
    
    /**
     * 获取接口响应时间阈值
     * 
     * @param interfaceName 接口名称
     * @return 阈值（毫秒）
     */
    private long getThreshold(String interfaceName) {
        switch (interfaceName) {
            case "deviceInfo":
            case "deviceDetail":
            case "errorInfo":
                return 30000; // 30秒
            case "yield":
            case "yieldHour":
            case "yieldWorkshop":
                return 60000; // 60秒
            case "energy2":
                return 120000; // 120秒
            default:
                return 30000; // 默认30秒
        }
    }
    
    /**
     * 接口统计信息类
     */
    public static class InterfaceStats {
        private final AtomicLong totalCalls = new AtomicLong(0);
        private final AtomicLong totalDuration = new AtomicLong(0);
        private final AtomicLong maxDuration = new AtomicLong(0);
        private final AtomicLong minDuration = new AtomicLong(Long.MAX_VALUE);
        private final AtomicLong slowCalls = new AtomicLong(0); // 慢调用次数
        
        public void addCall(long duration) {
            totalCalls.incrementAndGet();
            totalDuration.addAndGet(duration);
            
            // 更新最大值
            long currentMax = maxDuration.get();
            while (duration > currentMax && !maxDuration.compareAndSet(currentMax, duration)) {
                currentMax = maxDuration.get();
            }
            
            // 更新最小值
            long currentMin = minDuration.get();
            while (duration < currentMin && !minDuration.compareAndSet(currentMin, duration)) {
                currentMin = minDuration.get();
            }
            
            // 统计慢调用（超过30秒）
            if (duration > 30000) {
                slowCalls.incrementAndGet();
            }
        }
        
        public long getTotalCalls() {
            return totalCalls.get();
        }
        
        public long getAverageDuration() {
            long calls = totalCalls.get();
            return calls > 0 ? totalDuration.get() / calls : 0;
        }
        
        public long getMaxDuration() {
            return maxDuration.get();
        }
        
        public long getMinDuration() {
            long min = minDuration.get();
            return min == Long.MAX_VALUE ? 0 : min;
        }
        
        public long getSlowCalls() {
            return slowCalls.get();
        }
        
        public double getSlowCallRate() {
            long total = totalCalls.get();
            return total > 0 ? (double) slowCalls.get() / total * 100 : 0;
        }
        
        @Override
        public String toString() {
            return String.format(
                "InterfaceStats{totalCalls=%d, avgDuration=%dms, maxDuration=%dms, minDuration=%dms, slowCalls=%d, slowCallRate=%.2f%%}",
                getTotalCalls(), getAverageDuration(), getMaxDuration(), getMinDuration(), getSlowCalls(), getSlowCallRate()
            );
        }
    }
}
