package com.ruoyi.zhenghe.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 车间管理对象 zh_workshop
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@ApiModel(value = "ZhWorkshop",description = "车间管理对象")
public class ZhWorkshop extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
//    @Excel(name = "工厂车间id",sort = 1)
    private Long id;

    /** 工厂车间名称 */
    @ApiModelProperty(value = "工厂车间名称")
    @Excel(name = "工厂车间名称",sort = 2)
    private String workshopName;

    /** 车间编码 */
    @ApiModelProperty(value = "车间编码")
    @Excel(name = "车间编码",sort = 3)
    private String workshopCode;

    /** 父级id 父级为0 */
    @ApiModelProperty(value = "父级id")
//    @Excel(name = "父级id 父级为0",sort = 4)
    private Long parentId;

    /** 部门id */
    private Long deptId;

    /** 子级 */
    private List<ZhWorkshop> children;

    /** 部门名称 */
    @Excel(name = "部门名称",sort = 4)
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /** 车间总计数 */
    @ApiModelProperty(value = "车间总计数")
    private Integer totalCount;

    /** 设备类型id */
    private Long deviceTypeId;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "车间电量")

    private Double workshopPower;

    @ApiModelProperty(value = "平段电量")
    private Double flatPower;

    @ApiModelProperty(value = "谷段电量")
    private Double valleyPower;

    @ApiModelProperty(value = "峰段电量")
    private Double crestPower;

    @ApiModelProperty(value = "深谷电量")
    private Double barrancaPower;

    @ApiModelProperty(value = "尖峰电量")
    private Double spikePower;

    public Double getFlatPower() {
        return flatPower;
    }

    public void setFlatPower(Double flatPower) {
        this.flatPower = flatPower;
    }

    public Double getValleyPower() {
        return valleyPower;
    }

    public void setValleyPower(Double valleyPower) {
        this.valleyPower = valleyPower;
    }

    public Double getCrestPower() {
        return crestPower;
    }

    public void setCrestPower(Double crestPower) {
        this.crestPower = crestPower;
    }

    public Double getBarrancaPower() {
        return barrancaPower;
    }

    public void setBarrancaPower(Double barrancaPower) {
        this.barrancaPower = barrancaPower;
    }

    public Double getSpikePower() {
        return spikePower;
    }

    public void setSpikePower(Double spikePower) {
        this.spikePower = spikePower;
    }

    public Double getWorkshopPower() {
        return workshopPower;
    }

    public void setWorkshopPower(Double workshopPower) {
        this.workshopPower = workshopPower;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Long getDeviceTypeId() {
        return deviceTypeId;
    }

    public void setDeviceTypeId(Long deviceTypeId) {
        this.deviceTypeId = deviceTypeId;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public List<ZhWorkshop> getChildren() {
        return children;
    }

    public void setChildren(List<ZhWorkshop> children) {
        this.children = children;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setWorkshopName(String workshopName) 
    {
        this.workshopName = workshopName;
    }

    public String getWorkshopName() 
    {
        return workshopName;
    }

    public void setWorkshopCode(String workshopCode) 
    {
        this.workshopCode = workshopCode;
    }

    public String getWorkshopCode() 
    {
        return workshopCode;
    }

    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("workshopName", getWorkshopName())
            .append("workshopCode", getWorkshopCode())
            .append("remark", getRemark())
            .append("parentId", getParentId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
