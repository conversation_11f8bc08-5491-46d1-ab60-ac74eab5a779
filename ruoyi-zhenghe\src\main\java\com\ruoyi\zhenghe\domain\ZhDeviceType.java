package com.ruoyi.zhenghe.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备类型/Iot物模型对象 zh_device_type
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
public class ZhDeviceType extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @Excel(name = "设备类型id", sort = 0)
    @ApiModelProperty(value = "设备类型id")
    private Long id;

    /**
     * 物模型名称
     */
    @Excel(name = "设备类型", sort = 1)
    @ApiModelProperty(value = "设备类型")
    private String tslName;

    /**
     * 物模型编码
     */
    @Excel(name = "类型编码", sort = 2)
    @ApiModelProperty(value = "类型编码")
    private String tslCode;

    /**
     * 设备类型描述
     */
//    @Excel(name = "设备类型描述")
    @ApiModelProperty(value = "设备类型描述")
    private String tslDesc;

    /**
     * 设备类型图片
     */
    @ApiModelProperty(value = "设备类型图片")
    private String tslImg;

    /**
     * 序号
     */
//    @Excel(name = "序号")
    @ApiModelProperty(value = "排序号")
    private Long tslSort;

    /**
     * $column.columnComment
     */
    private Long deptId;

    @Excel(name = "所属事业部", sort = 3)
    @ApiModelProperty(value = "所属事业部")
    private String deptName;

    /**
     * $column.columnComment
     */
    private String ex1;

    /**
     * MQ主题
     */
    @ApiModelProperty(value = "MQ主题")
    private String mqTopic;


}

