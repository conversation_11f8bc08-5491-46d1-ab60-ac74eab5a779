package com.ruoyi.zhenghe.service.impl;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.zhenghe.domain.ZhDeviceType;
import com.ruoyi.zhenghe.mapper.ZhDeviceTypeMapper;
import com.ruoyi.zhenghe.service.IZhDeviceTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备类型/Iot物模型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Service
public class ZhDeviceTypeServiceImpl implements IZhDeviceTypeService {
    @Autowired
    private ZhDeviceTypeMapper zhDeviceTypeMapper;

    /**
     * 查询设备类型/Iot物模型
     *
     * @param id 设备类型/Iot物模型主键
     * @return 设备类型/Iot物模型
     */
    @Override
    public ZhDeviceType selectZhDeviceTypeById(Long id) {
        return zhDeviceTypeMapper.selectZhDeviceTypeById(id);
    }

    /**
     * 查询设备类型/Iot物模型列表
     *
     * @param zhDeviceType 设备类型/Iot物模型
     * @return 设备类型/Iot物模型
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<ZhDeviceType> selectZhDeviceTypeList(ZhDeviceType zhDeviceType) {
        return zhDeviceTypeMapper.selectZhDeviceTypeList(zhDeviceType);
    }

    /**
     * 新增设备类型/Iot物模型
     *
     * @param zhDeviceType 设备类型/Iot物模型
     * @return 结果
     */
    @Override
    public int insertZhDeviceType(ZhDeviceType zhDeviceType) {
        zhDeviceType.setCreateTime(DateUtils.getNowDate());
        zhDeviceType.setCreateBy(SecurityUtils.getUsername());
        if (zhDeviceType.getDeptId() == null) {
            zhDeviceType.setDeptId(SecurityUtils.getDeptId());
        }
        return zhDeviceTypeMapper.insertZhDeviceType(zhDeviceType);
    }

    /**
     * 修改设备类型/Iot物模型
     *
     * @param zhDeviceType 设备类型/Iot物模型
     * @return 结果
     */
    @Override
    public int updateZhDeviceType(ZhDeviceType zhDeviceType) {
        zhDeviceType.setUpdateTime(DateUtils.getNowDate());
        return zhDeviceTypeMapper.updateZhDeviceType(zhDeviceType);
    }

    /**
     * 批量删除设备类型/Iot物模型
     *
     * @param ids 需要删除的设备类型/Iot物模型主键
     * @return 结果
     */
    @Override
    public int deleteZhDeviceTypeByIds(Long[] ids) {
        return zhDeviceTypeMapper.deleteZhDeviceTypeByIds(ids);
    }

    /**
     * 删除设备类型/Iot物模型信息
     *
     * @param id 设备类型/Iot物模型主键
     * @return 结果
     */
    @Override
    public int deleteZhDeviceTypeById(Long id) {
        return zhDeviceTypeMapper.deleteZhDeviceTypeById(id);
    }
}
