<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhAlarmLevelMapper">

    <resultMap type="ZhAlarmLevel" id="ZhAlarmLevelResult">
        <result property="id"    column="id"    />
        <result property="alarmName"    column="alarm_name"    />
        <result property="alarmCode"    column="alarm_code"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_Name"    />
    </resultMap>

    <sql id="selectZhAlarmLevelVo">
        SELECT
            a.id,
            a.alarm_name,
            a.alarm_code,
            a.remark,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time,
            a.dept_id,
            d.dept_name
        FROM
            zh_alarm_level a
                LEFT JOIN sys_dept d ON a.dept_id = d.dept_id
    </sql>

    <select id="selectZhAlarmLevelList" parameterType="ZhAlarmLevel" resultMap="ZhAlarmLevelResult">
        <include refid="selectZhAlarmLevelVo"/>
        <where>
            1 = 1
            <if test="alarmName != null  and alarmName != ''"> and a.alarm_name like concat('%', #{alarmName}, '%')</if>
            <if test="alarmCode != null  and alarmCode != ''"> and a.alarm_code = #{alarmCode}</if>
        </where>
        ${params.dataScope}
        order by
        a.create_time desc
    </select>

    <select id="selectZhAlarmLevelById" parameterType="Long" resultMap="ZhAlarmLevelResult">
        <include refid="selectZhAlarmLevelVo"/>
        where a.id = #{id}
    </select>

    <insert id="insertZhAlarmLevel" parameterType="ZhAlarmLevel" useGeneratedKeys="true" keyProperty="id">
        insert into zh_alarm_level
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="alarmName != null and alarmName != ''">alarm_name,</if>
            <if test="alarmCode != null and alarmCode != ''">alarm_code,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deptId != null">dept_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="alarmName != null and alarmName != ''">#{alarmName},</if>
            <if test="alarmCode != null and alarmCode != ''">#{alarmCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deptId != null">#{deptId},</if>
        </trim>
    </insert>

    <update id="updateZhAlarmLevel" parameterType="ZhAlarmLevel">
        update zh_alarm_level
        <trim prefix="SET" suffixOverrides=",">
            <if test="alarmName != null and alarmName != ''">alarm_name = #{alarmName},</if>
            <if test="alarmCode != null and alarmCode != ''">alarm_code = #{alarmCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhAlarmLevelById" parameterType="Long">
        delete from zh_alarm_level where id = #{id}
    </delete>

    <delete id="deleteZhAlarmLevelByIds" parameterType="String">
        delete from zh_alarm_level where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>