<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhAlarmMessageMapper">

    <resultMap type="ZhAlarmMessage" id="ZhAlarmMessageResult">
        <result property="id"    column="id"    />
        <result property="alarmConfigurationId"    column="alarm_configuration_id"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="sustainTime"    column="sustain_time"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectZhAlarmMessageVo">
        select id, alarm_configuration_id, start_time, end_time, sustain_time, dept_id, create_by, create_time, update_by, update_time from zh_alarm_message
    </sql>

    <sql id="selectZhAlarmMessageVoLian">
        select t1.*,
        t3.equipment_name as equipmentName,
        t3.equipment_code as equipmentCode,
        t4.dept_name as deptName,
        t5.workshop_name as workshopName,
        t6.tsl_name as deviceTypeName,
        t7.alarm_name as alarmName,
        t2.alarm_content as alarmContent,
        t2.alarm_suggestion as alarmSuggestion
        from zh_alarm_message t1
        left join zh_alarm_configuration t2 on t1.alarm_configuration_id = t2.id
        left join zh_iot_equipment t3 on t2.equipment_id = t3.id
        left join sys_dept t4 on t3.dept_id = t4.dept_id
        left join zh_workshop t5 on t3.workshop_id = t5.id
        left join zh_device_type t6 on t3.device_type_id = t6.id
        left join zh_alarm_level t7 on t2.alarm_level_id = t7.id
    </sql>


    <select id="selectZhAlarmMessageList" parameterType="ZhAlarmMessage" resultMap="ZhAlarmMessageResult">
        <include refid="selectZhAlarmMessageVoLian"/>
        <where>
            <if test="alarmConfigurationId != null "> and t1.alarm_configuration_id = #{alarmConfigurationId}</if>
            <if test="startTime != null "> and t1.start_time <![CDATA[>= ]]> #{startTime}</if>
            <if test="endTime != null "> and t1.end_time <![CDATA[<= ]]> #{startTime}</if>
            <if test="sustainTime != null "> and t1.sustain_time = #{sustainTime}</if>
            <if test="deptId != null "> and t3.dept_id = #{deptId}</if>
            <if test="workshopId != null "> and t3.workshop_id = #{workshopId}</if>
            <if test="deviceTypeId != null "> and t3.device_type_id = #{deviceTypeId}</if>
        </where>
        order by t1.end_time desc
    </select>

    <select id="selectZhAlarmMessageById" parameterType="Long" resultMap="ZhAlarmMessageResult">
        <include refid="selectZhAlarmMessageVo"/>
        where id = #{id}
    </select>

    <insert id="insertZhAlarmMessage" parameterType="ZhAlarmMessage" useGeneratedKeys="true" keyProperty="id">
        insert into zh_alarm_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="alarmConfigurationId != null">alarm_configuration_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="sustainTime != null">sustain_time,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="alarmConfigurationId != null">#{alarmConfigurationId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="sustainTime != null">#{sustainTime},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateZhAlarmMessage" parameterType="ZhAlarmMessage">
        update zh_alarm_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="alarmConfigurationId != null">alarm_configuration_id = #{alarmConfigurationId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="sustainTime != null">sustain_time = #{sustainTime},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhAlarmMessageById" parameterType="Long">
        delete from zh_alarm_message where id = #{id}
    </delete>

    <delete id="deleteZhAlarmMessageByIds" parameterType="String">
        delete from zh_alarm_message where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectZhAlarmMessageCountByAlarmConfigurationId" resultType="integer">
        select count(*)
        from zh_alarm_message
        where alarm_configuration_id = #{alarmConfigurationId}
        and end_time is null
        order by id desc
        limit 1
    </select>

    <select id="selectZhAlarmMessageByAlarmConfigurationId" parameterType="Long" resultMap="ZhAlarmMessageResult">
        <include refid="selectZhAlarmMessageVo"/>
        where alarm_configuration_id = #{alarmConfigurationId}
        and end_time is null
        order by id desc
        limit 1
    </select>
</mapper>