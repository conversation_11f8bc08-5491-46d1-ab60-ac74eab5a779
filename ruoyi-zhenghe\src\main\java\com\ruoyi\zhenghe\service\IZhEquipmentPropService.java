package com.ruoyi.zhenghe.service;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhEquipmentProp;

/**
 * 设备属性配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface IZhEquipmentPropService 
{
    /**
     * 查询设备属性配置
     * 
     * @param id 设备属性配置主键
     * @return 设备属性配置
     */
//    public ZhEquipmentProp selectZhEquipmentPropById(Long id);

    /**
     * 查询设备属性配置列表
     * 
     * @param zhEquipmentProp 设备属性配置
     * @return 设备属性配置集合
     */
    public List<ZhEquipmentProp> selectZhEquipmentPropList(ZhEquipmentProp zhEquipmentProp);

    public List<ZhEquipmentProp> selectZhEquipmentPropByEquipmentId(Long id);

    /**
     * 新增设备属性配置
     * 
     * @param zhEquipmentProp 设备属性配置
     * @return 结果
     */
    public int insertZhEquipmentProp(ZhEquipmentProp zhEquipmentProp);

    /**
     * 修改设备属性配置
     * 
     * @param zhEquipmentProp 设备属性配置
     * @return 结果
     */
    public int updateZhEquipmentProp(ZhEquipmentProp zhEquipmentProp);

    /**
     * 批量删除设备属性配置
     * 
     * @param ids 需要删除的设备属性配置主键集合
     * @return 结果
     */
    public int deleteZhEquipmentPropByIds(Long[] ids);

    /**
     * 删除设备属性配置信息
     * 
     * @param id 设备属性配置主键
     * @return 结果
     */
    public int deleteZhEquipmentPropById(Long id);

    /**
     * 根据设备id查询设备已配置属性中勾选了报警配置的属性
     *
     * @param id 设备属性配置主键
     * @return 结果
     */
    public List<ZhEquipmentProp> selectZhEquipmentPropByEquipmentIdAndAlarm(Long id);
}
