package com.ruoyi.zhengheiot.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONArray;
import com.ruoyi.ConstantZH;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.util.DataTimeUtil;
import com.ruoyi.util.iotdb.IoTDBUtil;
import com.ruoyi.zhenghe.domain.ZhDeviceTypeAttr;
import com.ruoyi.zhenghe.domain.ZhEquipmentProp;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.domain.ZhWorkshop;
import com.ruoyi.zhenghe.mapper.ZhDeviceTypeAttrMapper;
import com.ruoyi.zhenghe.mapper.ZhEquipmentPropMapper;
import com.ruoyi.zhenghe.mapper.ZhIotEquipmentMapper;
import com.ruoyi.zhenghe.mapper.ZhWorkshopMapper;
import com.ruoyi.zhengheiot.domain.DeviceNesting;
import com.ruoyi.zhengheiot.service.IotHistoryDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


@Slf4j
@Service
public class IotHistoryDataServiceImpl implements IotHistoryDataService {

    private static final String TENANT = Constants.TENANT;

    @Resource
    IoTDBUtil iotDBUtil;

    @Resource
    ZhIotEquipmentMapper equipmentMapper;
    @Resource
    ZhDeviceTypeAttrMapper deviceTypeAttrMapper;

    @Resource
    private SysDeptMapper deptMapper;

    @Resource
    private ZhWorkshopMapper zhWorkshopMapper;

    @Resource
    private ZhEquipmentPropMapper zhEquipmentPropMapper;

    @Autowired
    private ISysDeptService deptService;

    /**
     * 获取设备的历史数据
     *
     * @param id        设备的唯一标识符
     * @param startTime 查询的开始时间，格式应为 'yyyy-MM-dd HH:mm:ss'
     * @param endTime   查询的结束时间，格式应为 'yyyy-MM-dd HH:mm:ss'
     * @return JSONObject 包含设备历史数据的JSON对象，包含属性和数据列表
     * @throws IllegalArgumentException 如果时间格式不正确或设备ID不存在
     * @throws RuntimeException         如果IoTDB连接或执行异常，或数据处理异常
     */
    @Override
    public JSONObject getHistoryData(Long id, String startTime, String endTime, Integer pageNum, Integer pageSize) {
        // 校验时间格式
        if (!DataTimeUtil.isValidTimeFormat(startTime) || !DataTimeUtil.isValidTimeFormat(endTime)) {
            throw new IllegalArgumentException("时间格式不正确，应为 'yyyy-MM-dd HH:mm:ss'");
        }

        // 将时间格式化
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        startTime = sdf.format(DateUtil.parse(startTime));
        endTime = sdf.format(DateUtil.parse(endTime));
        if (startTime.compareTo(endTime) > 0) {
            throw new IllegalArgumentException("开始时间不能大于结束时间");
        }

        JSONObject result = new JSONObject();
        ZhIotEquipment equipment = equipmentMapper.selectZhIotEquipmentById(id);
        if (equipment == null) {
            throw new IllegalArgumentException("设备ID不存在");
        }

        String equipmentCode = equipment.getEquipmentCode();
        List<String> tagList = new ArrayList<>();
        List<ZhDeviceTypeAttr> attrs = Collections.emptyList();

        // 查询设备类型属性
        ZhDeviceTypeAttr query = new ZhDeviceTypeAttr();
        query.setTslId(equipment.getDeviceTypeId());
        attrs = deviceTypeAttrMapper.selectZhDeviceTypeAttrList(query);
        result.put("attrs", attrs);
        for (ZhDeviceTypeAttr attr : attrs) {
            tagList.add(attr.getAttrCode());
        }

        JSONArray list;
        try {
            // 从IoTDB获取历史数据
//            list = iotDBUtil.listDataHistoryNew(TENANT, equipmentCode, tagList, startTime, endTime, pageNum, pageSize);
            list = iotDBUtil.listDataHistoryList(TENANT, equipmentCode, tagList, startTime, endTime, pageNum, pageSize);
        } catch (IoTDBConnectionException e) {
            throw new RuntimeException("IoTDB连接异常", e);
        } catch (StatementExecutionException e) {
            throw new RuntimeException("IoTDB执行异常", e);
        }

        // 处理数值类型的属性数据,替换属性值
        for (ZhDeviceTypeAttr attr : attrs) {

            ZhEquipmentProp queryProp = new ZhEquipmentProp();
            queryProp.setEquipmentId(id);
            queryProp.setAttrId(attr.getId());
            final List<ZhEquipmentProp> zhEquipmentProps = zhEquipmentPropMapper.selectZhEquipmentPropList(queryProp);
            if (zhEquipmentProps != null && zhEquipmentProps.size() > 0) {

                ZhEquipmentProp zhEquipmentProp = zhEquipmentProps.get(0);

                // 获取故障值
                final String faultVal = zhEquipmentProp.getFaultVal();

                if (zhEquipmentProp != null) {
                    for (int i = 0; i < list.size(); i++) {
                        com.alibaba.fastjson2.JSONObject temp = list.getJSONObject(i);

                        // 处理枚举类型
                        if (zhEquipmentProp.getEnumList() != null) {
                            try {
                                for (String tag : temp.keySet()) {
                                    if (attr.getAttrCode().equalsIgnoreCase(tag)) {

                                        JSONObject jsonObject = JSONUtil.parseObj(zhEquipmentProp.getEnumList());
                                        String value = (String) jsonObject.get(temp.getInteger(tag) + "");

                                        temp.put(tag, value);
                                        list.set(i, temp);
                                    }
                                }
                            } catch (Exception e) {
//                                throw new RuntimeException("数据处理异常", e);
                            }
                        }
                        // 处理故障值
                        if (StringUtils.isNotEmpty(faultVal)) {
                            try {
                                for (String tag : temp.keySet()) {
                                    if (attr.getAttrCode().equalsIgnoreCase(tag)) {
                                        String value = faultVal.equals(temp.getInteger(tag) + "") ? ConstantZH.FAULT : ConstantZH.NORMAL;
                                        temp.put(tag, value);
                                        list.set(i, temp);
                                    }
                                }
                            } catch (Exception e) {
//                                throw new RuntimeException("数据处理异常", e);
                            }
                        }

                    }
                }
            }

//倍率转换
            if (attr != null && (attr.getAttrType().equalsIgnoreCase("double") || attr.getAttrType().equalsIgnoreCase("int"))) {
                for (int i = 0; i < list.size(); i++) {
                    com.alibaba.fastjson2.JSONObject temp = list.getJSONObject(i);
                    for (String tag : temp.keySet()) {
                        if (attr.getAttrCode().equalsIgnoreCase(tag)) {
                            try {
                                temp.put(tag, NumberUtil.round(Convert.toDouble(temp.getString(tag)) * Convert.toDouble(attr.getAttrMultiple()), 3));
                                list.set(i, temp);
                                break;
                            } catch (Exception e) {
//                                throw new RuntimeException("数据处理异常", e);
                            }
                        }
                    }
                }
            }
        }
        result.put("total", DateUtil.between(DateUtil.parse(startTime), DateUtil.parse(endTime), DateUnit.MINUTE));
        try {
//            result.put("total", iotDBUtil.queryCountMax(TENANT, equipmentCode, startTime, endTime));
            result.put("total", list.size());
        } catch (Exception e) {
            log.error("queryCountMax查询总数失败", e);
        }
        result.put("list", list.subList((pageNum - 1) * pageSize, Math.min(list.size(), pageNum * pageSize)));
        return result;
    }

    @Override
    public JSONObject getHistoryDataTagList(Long id, String tag, String startTime, String endTime,Integer pageNum, Integer pageSize) {
        // 校验时间格式
        if (!DataTimeUtil.isValidTimeFormat(startTime) || !DataTimeUtil.isValidTimeFormat(endTime)) {
            throw new IllegalArgumentException("时间格式不正确，应为 'yyyy-MM-dd HH:mm:ss'");
        }
//        if (startTime.compareTo(endTime) > 0) {
//            throw new IllegalArgumentException("开始时间不能大于结束时间");
//        }
        ZhIotEquipment equipment = equipmentMapper.selectZhIotEquipmentById(id);
        if (equipment == null) {
            throw new IllegalArgumentException("设备ID不存在");
        }
        JSONObject result = new JSONObject();
        JSONArray list = null;
        String equipmentCode = equipment.getEquipmentCode();
        try {
            List<String> tagList = new ArrayList<>();
            tagList.add(tag);
            list = iotDBUtil.listDataHistoryNew(TENANT, equipmentCode, tagList, startTime, endTime, pageNum, pageSize);
        } catch (Exception e) {
//            throw new RuntimeException("数据处理异常", e);
        }

        // 查询设备类型属性
        ZhDeviceTypeAttr query = new ZhDeviceTypeAttr();
        query.setTslId(equipment.getDeviceTypeId());
        query.setAttrCode(tag);
        List<ZhDeviceTypeAttr> attrs = deviceTypeAttrMapper.selectZhDeviceTypeAttrList(query);

        // 处理数值类型的属性数据，进行倍数转换并四舍五入
        for (ZhDeviceTypeAttr attr : attrs) {
            if (attr != null && (attr.getAttrType().equalsIgnoreCase("double") || attr.getAttrType().equalsIgnoreCase("int") || attr.getAttrType().equalsIgnoreCase("integer"))) {
                for (int i = 0; i < list.size(); i++) {
                    com.alibaba.fastjson2.JSONObject temp = list.getJSONObject(i);
                    for (String tag1 : temp.keySet()) {
                        if (attr.getAttrCode().equalsIgnoreCase(tag1)) {
                            try {
                                temp.put(tag1, NumberUtil.round(Convert.toDouble(temp.getString(tag1)) * Convert.toDouble(attr.getAttrMultiple()), 3));
                                list.set(i, temp);
                                break;
                            } catch (Exception e) {
//                                throw new RuntimeException("数据处理异常", e);
                            }
                        }
                    }
                }
            }
        }
        result.put("attrName", "");
        try {
            result.put("attrName", attrs.get(0).getAttrName());
        } catch (Exception e) {

        }
        //把tag值变为固定val
        for (int i = 0; i < list.size(); i++) {
            com.alibaba.fastjson2.JSONObject temp = list.getJSONObject(i);
            temp.put("val", Convert.toStr(temp.get(tag)));
            temp.remove(tag);
            list.set(i, temp);
        }
        result.put("tag", tag);
        result.put("list", list);
        return result;
    }

    @Override
    public List<DeviceNesting> getDeviceforHistoryData() {
        SysDept dept = new SysDept();
        final List<SysDept> sysDepts = deptService.selectDeptList(dept);

        List<DeviceNesting> nestings = new ArrayList<>();

        for (SysDept sysDept : sysDepts) {
            DeviceNesting nesting = new DeviceNesting();
            // 第一层：部门
            nesting.setId(sysDept.getDeptId());
            nesting.setName(sysDept.getDeptName());

            ZhWorkshop workshop = new ZhWorkshop();
            workshop.setDeptId(sysDept.getDeptId());
            final List<ZhWorkshop> zhWorkshops = zhWorkshopMapper.selectZhWorkshopList(workshop);

            for (ZhWorkshop zhWorkshop : zhWorkshops) {
                DeviceNesting child = new DeviceNesting();
                // 第二层：车间
                child.setId(zhWorkshop.getId());
                child.setName(zhWorkshop.getWorkshopName());
                child.setCode(zhWorkshop.getWorkshopCode());

                ZhIotEquipment equipment = new ZhIotEquipment();
                equipment.setWorkshopId(zhWorkshop.getId());
                final List<ZhIotEquipment> zhIotEquipments = equipmentMapper.selectZhIotEquipmentList(equipment);

                for (ZhIotEquipment zhIotEquipment : zhIotEquipments) {
                    DeviceNesting childChild = new DeviceNesting();
                    // 第三层：设备
                    childChild.setId(zhIotEquipment.getId());
                    childChild.setName(zhIotEquipment.getEquipmentName());
                    childChild.setCode(zhIotEquipment.getEquipmentCode());
                    // 将设备添加到车间的子列表中
                    child.getChild().add(childChild);
                }

                // 将车间添加到部门的子列表中
                nesting.getChild().add(child);
            }

            nestings.add(nesting);
        }
        return nestings;
    }

    @Override
    public List<ZhDeviceTypeAttr> getAttrs(Long id) {
        ZhIotEquipment equipment = equipmentMapper.selectZhIotEquipmentById(id);
        if (equipment == null) {
            throw new IllegalArgumentException("设备ID不存在");
        }
        // 查询设备类型属性
        ZhDeviceTypeAttr query = new ZhDeviceTypeAttr();
        query.setTslId(equipment.getDeviceTypeId());
        List<ZhDeviceTypeAttr> attrs = deviceTypeAttrMapper.selectZhDeviceTypeAttrList(query);
        return attrs;
    }


}
