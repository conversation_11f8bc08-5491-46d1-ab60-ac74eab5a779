package com.ruoyi.zhenghe.util;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.util.iotdb.IoTDBUtil;
import com.ruoyi.zhenghe.domain.ZhDeviceTypeAttr;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.mapper.ZhDeviceTypeAttrMapper;
import com.ruoyi.zhenghe.service.IZhIotEquipmentService;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 电量计算统一工具类
 * 确保不同接口使用相同的计算逻辑，避免总电量不一致的问题
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
public class ElectricQuantityCalculator {
    
    private static final Logger log = LoggerFactory.getLogger(ElectricQuantityCalculator.class);
    
    private static final String TENANT = Constants.TENANT;
    private static final String POWER_CODE = "TotalPower";
    
    @Autowired
    private IoTDBUtil ioTDBUtil;
    
    @Autowired
    private IZhIotEquipmentService zhIotEquipmentService;
    
    @Autowired
    private ZhDeviceTypeAttrMapper attrMapper;
    
    /**
     * 计算车间电量（统一方法）
     * 
     * @param workshopId 车间ID
     * @param deviceTypeId 设备类型ID（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param onlyElectricMeter 是否只统计电表设备（true=只统计名称包含"电表"或"总电"的设备）
     * @return 车间总电量
     */
    public double calculateWorkshopPower(Long workshopId, Long deviceTypeId, String startTime, String endTime, boolean onlyElectricMeter) 
            throws IoTDBConnectionException, StatementExecutionException {
        
        // 构建查询条件
        ZhIotEquipment queryCondition = new ZhIotEquipment();
        queryCondition.setWorkshopId(workshopId);
        queryCondition.setStartTime(startTime);
        queryCondition.setEndTime(endTime);
        if (ObjectUtil.isNotNull(deviceTypeId)) {
            queryCondition.setDeviceTypeId(deviceTypeId);
        }
        
        // 查询设备列表
        List<ZhIotEquipment> equipmentList = zhIotEquipmentService.selectZhIotEquipmentList(queryCondition);
        
        if (ObjectUtil.isNull(equipmentList) || equipmentList.isEmpty()) {
            log.debug("车间 {} 未找到设备", workshopId);
            return 0.0;
        }
        
        double totalPower = 0.0;
        
        for (ZhIotEquipment equipment : equipmentList) {
            // 设备筛选逻辑
            if (onlyElectricMeter) {
                // 只统计电表设备
                if (!isElectricMeterDevice(equipment)) {
                    continue;
                }
            }
            
            try {
                // 计算设备电量
                double devicePower = calculateDevicePower(equipment, startTime, endTime);
                totalPower += devicePower;
                
                log.debug("设备 {} 电量: {}", equipment.getEquipmentCode(), devicePower);
                
            } catch (Exception e) {
                log.error("计算设备电量失败 - 设备: {}", equipment.getEquipmentCode(), e);
            }
        }
        
        // 格式化结果，保留3位小数
        String formattedPower = String.format("%.3f", totalPower);
        return Double.parseDouble(formattedPower);
    }
    
    /**
     * 计算单个设备的电量
     * 
     * @param equipment 设备信息
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 设备电量
     */
    private double calculateDevicePower(ZhIotEquipment equipment, String startTime, String endTime) 
            throws IoTDBConnectionException, StatementExecutionException {
        
        // 获取设备电量差值
        double powerDifference = ioTDBUtil.queryIotEquipmentAttributeDifference(
            TENANT, 
            equipment.getEquipmentCode(), 
            POWER_CODE, 
            startTime, 
            endTime
        );
        
        // 获取设备属性倍数
        double customMultiple = getDeviceMultiple(equipment.getDeviceTypeId());
        
        // 计算最终电量
        return powerDifference * customMultiple;
    }
    
    /**
     * 获取设备属性倍数
     * 
     * @param deviceTypeId 设备类型ID
     * @return 倍数值
     */
    private double getDeviceMultiple(Long deviceTypeId) {
        try {
            ZhDeviceTypeAttr queryAttr = new ZhDeviceTypeAttr();
            queryAttr.setTslId(deviceTypeId);
            queryAttr.setAttrCode(POWER_CODE);
            
            List<ZhDeviceTypeAttr> attrs = attrMapper.selectZhDeviceTypeAttrList(queryAttr);
            
            if (ObjectUtil.isNotNull(attrs) && !attrs.isEmpty()) {
                ZhDeviceTypeAttr attr = attrs.get(0);
                if (attr.getAttrMultiple() != null) {
                    return attr.getAttrMultiple().doubleValue();
                }
            }
        } catch (Exception e) {
            log.warn("获取设备属性倍数失败 - 设备类型: {}", deviceTypeId, e);
        }
        
        return 1.0; // 默认倍数
    }
    
    /**
     * 判断是否为电表设备
     * 
     * @param equipment 设备信息
     * @return true=电表设备，false=非电表设备
     */
    private boolean isElectricMeterDevice(ZhIotEquipment equipment) {
        if (equipment.getEquipmentName() == null) {
            return false;
        }
        
        String equipmentName = equipment.getEquipmentName();
        return equipmentName.contains("电表") || equipmentName.contains("总电");
    }
    
    /**
     * 计算所有车间的总电量（用于验证）
     * 
     * @param workshopIds 车间ID列表
     * @param deviceTypeId 设备类型ID（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param onlyElectricMeter 是否只统计电表设备
     * @return 总电量
     */
    public double calculateTotalPower(List<Long> workshopIds, Long deviceTypeId, String startTime, String endTime, boolean onlyElectricMeter) 
            throws IoTDBConnectionException, StatementExecutionException {
        
        double totalPower = 0.0;
        
        for (Long workshopId : workshopIds) {
            double workshopPower = calculateWorkshopPower(workshopId, deviceTypeId, startTime, endTime, onlyElectricMeter);
            totalPower += workshopPower;
        }
        
        return totalPower;
    }
}
