package com.ruoyi.screen;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.screen.entity.WDLErrorInfo;
import com.ruoyi.util.iotdb.IoTDBUtil;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.mapper.ZhDeviceTypeAttrMapper;
import com.ruoyi.zhenghe.service.IZhIotEquipmentService;
import com.ruoyi.zhengheiot.domain.EquipmentDetail;
import com.ruoyi.zhengheiot.service.IIotRealDataService;
import com.ruoyi.zhengheiot.service.IotHistoryDataService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Api(tags = "剪包线大屏")
@RestController
@RequestMapping("/screen/data/jianbaoxian")
public class JianBaoXianController {
    @Autowired
    private IZhIotEquipmentService zhIotEquipmentService;
    @Autowired
    private IIotRealDataService realDataService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private IIotRealDataService iotRealDataService;

    @Autowired
    private IoTDBUtil ioTDBUtil;

    @Resource
    private ZhDeviceTypeAttrMapper zhDeviceTypeAttrMapper;
    @Resource
    private IotHistoryDataService historyDataService;

    // 常量定义
    final int REDIS_CACHE_EXPIRATION_SECONDS = 300;// 缓存过期时间（秒）

    List<ZhIotEquipment> jianbaoEquipment = new ArrayList<>();


    /**
     * 获取冲压设备信息
     * 本接口用于获取特定类型设备的信息，主要针对的是网络借贷设备
     * 使用GET请求来获取数据，响应结果是JSON格式的AjaxResult对象
     *
     * @return 返回包含设备信息列表的AjaxResult对象，表示操作成功并携带数据
     */
    @GetMapping("/get/jianbaoxian/device")
    public AjaxResult getJianbaoxian() {

        getJianBaoEquipment();
        return AjaxResult.success(jianbaoEquipment);
    }


    /**
     * 获取错误信息
     * 本接口用于获取特定设备类型的错误信息，并结合缓存中的数据进行综合处理
     *
     * @return 返回设备错误信息的列表，如果无错误信息则返回空列表
     */
    @GetMapping("/error/info")
    public AjaxResult getErrorInfo(Long id) {

        // 初始化错误信息列表
        List<WDLErrorInfo> ans = new ArrayList<>();

        try {
            // 获取设备列表
            List<ZhIotEquipment> equipmentList = new ArrayList<>();
            if (id != null) {
                final ZhIotEquipment equipment = zhIotEquipmentService.selectZhIotEquipmentById(id);
                if (equipment != null) {
                    equipmentList.add(equipment);
                }
            } else {
                // 创建设备对象并设置设备类型ID
                getJianBaoEquipment();
                equipmentList =jianbaoEquipment;
            }

            // 如果设备列表为空，直接返回成功响应
            if (equipmentList == null || equipmentList.isEmpty()) {
                return AjaxResult.success(new ArrayList<>());
            }

            // 遍历设备列表，获取每个设备的实时数据
            for (ZhIotEquipment equipment : equipmentList) {
                try {
                    EquipmentDetail realDataList = realDataService.getRealDataList(equipment.getId());

                    // 如果实时数据为空，跳过当前设备
                    if (realDataList == null || realDataList.getAttrList() == null) {
                        continue;
                    }

                    // 处理实时数据中的每个属性，寻找错误信息
                    realDataList.getAttrList().forEach(attr -> {
                        if (attr != null && "1".equals(attr.getAlarm()) && attr.getAttrName() != null) {
                            WDLErrorInfo wdlErrorInfo = new WDLErrorInfo();
                            wdlErrorInfo.setErrorInfo(attr.getAttrName());
                            wdlErrorInfo.setDeviceName(equipment.getEquipmentName() + equipment.getEquipmentCode());
                            wdlErrorInfo.setErrorTime(attr.getLastUpdateTime());
                            ans.add(wdlErrorInfo);
                        }
                    });
                } catch (Exception e) {
                    log.error("处理设备 {} 的实时数据时发生异常", equipment.getId(), e);
                }
            }
            // 返回错误信息列表的响应
            return AjaxResult.success(ans);
        } catch (Exception e) {
            log.error("获取错误信息时发生异常", e);
            return AjaxResult.error("获取错误信息失败");
        }
    }


    // 获取产量信息
    @GetMapping("/number/info")
    public AjaxResult numberInfo(Long id) {
        // 构建缓存键
        String cacheKey = "jianbao:number_info:" + (id == null ? "all" : id.toString());

        // 检查缓存是否存在
        if (redisCache.hasKey(cacheKey)) {
            return AjaxResult.success(JSONObject.parseObject(redisCache.getCacheObject(cacheKey).toString()));
        }

        JSONObject ans = new JSONObject();

        // 获取设备列表
        getJianBaoEquipment();
        final List<ZhIotEquipment> equipmentList = jianbaoEquipment;
        // 返回的日期
        List<String> dateList = new ArrayList<>();
        // 返回的电量
        List<Integer> numberList = new ArrayList<>();

        final DateTime date = DateUtil.date();
        for (int i = 6; i >= 0; i--) {
            DateTime begin = DateUtil.beginOfDay(DateUtil.offsetDay(date, -i));
            DateTime end = DateUtil.endOfDay(begin);
            if (end.isAfter(date)) {
                end = DateUtil.date();
            }
            final String format = DateUtil.format(begin, "MM-dd");
            dateList.add(format);
            if (equipmentList == null || equipmentList.isEmpty()) {
                continue;
            }
            String startTime = DateUtil.format(begin, "yyyy-MM-dd HH:mm:ss");
            String endTime = DateUtil.format(end, "yyyy-MM-dd HH:mm:ss");

            int energyTotal = 0;
            for (ZhIotEquipment equipment : equipmentList) {
                final cn.hutool.json.JSONObject number = historyDataService.getHistoryDataTagList(equipment.getId(), "Number", startTime, endTime, null, null);
                final int energy = ioTDBUtil.getTotalPower(number);
                energyTotal = energyTotal + energy;
            }
            numberList.add(Math.abs(energyTotal));
        }
        ans.put("dateList", dateList);
        ans.put("numberList", numberList);
        redisCache.setCacheObject(cacheKey, ans.toJSONString(), REDIS_CACHE_EXPIRATION_SECONDS, TimeUnit.SECONDS);
        return AjaxResult.success(ans);
    }


    @GetMapping("device/monitor")
    public AjaxResult deviceMonitor() {

        String cacheKey = "jianbao:device_monitor";
        if (redisCache.hasKey(cacheKey)) {
            return AjaxResult.success(JSONObject.parseObject(redisCache.getCacheObject(cacheKey).toString()));
        }

        JSONObject ans = new JSONObject();
        int runTime = 0;
        int standbyTime = 0;
        int errorTime = 0;
        String openRate = "";

        ans.put("runTime", runTime);
        ans.put("standbyTime", standbyTime);
        ans.put("errorTime", errorTime);
        ans.put("openRate", openRate);
        ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
        zhIotEquipment.setDeviceTypeId(80L);
        // 获取设备列表
        getJianBaoEquipment();
        final List<ZhIotEquipment> equipmentList = jianbaoEquipment;
        if (equipmentList == null || equipmentList.isEmpty()) {
            return AjaxResult.success(ans);
        }
        String startTime = DateUtil.format(DateUtil.beginOfDay(new Date()), "yyyy-MM-dd HH:mm:ss");
        String endTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        int kaijiTime = 0;
        int guanjiTime = 0;
        for (ZhIotEquipment equipment : equipmentList) {
            final cn.hutool.json.JSONObject powerOn = historyDataService.getHistoryDataTagList(equipment.getId(), "Power_ON", startTime, endTime, null, null);
            if (powerOn.get("list") != null) {
                final cn.hutool.json.JSONArray data = powerOn.getJSONArray("list");
                if (data != null && data.size() > 0) {
                    for (int j = 0; j < data.size(); j++) {
                        final cn.hutool.json.JSONObject jsonObject = data.getJSONObject(j);
                        if (jsonObject.get("val") != null && !"null".equals(jsonObject.get("val") + "") && !"/".equals(jsonObject.get("val") + "")) {
                            try {
                                double value = Double.parseDouble(jsonObject.get("val") + "");
                                if (value > 0) {
                                    kaijiTime++;
                                }else {
                                    guanjiTime++;
                                }
                            } catch (NumberFormatException e) {
//                            throw new RuntimeException(e);
                            }
                        }
                    }
                }
            }
        }

        double rate ;
        if (kaijiTime == 0) {
            rate = 0;
        } else {
            rate = (double) kaijiTime / (kaijiTime + guanjiTime);
        }
        ans.put("runTime", kaijiTime/ 60);
        ans.put("standbyTime", guanjiTime/ 60);
        ans.put("errorTime", "-");
        ans.put("openRate", String.format("%.2f", rate * 100) + "%");
        redisCache.setCacheObject(cacheKey, ans.toJSONString(), REDIS_CACHE_EXPIRATION_SECONDS, TimeUnit.SECONDS);
        return AjaxResult.success(ans);
    }


    @GetMapping("device/rate")
    public AjaxResult deviceRate() {

        String cacheKey = "jianbao:device_rate";
        if (redisCache.hasKey(cacheKey)) {
            return AjaxResult.success(JSONObject.parseObject(redisCache.getCacheObject(cacheKey).toString()));
        }
        JSONObject ans = new JSONObject();
        // 返回的日期
        List<String> dateList = new ArrayList<>();
        //返回的利用率
        List<String> rateList = new ArrayList<>();

        final DateTime date = DateUtil.date();
        for (int i = 6; i >= 0; i--) {
            DateTime begin = DateUtil.beginOfDay(DateUtil.offsetDay(date, -i));
            DateTime end = DateUtil.endOfDay(begin);
            if (end.isAfter(date)) {
                end = DateUtil.date();
            }
            String startTime = DateUtil.format(begin, "yyyy-MM-dd HH:mm:ss");
            String endTime = DateUtil.format(end, "yyyy-MM-dd HH:mm:ss");

            final String format = DateUtil.format(begin, "MM-dd");
            dateList.add(format);

            getJianBaoEquipment();
            final List<ZhIotEquipment> equipmentList = jianbaoEquipment;
            int kaijiTime = 0;
            int guanjiTime = 0;

            for (ZhIotEquipment equipment : equipmentList) {
                final cn.hutool.json.JSONObject powerOn = historyDataService.getHistoryDataTagList(equipment.getId(), "Power_ON", startTime, endTime, null, null);
                if (powerOn.get("list") != null) {
                    final cn.hutool.json.JSONArray data = powerOn.getJSONArray("list");
                    if (data != null && data.size() > 0) {
                        for (int j = 0; j < data.size(); j++) {
                            final cn.hutool.json.JSONObject jsonObject = data.getJSONObject(j);
                            if (jsonObject.get("val") != null && !"null".equals(jsonObject.get("val") + "") && !"/".equals(jsonObject.get("val") + "")) {
                                try {
                                    double value = Double.parseDouble(jsonObject.get("val") + "");
                                    if (value > 0) {
                                       kaijiTime++;
                                    }else {
                                        guanjiTime++;
                                    }
                                } catch (NumberFormatException e) {
//                            throw new RuntimeException(e);
                                }
                            }
                        }
                    }
                }
            }
            double rate ;
            if (kaijiTime == 0) {
                rate = 0;
            } else {
                rate = (double) kaijiTime / (kaijiTime + guanjiTime);
            }
            rateList.add(String.format("%.2f", rate * 100));
        }
        ans.put("dateList", dateList);
        ans.put("rateList", rateList);
        redisCache.setCacheObject(cacheKey, ans.toJSONString(), REDIS_CACHE_EXPIRATION_SECONDS, TimeUnit.SECONDS);
        return AjaxResult.success(ans);
    }


    @GetMapping("/monitor/real/data")
    public AjaxResult list(@RequestParam(required = true) Long id) {
        try {
            // 获取实时数据
            final EquipmentDetail realDataList = iotRealDataService.getRealDataList(id);
            return AjaxResult.success(realDataList);
        } catch (IllegalArgumentException e) {
            // 参数校验失败，返回具体的错误信息
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            // 记录异常日志
            log.error("查询IoT实时数据列表失败，id: {}", id, e);
            return AjaxResult.error("系统内部错误，请稍后重试");
        }
    }


    private void getJianBaoEquipment() {
        String key = "jianbao_device_list_new1";
        if (redisCache.hasKey(key)) {
            return;
        }
        // 定义关注的设备类型ID数组，这些ID代表不同的设备类型
        Long[] deviceTypeIds = {110L, 104L, 82L,83L,84L,79L,109L,81L,108L,103L,109L}; //设备类型
        Long[] processIds = {71L,73L,69L}; //工序
        List<ZhIotEquipment> tempEquipment = new ArrayList<>();
        ZhIotEquipment zhIotEquipment = new ZhIotEquipment();

//        for (Long deviceTypeId : deviceTypeIds) {
        for (Long processId : processIds) {
            // 设置当前查询的设备类型ID
//            zhIotEquipment.setDeviceTypeId(deviceTypeId);
            zhIotEquipment.setProcessId(processId);
            // 获取设备列表，并将结果合并到答案列表中
            // 这里解释了为什么要进行多次查询：因为需要根据不同的设备类型分别查询，并汇总结果
            final List<ZhIotEquipment> equipmentList = zhIotEquipmentService.selectZhIotEquipmentListNoScope(zhIotEquipment);
            tempEquipment.addAll(equipmentList);
        }
        Map<String, List<ZhIotEquipment> > tempMap  = new HashMap<>();
        for (ZhIotEquipment equipment : tempEquipment) {
            String equipmentCode = equipment.getEquipmentCode();
            if(equipmentCode != null){

                if(equipmentCode.contains("_")){
                    equipmentCode = equipmentCode.split("_")[0];
                }
                equipmentCode=equipmentCode.substring(equipmentCode.length()-2, equipmentCode.length());

                if(tempMap.containsKey(equipmentCode)){
                    tempMap.get(equipmentCode).add(equipment);
                }else {
                    List<ZhIotEquipment> list = new ArrayList<>();
                    list.add(equipment);
                    tempMap.put(equipmentCode, list);
                }
            }
        }
        jianbaoEquipment.clear();
        for (Map.Entry<String, List<ZhIotEquipment>> entry : tempMap.entrySet()) {
            jianbaoEquipment.addAll(entry.getValue());
        }
        redisCache.setCacheObject(key, jianbaoEquipment, REDIS_CACHE_EXPIRATION_SECONDS, TimeUnit.SECONDS);

    }

}
