# 日志级别配置说明

## 修改内容

为了减少日志输出，已将系统日志级别从 `INFO` 调整为 `WARN`，只显示警告和错误级别的日志。

## 修改的文件

### 1. logback.xml 配置文件
**文件路径**: `ruoyi-admin/src/main/resources/logback.xml`

**主要修改**:
- 系统模块日志级别: `info` → `warn`
- 根日志级别: `info` → `warn`
- 文件日志过滤器: `LevelFilter(INFO)` → `ThresholdFilter(WARN)`
- 新增第三方组件日志级别控制

```xml
<!-- 系统模块日志级别控制  -->
<logger name="com.ruoyi" level="warn" />
<!-- Spring日志级别控制  -->
<logger name="org.springframework" level="warn" />
<!-- IoTDB相关日志级别控制 -->
<logger name="org.apache.iotdb" level="warn" />
<!-- MQTT相关日志级别控制 -->
<logger name="org.eclipse.paho" level="warn" />
<!-- MyBatis日志级别控制 -->
<logger name="org.apache.ibatis" level="warn" />
<!-- Druid数据源日志级别控制 -->
<logger name="com.alibaba.druid" level="warn" />

<root level="warn">
    <appender-ref ref="console" />
</root>
```

### 2. application.yml 配置文件
**文件路径**: `ruoyi-admin/src/main/resources/application.yml`

**主要修改**:
```yaml
# 日志配置
logging:
  level:
    com.ruoyi: warn
    org.springframework: warn
    org.apache.iotdb: warn
    org.eclipse.paho: warn
    org.apache.ibatis: warn
    com.alibaba.druid: warn
    root: warn
```

## 日志级别说明

### 日志级别优先级（从高到低）
1. **ERROR** - 错误信息，系统异常
2. **WARN** - 警告信息，潜在问题
3. **INFO** - 一般信息，业务流程
4. **DEBUG** - 调试信息，详细执行过程
5. **TRACE** - 跟踪信息，最详细的日志

### 当前配置效果
- ✅ **显示**: ERROR 和 WARN 级别的日志
- ❌ **隐藏**: INFO、DEBUG、TRACE 级别的日志

## 受影响的组件

### 系统组件
- `com.ruoyi.*` - 系统业务模块
- `org.springframework.*` - Spring框架

### 第三方组件
- `org.apache.iotdb.*` - IoTDB时序数据库
- `org.eclipse.paho.*` - MQTT客户端
- `org.apache.ibatis.*` - MyBatis ORM框架
- `com.alibaba.druid.*` - Druid数据源

## 临时调整日志级别

如果需要临时查看更详细的日志进行调试，可以：

### 方法1: 修改配置文件（需要重启）
将需要调试的包的日志级别临时改为 `debug` 或 `info`:

```xml
<!-- 临时调试IoTDB相关问题 -->
<logger name="com.ruoyi.util.iotdb" level="debug" />
<!-- 临时调试MQTT相关问题 -->
<logger name="com.ruoyi.util.mq" level="debug" />
```

### 方法2: 使用Spring Boot Actuator（推荐）
如果项目启用了Actuator，可以通过HTTP接口动态调整日志级别：

```bash
# 查看当前日志级别
curl http://localhost:8081/actuator/loggers/com.ruoyi.util.iotdb

# 临时调整日志级别为DEBUG
curl -X POST http://localhost:8081/actuator/loggers/com.ruoyi.util.iotdb \
  -H "Content-Type: application/json" \
  -d '{"configuredLevel": "DEBUG"}'

# 恢复日志级别为WARN
curl -X POST http://localhost:8081/actuator/loggers/com.ruoyi.util.iotdb \
  -H "Content-Type: application/json" \
  -d '{"configuredLevel": "WARN"}'
```

## 特殊情况处理

### 如果需要查看特定功能的详细日志
可以为特定的类或包单独设置日志级别：

```xml
<!-- 只查看IoTDB快照相关的详细日志 -->
<logger name="com.ruoyi.util.iotdb.IoTDBUtil" level="info" />
<!-- 只查看MQTT消息处理的详细日志 -->
<logger name="com.ruoyi.util.mq.MQTTReceiveCallback" level="info" />
<!-- 只查看产品数量查询的详细日志 -->
<logger name="com.ruoyi.zhenghe.service.impl.ZhDeviceProductionRecordServiceImpl" level="info" />
```

### 如果需要完全关闭某些组件的日志
```xml
<!-- 完全关闭Druid的日志输出 -->
<logger name="com.alibaba.druid" level="off" />
```

## 生效方式

修改配置文件后需要重启应用才能生效：

```bash
# 停止应用
# 重新启动应用
```

## 验证配置

启动应用后，观察控制台输出：
- 应该只看到 WARN 和 ERROR 级别的日志
- 不应该看到大量的 INFO 和 DEBUG 日志
- 系统启动信息会显著减少

如果仍有过多日志输出，请检查是否有其他日志配置文件或第三方组件的日志配置。
