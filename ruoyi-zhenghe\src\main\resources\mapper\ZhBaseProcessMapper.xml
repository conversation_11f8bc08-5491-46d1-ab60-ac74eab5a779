<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhBaseProcessMapper">

    <resultMap type="ZhBaseProcess" id="ZhBaseProcessResult">
        <result property="id"    column="id"    />
        <result property="processName"    column="process_name"    />
        <result property="processSort"    column="process_sort"    />
        <result property="processCode"    column="process_code"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userId"    column="user_id"    />
    </resultMap>

    <sql id="selectZhBaseProcessVo">
        select id, process_name, process_sort, process_code, remark, create_by, create_time, update_by, update_time,dept_id,user_id from zh_base_process b
    </sql>

    <sql id="selectZhBaseProcessVoLian">
        select b.id, b.process_name, b.process_sort, b.process_code, b.remark, b.create_by, b.create_time,
        b.update_by, b.update_time,b.dept_id,b.user_id,
        d.dept_name as deptName
        from zh_base_process b
        left join sys_dept d on b.dept_id = d.dept_id
    </sql>

    <select id="selectZhBaseProcessList" parameterType="ZhBaseProcess" resultMap="ZhBaseProcessResult">
        <include refid="selectZhBaseProcessVoLian"/>
        <where>
            1 = 1
            <if test="processName != null  and processName != ''"> and b.process_name like concat('%', #{processName}, '%')</if>
            <if test="processSort != null "> and b.process_sort = #{processSort}</if>
            <if test="processCode != null  and processCode != ''"> and b.process_code like concat('%', #{processCode}, '%')</if>
            <if test="deptId != null  and deptId != ''"> and b.dept_id = #{deptId}</if>
        </where>
        ${params.dataScope}
        order by
        b.create_time desc
    </select>

    <select id="selectZhBaseProcessById" parameterType="Long" resultMap="ZhBaseProcessResult">
        <include refid="selectZhBaseProcessVo"/>
        where b.id = #{id}
    </select>

    <insert id="insertZhBaseProcess" parameterType="ZhBaseProcess" useGeneratedKeys="true" keyProperty="id">
        insert into zh_base_process
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="processName != null and processName != ''">process_name,</if>
            <if test="processSort != null">process_sort,</if>
            <if test="processCode != null and processCode != ''">process_code,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="processName != null and processName != ''">#{processName},</if>
            <if test="processSort != null">#{processSort},</if>
            <if test="processCode != null and processCode != ''">#{processCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
        </trim>
    </insert>

    <update id="updateZhBaseProcess" parameterType="ZhBaseProcess">
        update zh_base_process
        <trim prefix="SET" suffixOverrides=",">
            <if test="processName != null and processName != ''">process_name = #{processName},</if>
            <if test="processSort != null">process_sort = #{processSort},</if>
            <if test="processCode != null and processCode != ''">process_code = #{processCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhBaseProcessById" parameterType="Long">
        delete from zh_base_process where id = #{id}
    </delete>

    <delete id="deleteZhBaseProcessByIds" parameterType="String">
        delete from zh_base_process where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>