package com.ruoyi.zhenghe.service;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhWorkshop;

/**
 * 车间管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface IZhWorkshopService 
{
    /**
     * 查询车间管理
     * 
     * @param id 车间管理主键
     * @return 车间管理
     */
    public ZhWorkshop selectZhWorkshopById(Long id);

    /**
     * 查询车间管理列表
     * 
     * @param zhWorkshop 车间管理
     * @return 车间管理集合
     */
    public List<ZhWorkshop> selectZhWorkshopList(ZhWorkshop zhWorkshop);

    public List<ZhWorkshop> selectOnlyZhWorkshopList(ZhWorkshop zhWorkshop);

    /**
     * 新增车间管理
     * 
     * @param zhWorkshop 车间管理
     * @return 结果
     */
    public int insertZhWorkshop(ZhWorkshop zhWorkshop);

    /**
     * 修改车间管理
     * 
     * @param zhWorkshop 车间管理
     * @return 结果
     */
    public int updateZhWorkshop(ZhWorkshop zhWorkshop);

    /**
     * 批量删除车间管理
     * 
     * @param ids 需要删除的车间管理主键集合
     * @return 结果
     */
    public int deleteZhWorkshopByIds(Long[] ids);

    /**
     * 删除车间管理信息
     * 
     * @param id 车间管理主键
     * @return 结果
     */
    public int deleteZhWorkshopById(Long id);
}
