package com.ruoyi.zhenghe.mapper;

import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.domain.query.ZhIotEquipmentQuery;
import com.ruoyi.zhengheiot.domain.EquipmentDetail;

import java.util.List;
import java.util.Map;

/**
 * 物联网设备明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface ZhIotEquipmentMapper 
{
    /**
     * 查询物联网设备明细
     * 
     * @param id 物联网设备明细主键
     * @return 物联网设备明细
     */
    public ZhIotEquipment selectZhIotEquipmentById(Long id);

    /**
     * 获取设备实时数据by id
     * @param id
     * @return
     */
    public EquipmentDetail selectZhIotEquipmentDetailById(Long id);

    /**
     * 查询物联网设备明细列表
     * 
     * @param zhIotEquipment 物联网设备明细
     * @return 物联网设备明细集合
     */
    public List<ZhIotEquipment> selectZhIotEquipmentList(ZhIotEquipment zhIotEquipment);

    public List<ZhIotEquipment> selectZhIotEquipmentList2(ZhIotEquipmentQuery query);

    public List<ZhIotEquipment> selectZhIotEquipmentListNoScope(ZhIotEquipment zhIotEquipment);

    /**
     * 新增物联网设备明细
     * 
     * @param zhIotEquipment 物联网设备明细
     * @return 结果
     */
    public int insertZhIotEquipment(ZhIotEquipment zhIotEquipment);

    /**
     * 修改物联网设备明细
     * 
     * @param zhIotEquipment 物联网设备明细
     * @return 结果
     */
    public int updateZhIotEquipment(ZhIotEquipment zhIotEquipment);

    /**
     * 删除物联网设备明细
     * 
     * @param id 物联网设备明细主键
     * @return 结果
     */
    public int deleteZhIotEquipmentById(Long id);

    /**
     * 批量删除物联网设备明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZhIotEquipmentByIds(Long[] ids);

    /**
     * 查询物联网设备明细
     *
     * @param equipmentCode 物联网设备明细主键
     * @return 物联网设备明细
     */
    public ZhIotEquipment selectZhIotEquipmentByEquipmentCode(String equipmentCode);

//    public R
}
