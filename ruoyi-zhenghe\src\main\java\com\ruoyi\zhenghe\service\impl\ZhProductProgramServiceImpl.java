package com.ruoyi.zhenghe.service.impl;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.zhenghe.domain.ZhProductProgram;
import com.ruoyi.zhenghe.mapper.ZhProductProgramMapper;
import com.ruoyi.zhenghe.service.IZhProductProgramService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品程序号Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
@Service
public class ZhProductProgramServiceImpl implements IZhProductProgramService 
{
    @Autowired
    private ZhProductProgramMapper zhProductProgramMapper;

    /**
     * 查询产品程序号
     * 
     * @param id 产品程序号主键
     * @return 产品程序号
     */
    @Override
    public ZhProductProgram selectZhProductProgramById(Long id)
    {
        return zhProductProgramMapper.selectZhProductProgramById(id);
    }

    /**
     * 查询产品程序号列表
     * 
     * @param zhProductProgram 产品程序号
     * @return 产品程序号
     */
    @Override
    @DataScope(deptAlias = "t1")
    public List<ZhProductProgram> selectZhProductProgramList(ZhProductProgram zhProductProgram)
    {
        return zhProductProgramMapper.selectZhProductProgramList(zhProductProgram);
    }

    /**
     * 新增产品程序号
     * 
     * @param zhProductProgram 产品程序号
     * @return 结果
     */
    @Override
    public int insertZhProductProgram(ZhProductProgram zhProductProgram) {
        if (zhProductProgram.getDeptId() == null) {
            zhProductProgram.setDeptId(SecurityUtils.getDeptId());
        }
        zhProductProgram.setCreateBy(SecurityUtils.getUsername());
        zhProductProgram.setCreateTime(DateUtils.getNowDate());
        return zhProductProgramMapper.insertZhProductProgram(zhProductProgram);
    }

    /**
     * 修改产品程序号
     * 
     * @param zhProductProgram 产品程序号
     * @return 结果
     */
    @Override
    public int updateZhProductProgram(ZhProductProgram zhProductProgram)
    {
        zhProductProgram.setUpdateTime(DateUtils.getNowDate());
        return zhProductProgramMapper.updateZhProductProgram(zhProductProgram);
    }

    /**
     * 批量删除产品程序号
     * 
     * @param ids 需要删除的产品程序号主键
     * @return 结果
     */
    @Override
    public int deleteZhProductProgramByIds(Long[] ids)
    {
        return zhProductProgramMapper.deleteZhProductProgramByIds(ids);
    }

    /**
     * 删除产品程序号信息
     * 
     * @param id 产品程序号主键
     * @return 结果
     */
    @Override
    public int deleteZhProductProgramById(Long id)
    {
        return zhProductProgramMapper.deleteZhProductProgramById(id);
    }
}
