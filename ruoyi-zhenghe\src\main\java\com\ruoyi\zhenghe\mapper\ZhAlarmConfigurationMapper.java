package com.ruoyi.zhenghe.mapper;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhAlarmConfiguration;

/**
 * 告警配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface ZhAlarmConfigurationMapper 
{
    /**
     * 查询告警配置
     * 
     * @param id 告警配置主键
     * @return 告警配置
     */
    public ZhAlarmConfiguration selectZhAlarmConfigurationById(Long id);

    /**
     * 查询告警配置列表
     * 
     * @param zhAlarmConfiguration 告警配置
     * @return 告警配置集合
     */
    public List<ZhAlarmConfiguration> selectZhAlarmConfigurationList(ZhAlarmConfiguration zhAlarmConfiguration);

    /**
     * 新增告警配置
     * 
     * @param zhAlarmConfiguration 告警配置
     * @return 结果
     */
    public int insertZhAlarmConfiguration(ZhAlarmConfiguration zhAlarmConfiguration);

    /**
     * 修改告警配置
     * 
     * @param zhAlarmConfiguration 告警配置
     * @return 结果
     */
    public int updateZhAlarmConfiguration(ZhAlarmConfiguration zhAlarmConfiguration);

    /**
     * 删除告警配置
     * 
     * @param id 告警配置主键
     * @return 结果
     */
    public int deleteZhAlarmConfigurationById(Long id);

    /**
     * 批量删除告警配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZhAlarmConfigurationByIds(Long[] ids);

    /**
     * 查询告警配置根据设备id
     *
     * @param equipmentId 告警配置设备id
     * @return 告警配置
     */
    public List<ZhAlarmConfiguration> selectZhAlarmConfigurationByEquipmentId(Long equipmentId);
}
