package com.ruoyi.zhengheiot.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.ruoyi.ConstantZH;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.zhenghe.domain.ZhDeviceTypeAttr;
import com.ruoyi.zhenghe.service.IZhDeviceTypeAttrService;
import com.ruoyi.zhenghe.service.IZhIotEquipmentService;
import com.ruoyi.zhengheiot.domain.DeviceNesting;
import com.ruoyi.zhengheiot.service.IotHistoryDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Api(tags = "设备历史数据")
@RestController
@RequestMapping("/zhengheiot/devices")
public class IotHistoryDataController {


    @Resource
    private IotHistoryDataService historyDataService;
    @Resource
    IZhIotEquipmentService equipmentService;
    @Resource
    IZhDeviceTypeAttrService deviceTypeAttrService;

    @Autowired
    private RedisCache redisCache;


    /**
     * 查询IoT历史数据
     * <p>
     * 该函数用于查询指定设备的历史数据。如果未提供开始时间和结束时间，则默认查询过去24小时的数据。
     *
     * @param id        设备ID，必填参数，用于指定查询的设备。
     * @param startTime 开始时间，可选参数，用于指定查询的时间范围起始点。如果未提供，则默认设置为当前时间的前一天。
     * @param endTime   结束时间，可选参数，用于指定查询的时间范围结束点。如果未提供，则默认设置为当前时间。
     * @return 返回一个包含查询结果的AjaxResult对象，其中包含设备的历史数据。
     */
    @ApiOperation("查询设备历史数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "设备的id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, paramType = "query", dataType = "string")
    }
    )
    @PreAuthorize("@ss.hasPermi('zhengheiot:iotdata:list')")
    @GetMapping("/history/data")
    public AjaxResult list(@RequestParam(required = false) Long id, @RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime,
                           @RequestParam(required = false) Integer pageNum, @RequestParam(required = false) Integer pageSize) {
        if (id == null) {
            return AjaxResult.success(new ArrayList<>());
        }
        // 如果未提供开始时间或结束时间，则默认查询过去24小时的数据
        if (startTime == null || endTime == null) {
            startTime = DateUtil.format(DateUtil.beginOfDay(new Date()), "yyyy-MM-dd HH:mm:ss");
            endTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        }

        // 调用服务层方法获取设备历史数据
        final JSONObject objects = historyDataService.getHistoryData(id, startTime, endTime, pageNum, pageSize);

        // 返回查询结果
        return AjaxResult.success(objects);
    }

    @ApiOperation("查询设备单点位历史数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "设备的id", required = true, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, paramType = "query", dataType = "string")
    }
    )
    @PreAuthorize("@ss.hasPermi('zhengheiot:iotdata:list')")
    @GetMapping("/history/tag/data")
    public AjaxResult list(@RequestParam(required = true) Long id, @RequestParam(required = true) String tag,
                           @RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime,
                           @RequestParam(required = false) Integer pageNum, @RequestParam(required = false) Integer pageSize) {
        final Date dateNow = new Date();
        if (startTime == null || endTime == null) {
            startTime = DateUtil.format(DateUtil.offsetHour(dateNow, -2), "yyyy-MM-dd HH:mm:ss");
            endTime = DateUtil.format(dateNow, "yyyy-MM-dd HH:mm:ss");
        }

        // 解析时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime start = LocalDateTime.parse(startTime, formatter);
        LocalDateTime end = LocalDateTime.parse(endTime, formatter);

        // 检查时间顺序
        if (start.isAfter(end)) {
            return AjaxResult.error("开始时间不能大于结束时间");
        }

        Duration duration = Duration.between(start, end);
        long diffMinutes = duration.toMinutes(); // 精确到分钟
        double diffHours = diffMinutes / 60.0;    // 精确小时（保留小数）

        if (diffHours > 12) {
          return AjaxResult.error("时间间隔不能超过12小时");
        }



        // 调用服务层方法获取设备历史数据
        final JSONObject objects = historyDataService.getHistoryDataTagList(id, tag, startTime, endTime, pageNum, pageSize);
        if (objects.getJSONArray("list") != null) {
            objects.getJSONArray("list").forEach(o -> {
                JSONObject jsonObject = (JSONObject) o;
                if ("/".equals(jsonObject.get("val"))) {
                    jsonObject.put("val", "null");
                }
            });
        }

        return AjaxResult.success(objects);
    }

    @ApiOperation("查询设备列表")
    @PreAuthorize("@ss.hasPermi('zhengheiot:iotdata:list')")
    @GetMapping("/device/data")
    public AjaxResult deviceList() {
        String casheKey = "deviceforHistoryData";
        final Object cacheObject = redisCache.getCacheObject(casheKey);
        if (cacheObject != null) {
            return AjaxResult.success(cacheObject);
        } else {
            List<DeviceNesting> deviceforHistoryData = historyDataService.getDeviceforHistoryData();

            redisCache.setCacheObject(casheKey, deviceforHistoryData, ConstantZH.CASH_DEVICE_FOR_HISTORY, TimeUnit.SECONDS);
            return AjaxResult.success(deviceforHistoryData);
        }
    }

    @ApiOperation("获取设备属性by 设备id")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "设备的id", required = true, paramType = "query", dataType = "long")
    }
    )
    @GetMapping("/device/attrs/list")
    public AjaxResult deviceAttrsList(@RequestParam(required = true) Long id) {
        final List<ZhDeviceTypeAttr> attrs = historyDataService.getAttrs(id);
        return AjaxResult.success(attrs);
    }

}
