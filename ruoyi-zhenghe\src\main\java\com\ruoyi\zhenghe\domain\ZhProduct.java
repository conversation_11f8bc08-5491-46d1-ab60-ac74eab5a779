package com.ruoyi.zhenghe.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 产品信息对象 zh_product
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
public class ZhProduct extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    @Excel(name = "客户名称", sort = 1)
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    /** 产品型号 */
    @Excel(name = "产品型号", sort = 2)
    @ApiModelProperty(value = "产品型号")
    private String productModel;

    @Excel(name = "机型", sort = 3)
    @ApiModelProperty(value = "机型")
    private String machineType;

    /** 片数 */
//    @Excel(name = "片数", sort = 3)
    @ApiModelProperty(value = "片数")
    private Integer pieceCount;

    /** 节数 */
//    @Excel(name = "节数", sort = 4)
    @ApiModelProperty(value = "节数")
    private Integer sectionCount;
    @Excel(name = "OE号", sort = 4)
    private String oe;
    @Excel(name = "链长", sort = 5)
    private String chainLength;
    @Excel(name = "链长公差", sort = 6)
    private String chainLengthTolerance;


    /** 正时标记 */
    @Excel(name = "正时标记", sort = 7)
    @ApiModelProperty(value = "正时标记")
    private String timingMark;

    /** 产品图片 */
    @ApiModelProperty(value = "产品图片")
    private String productImage;

    /** 产品瑕疵列表 */
    @ApiModelProperty(value = "产品瑕疵列表")
    private List<ZhProductDefect> defectList;

    @Excel(name = "打印、批次标识", sort = 8)
    @ApiModelProperty(value = "打印、批次标识")
    private String printMark;

    /** 客户名拼音 */
    private String pinyin;
}
