package com.ruoyi.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 生产数据配置类
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@Configuration
public class ProductionDataConfig {
    
    /**
     * 默认企业编码，用于IoTDB查询
     */
    @Value("${production.default.enterprise-code:f88540622c5245c9856061bfe11f133c}")
    private String defaultEnterpriseCode;
    
    /**
     * 是否启用IoTDB精确查询
     */
    @Value("${production.iotdb.enabled:true}")
    private boolean iotdbEnabled;
    
    public String getDefaultEnterpriseCode() {
        return defaultEnterpriseCode;
    }
    
    public void setDefaultEnterpriseCode(String defaultEnterpriseCode) {
        this.defaultEnterpriseCode = defaultEnterpriseCode;
    }
    
    public boolean isIotdbEnabled() {
        return iotdbEnabled;
    }
    
    public void setIotdbEnabled(boolean iotdbEnabled) {
        this.iotdbEnabled = iotdbEnabled;
    }
}
