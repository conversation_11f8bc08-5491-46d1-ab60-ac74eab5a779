package com.ruoyi.zhenghe.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 产品程序号对象 zh_product_program
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
@ApiModel(value = "ZhProductProgram",description = "产品程序号对象")
public class ZhProductProgram extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 产品名称 */
    @ApiModelProperty(value = "产品名称")
    @Excel(name = "产品名称",sort = 1)
    private String produceName;

    /** 程序号 */
    @ApiModelProperty(value = "程序号")
    @Excel(name = "程序号",sort = 2)
    private String programNumber;

    /** 倍率 */
    @ApiModelProperty(value = "倍率")
    @Excel(name = "倍率",sort = 3)
    private BigDecimal rate;

    /** 事业部（部门) */
    @Excel(name = "事业部（部门)",sort = 4)
    private String deptName;

    /** 部门id */
    private Long deptId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setProduceName(String produceName) 
    {
        this.produceName = produceName;
    }

    public String getProduceName() 
    {
        return produceName;
    }

    public void setProgramNumber(String programNumber) 
    {
        this.programNumber = programNumber;
    }

    public String getProgramNumber() 
    {
        return programNumber;
    }

    public void setRate(BigDecimal rate) 
    {
        this.rate = rate;
    }

    public BigDecimal getRate() 
    {
        return rate;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("produceName", getProduceName())
            .append("programNumber", getProgramNumber())
            .append("rate", getRate())
            .append("deptId", getDeptId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
