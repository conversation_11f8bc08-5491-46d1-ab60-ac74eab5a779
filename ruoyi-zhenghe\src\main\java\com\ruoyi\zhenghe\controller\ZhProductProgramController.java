package com.ruoyi.zhenghe.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.zhenghe.domain.ZhWorkshop;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.zhenghe.domain.ZhProductProgram;
import com.ruoyi.zhenghe.service.IZhProductProgramService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 产品程序号Controller
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
@Api(tags = "产品程序号Controller")
@RestController
@RequestMapping("/zhenghe/program")
public class ZhProductProgramController extends BaseController
{
    @Autowired
    private IZhProductProgramService zhProductProgramService;

    /**
     * 查询产品程序号列表
     */
    @ApiOperation(value = "查询产品程序号列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "produceName",value = "产品名称",required = false,paramType = "query",dataType = "String"),
            @ApiImplicitParam(name = "programNumber",value = "程序号",required = false,paramType = "query",dataType = "String"),
            @ApiImplicitParam(name = "pageNum",value = "页数",required = true,paramType = "query",dataType = "integer"),
            @ApiImplicitParam(name = "pageSize",value = "条数",required = true,paramType = "query",dataType = "integer")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:program:list')")
    //@DataScope(deptAlias = "t1")
    @GetMapping("/list")
    public TableDataInfo list(ZhProductProgram zhProductProgram)
    {
        startPage();
        List<ZhProductProgram> list = zhProductProgramService.selectZhProductProgramList(zhProductProgram);
        return getDataTable(list);
    }

    /**
     * 导出产品程序号列表
     */
    @ApiOperation(value = "导出产品程序号列表")
    @PreAuthorize("@ss.hasPermi('zhenghe:program:export')")
    @Log(title = "产品程序号", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhProductProgram zhProductProgram)
    {
        List<ZhProductProgram> list = zhProductProgramService.selectZhProductProgramList(zhProductProgram);
        ExcelUtil<ZhProductProgram> util = new ExcelUtil<ZhProductProgram>(ZhProductProgram.class);
        util.exportExcel(response, list, "产品程序号数据");
    }

    /**
     * 导入基础数据-产品程序号列表
     */
    @ApiOperation(value = "导入基础数据-产品程序号列表")
    //@PreAuthorize("@ss.hasPermi('zhenghe:workshop:import')")
    @Log(title = "基础数据-产品程序号导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws IOException {
        ExcelUtil<ZhProductProgram> util = new ExcelUtil<ZhProductProgram>(ZhProductProgram.class);
        List<ZhProductProgram> zhProductProgramList = util.importExcel(file.getInputStream());
        if (StringUtils.isNull(zhProductProgramList) || zhProductProgramList.size()==0){
            throw new ServiceException("导入产品程序号列表数据不能为空");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ZhProductProgram zhProductProgram : zhProductProgramList) {
            try {
                zhProductProgram.setDeptId(SecurityUtils.getDeptId());
                zhProductProgramService.insertZhProductProgram(zhProductProgram);
                successNum++;
                successMsg.append("<br/>" + successNum + "、产品 " + zhProductProgram.getProduceName() + " 导入成功");
            }
            catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、产品 " + zhProductProgram.getProduceName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum>0){
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return AjaxResult.success(successMsg.toString());
    }

    /**
     * 导出产品程序号列表模板
     */
    @ApiOperation(value = "导出产品程序号列表模板")
    @Log(title = "产品程序号列表模板", businessType = BusinessType.EXPORT)
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<ZhProductProgram> util = new ExcelUtil<ZhProductProgram>(ZhProductProgram.class);
        util.exportExcel(response, new ArrayList<>(), "产品程序号数据");
    }


    /**
     * 获取产品程序号详细信息
     */
    @ApiOperation(value = "获取产品程序号详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "产品程序号id",required = true,paramType = "path",dataType = "long")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:program:query')")
    @GetMapping(value = "/{id}")
    public R<ZhProductProgram> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(zhProductProgramService.selectZhProductProgramById(id));
    }

    /**
     * 新增产品程序号
     */
    @ApiOperation(value = "新增产品程序号")
    @PreAuthorize("@ss.hasPermi('zhenghe:program:add')")
    @Log(title = "产品程序号", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZhProductProgram zhProductProgram)
    {
        return toAjax(zhProductProgramService.insertZhProductProgram(zhProductProgram));
    }

    /**
     * 修改产品程序号
     */
    @ApiOperation(value = "修改产品程序号")
    @PreAuthorize("@ss.hasPermi('zhenghe:program:edit')")
    @Log(title = "产品程序号", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhProductProgram zhProductProgram)
    {
        return toAjax(zhProductProgramService.updateZhProductProgram(zhProductProgram));
    }

    /**
     * 删除产品程序号
     */
    @ApiOperation(value = "删除产品程序号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids",value = "产品程序号id",required = true,paramType = "path",dataType = "long")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:program:remove')")
    @Log(title = "产品程序号", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(zhProductProgramService.deleteZhProductProgramByIds(ids));
    }
}
