package com.ruoyi.zhenghe.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.zhenghe.domain.ZhEquipmentProp;
import com.ruoyi.zhenghe.service.IZhEquipmentPropService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备属性配置Controller
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Api(tags = "设备台账属性配置")
@RestController
@RequestMapping("/zhenghe/equipmentProp")
public class ZhEquipmentPropController extends BaseController {


    private static final Logger log = LoggerFactory.getLogger(ZhEquipmentPropController.class);

    @Autowired
    private IZhEquipmentPropService zhEquipmentPropService;

    /**
     * 查询设备属性配置列表
     */
    @ApiOperation(value = "设备属性配置列表获取")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "设备id", required = true, dataType = "Long", paramType = "query"),
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:equipmentProp:list')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam(name = "id", required = true) Long id) {
        // 初始化分页参数，查询所有记录
        PageUtils.offsetPage(0, 999);
        // 根据设备id查询设备属性配置列表
        List<ZhEquipmentProp> list = zhEquipmentPropService.selectZhEquipmentPropByEquipmentId(id);
        // 将查询结果转换为TableDataInfo对象并返回
        return getDataTable(list);
    }

    /**
     * 导出设备属性配置列表
     */
    @PreAuthorize("@ss.hasPermi('zhenghe:equipmentProp:export')")
    @Log(title = "设备属性配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhEquipmentProp zhEquipmentProp) {
        List<ZhEquipmentProp> list = zhEquipmentPropService.selectZhEquipmentPropList(zhEquipmentProp);
        ExcelUtil<ZhEquipmentProp> util = new ExcelUtil<ZhEquipmentProp>(ZhEquipmentProp.class);
        util.exportExcel(response, list, "设备属性配置数据");
    }

    /**
     * 获取设备属性配置详细信息
     */
//    @PreAuthorize("@ss.hasPermi('zhenghe:equipmentProp:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id) {
//        return success(zhEquipmentPropService.selectZhEquipmentPropById(id));
//    }

    /**
     * 新增设备属性配置
     */
    @ApiOperation(value = "新增设备属性配置")
    @PreAuthorize("@ss.hasPermi('zhenghe:equipmentProp:add')")
    @Log(title = "设备属性配置", businessType = BusinessType.INSERT)
    @Transactional
    @PostMapping
    public AjaxResult add(@RequestBody List<ZhEquipmentProp> zhEquipmentProp) {
        // 边界条件检查
        if (zhEquipmentProp == null || zhEquipmentProp.isEmpty()) {
            return AjaxResult.error("设备属性配置列表不能为空");
        }

        // 获取当前用户
        String currentUsername = getUsername();
        if (currentUsername == null || currentUsername.isEmpty()) {
            return AjaxResult.error("无法获取当前用户信息");
        }

        // 插入设备属性配置
        for (ZhEquipmentProp zhEquipmentProp1 : zhEquipmentProp) {
            try {
                // 设置创建人和创建时间
                zhEquipmentProp1.setCreateBy(currentUsername);
                zhEquipmentProp1.setCreateTime(DateUtils.getNowDate());
                int insertResult;
                if (zhEquipmentProp1.getId() == null) {
                    insertResult = zhEquipmentPropService.insertZhEquipmentProp(zhEquipmentProp1);
                } else {
                    insertResult = zhEquipmentPropService.updateZhEquipmentProp(zhEquipmentProp1);
                }
                // 执行插入操作
                if (insertResult <= 0) {
                    return AjaxResult.error("新增设备属性配置失败");
                }
            } catch (Exception e) {
                // 记录错误日志并抛出异常，触发事务回滚
                log.error("新增设备属性配置失败，数据：{}", zhEquipmentProp1, e);
                return AjaxResult.error("新增设备属性配置失败，请检查日志");
            }
        }

        // 返回成功结果
        return toAjax(1);
    }


    /**
     * 修改设备属性配置
     */
    @ApiOperation(value = "修改设备属性配置")
    @PreAuthorize("@ss.hasPermi('zhenghe:equipmentProp:edit')")
    @Log(title = "设备属性配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhEquipmentProp zhEquipmentProp) {
        return toAjax(zhEquipmentPropService.updateZhEquipmentProp(zhEquipmentProp));
    }

    /**
     * 删除设备属性配置
     */
//    @PreAuthorize("@ss.hasPermi('zhenghe:equipmentProp:remove')")
//    @Log(title = "设备属性配置", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids) {
//        return toAjax(zhEquipmentPropService.deleteZhEquipmentPropByIds(ids));
//    }

    /**
     * 根据设备id查询设备已配置属性中勾选了报警配置的属性
     */
    @ApiOperation(value = "设备属性勾选了报警配置的配置列表获取")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "设备id", required = true, dataType = "Long", paramType = "query"),
    })
    @GetMapping("/selectPropByAlarm")
    public TableDataInfo selectZhEquipmentPropByEquipmentIdAndAlarm(@RequestParam(name = "id", required = true) Long id) {
        // 初始化分页参数，查询所有记录
        PageUtils.offsetPage(0, 999);
        // 根据设备id查询设备属性配置列表
        List<ZhEquipmentProp> list = zhEquipmentPropService.selectZhEquipmentPropByEquipmentIdAndAlarm(id);
        // 将查询结果转换为TableDataInfo对象并返回
        return getDataTable(list);
    }

}
