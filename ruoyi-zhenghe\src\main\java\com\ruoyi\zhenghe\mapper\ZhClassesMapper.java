package com.ruoyi.zhenghe.mapper;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhClasses;

/**
 * 班次Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface ZhClassesMapper 
{
    /**
     * 查询班次
     * 
     * @param id 班次主键
     * @return 班次
     */
    public ZhClasses selectZhClassesById(Long id);

    /**
     * 查询班次列表
     * 
     * @param zhClasses 班次
     * @return 班次集合
     */
    public List<ZhClasses> selectZhClassesList(ZhClasses zhClasses);

    /**
     * 新增班次
     * 
     * @param zhClasses 班次
     * @return 结果
     */
    public int insertZhClasses(ZhClasses zhClasses);

    /**
     * 修改班次
     * 
     * @param zhClasses 班次
     * @return 结果
     */
    public int updateZhClasses(ZhClasses zhClasses);

    /**
     * 删除班次
     * 
     * @param id 班次主键
     * @return 结果
     */
    public int deleteZhClassesById(Long id);

    /**
     * 批量删除班次
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZhClassesByIds(Long[] ids);
}
