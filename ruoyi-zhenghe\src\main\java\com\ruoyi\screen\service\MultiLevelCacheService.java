package com.ruoyi.screen.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.screen.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 多级缓存服务
 * 提供设备级、时间段级、结果级的多层缓存机制
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
@Slf4j
@Service
public class MultiLevelCacheService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisUtils redisUtils;

    // 缓存过期时间配置
    private static final int DEVICE_CACHE_SECONDS = 300;      // 设备级缓存5分钟
    private static final int TIME_SEGMENT_CACHE_SECONDS = 1800; // 时间段缓存30分钟
    private static final int RESULT_CACHE_SECONDS = 600;       // 结果缓存10分钟
    private static final int DAILY_CACHE_SECONDS = 3600 * 24;  // 日级缓存24小时

    /**
     * 获取设备生产计数缓存
     */
    public Integer getDeviceProductionCount(String equipmentCode, String startTime, String endTime) {
        String cacheKey = buildDeviceCountKey(equipmentCode, startTime, endTime);
        return redisCache.getCacheObject(cacheKey);
    }

    /**
     * 设置设备生产计数缓存
     */
    public void setDeviceProductionCount(String equipmentCode, String startTime, String endTime, Integer count) {
        String cacheKey = buildDeviceCountKey(equipmentCode, startTime, endTime);
        int expireSeconds = isHistoricalData(endTime) ? DAILY_CACHE_SECONDS : DEVICE_CACHE_SECONDS;
        redisCache.setCacheObject(cacheKey, count, expireSeconds, TimeUnit.SECONDS);
    }

    /**
     * 获取设备生产时长缓存
     */
    public Integer getDeviceProductionDuration(String equipmentCode, String code, String startTime, String endTime) {
        String cacheKey = buildDeviceDurationKey(equipmentCode, code, startTime, endTime);
        return redisCache.getCacheObject(cacheKey);
    }

    /**
     * 设置设备生产时长缓存
     */
    public void setDeviceProductionDuration(String equipmentCode, String code, String startTime, String endTime, Integer duration) {
        String cacheKey = buildDeviceDurationKey(equipmentCode, code, startTime, endTime);
        int expireSeconds = isHistoricalData(endTime) ? DAILY_CACHE_SECONDS : DEVICE_CACHE_SECONDS;
        redisCache.setCacheObject(cacheKey, duration, expireSeconds, TimeUnit.SECONDS);
    }

    /**
     * 获取设备能耗数据缓存
     */
    public Integer getDeviceEnergyData(Long equipmentId, String startTime, String endTime) {
        String cacheKey = buildDeviceEnergyKey(equipmentId, startTime, endTime);
        return redisCache.getCacheObject(cacheKey);
    }

    /**
     * 设置设备能耗数据缓存
     */
    public void setDeviceEnergyData(Long equipmentId, String startTime, String endTime, Integer energy) {
        String cacheKey = buildDeviceEnergyKey(equipmentId, startTime, endTime);
        int expireSeconds = isHistoricalData(endTime) ? DAILY_CACHE_SECONDS : DEVICE_CACHE_SECONDS;
        redisCache.setCacheObject(cacheKey, energy, expireSeconds, TimeUnit.SECONDS);
    }

    /**
     * 获取时间段聚合数据缓存
     */
    public JSONObject getTimeSegmentData(String deptId, String workshopId, String dataType, String timeSegment) {
        String cacheKey = buildTimeSegmentKey(deptId, workshopId, dataType, timeSegment);
        Object cached = redisCache.getCacheObject(cacheKey);
        return cached != null ? JSONObject.parseObject(cached.toString()) : null;
    }

    /**
     * 设置时间段聚合数据缓存
     */
    public void setTimeSegmentData(String deptId, String workshopId, String dataType, String timeSegment, JSONObject data) {
        String cacheKey = buildTimeSegmentKey(deptId, workshopId, dataType, timeSegment);
        int expireSeconds = isHistoricalTimeSegment(timeSegment) ? DAILY_CACHE_SECONDS : TIME_SEGMENT_CACHE_SECONDS;
        redisCache.setCacheObject(cacheKey, data.toJSONString(), expireSeconds, TimeUnit.SECONDS);
    }

    /**
     * 获取预计算结果缓存
     */
    public JSONObject getPrecomputedResult(String resultType, String deptId, String workshopId, String timeRange) {
        String cacheKey = buildPrecomputedKey(resultType, deptId, workshopId, timeRange);
        Object cached = redisCache.getCacheObject(cacheKey);
        return cached != null ? JSONObject.parseObject(cached.toString()) : null;
    }

    /**
     * 设置预计算结果缓存
     */
    public void setPrecomputedResult(String resultType, String deptId, String workshopId, String timeRange, JSONObject result) {
        String cacheKey = buildPrecomputedKey(resultType, deptId, workshopId, timeRange);
        redisCache.setCacheObject(cacheKey, result.toJSONString(), RESULT_CACHE_SECONDS, TimeUnit.SECONDS);
    }

    /**
     * 批量删除相关缓存
     */
    public void evictRelatedCache(String equipmentCode) {
        try {
            // 删除设备相关的所有缓存
            String pattern = "device:*:" + equipmentCode + ":*";
            int deletedCount = redisUtils.deleteByPattern(pattern);
            log.info("删除设备 {} 相关缓存，共 {} 个键", equipmentCode, deletedCount);
        } catch (Exception e) {
            log.error("删除设备 {} 相关缓存失败", equipmentCode, e);
        }
    }

    /**
     * 构建设备计数缓存键
     */
    private String buildDeviceCountKey(String equipmentCode, String startTime, String endTime) {
        return String.format("device:count:%s:%s:%s", equipmentCode, 
            DateUtil.format(DateUtil.parse(startTime), "yyyyMMddHH"),
            DateUtil.format(DateUtil.parse(endTime), "yyyyMMddHH"));
    }

    /**
     * 构建设备时长缓存键
     */
    private String buildDeviceDurationKey(String equipmentCode, String code, String startTime, String endTime) {
        return String.format("device:duration:%s:%s:%s:%s", equipmentCode, code,
            DateUtil.format(DateUtil.parse(startTime), "yyyyMMddHH"),
            DateUtil.format(DateUtil.parse(endTime), "yyyyMMddHH"));
    }

    /**
     * 构建设备能耗缓存键
     */
    private String buildDeviceEnergyKey(Long equipmentId, String startTime, String endTime) {
        return String.format("device:energy:%d:%s:%s", equipmentId,
            DateUtil.format(DateUtil.parse(startTime), "yyyyMMddHH"),
            DateUtil.format(DateUtil.parse(endTime), "yyyyMMddHH"));
    }

    /**
     * 构建时间段缓存键
     */
    private String buildTimeSegmentKey(String deptId, String workshopId, String dataType, String timeSegment) {
        return String.format("segment:%s:%s:%s:%s", 
            deptId == null ? "all" : deptId,
            workshopId == null ? "all" : workshopId,
            dataType, timeSegment);
    }

    /**
     * 构建预计算结果缓存键
     */
    private String buildPrecomputedKey(String resultType, String deptId, String workshopId, String timeRange) {
        return String.format("precomputed:%s:%s:%s:%s", resultType,
            deptId == null ? "all" : deptId,
            workshopId == null ? "all" : workshopId,
            timeRange);
    }

    /**
     * 判断是否为历史数据（昨天之前的数据）
     */
    private boolean isHistoricalData(String endTime) {
        try {
            Date endDate = DateUtil.parse(endTime);
            Date yesterday = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -1));
            return endDate.before(yesterday);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断是否为历史时间段
     */
    private boolean isHistoricalTimeSegment(String timeSegment) {
        try {
            // 如果时间段是昨天之前的，认为是历史数据
            return timeSegment.contains(DateUtil.format(DateUtil.offsetDay(new Date(), -2), "MM-dd"));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取缓存统计信息
     */
    public JSONObject getCacheStats() {
        JSONObject stats = new JSONObject();
        // 这里可以添加缓存命中率等统计信息
        stats.put("deviceCacheExpire", DEVICE_CACHE_SECONDS);
        stats.put("timeSegmentCacheExpire", TIME_SEGMENT_CACHE_SECONDS);
        stats.put("resultCacheExpire", RESULT_CACHE_SECONDS);
        stats.put("dailyCacheExpire", DAILY_CACHE_SECONDS);
        return stats;
    }
}
