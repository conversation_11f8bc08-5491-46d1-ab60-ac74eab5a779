package com.ruoyi.zhenghe.controller;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.zhenghe.domain.*;
import com.ruoyi.zhenghe.mapper.ZhBaseProcessMapper;
import com.ruoyi.zhenghe.mapper.ZhDeviceTypeMapper;
import com.ruoyi.zhenghe.service.IZhIotEquipmentService;
import com.ruoyi.zhenghe.service.IZhWorkshopService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * 物联网设备明细Controller
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Api(tags = "设备台账")
@RestController
@RequestMapping("/zhenghe/equipment")
public class ZhIotEquipmentController extends BaseController {
    @Autowired
    private IZhIotEquipmentService zhIotEquipmentService;
    @Autowired
    private IZhWorkshopService zhWorkshopService;
    @Autowired
    private SysDeptMapper deptMapper;
    @Resource
    private ZhDeviceTypeMapper deviceTypeMapper;
    @Resource
    ZhBaseProcessMapper baseProcessMapper;

    /**
     * 查询物联网设备明细列表
     */
    @ApiOperation(value = "查询设备明细列表", response = ZhIotEquipment.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "zhIotEquipment", value = "物联网设备明细", required = true, paramType = "body", dataType = "ZhIotEquipment")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:list')")
    @GetMapping("/list")
    public TableDataInfo list(ZhIotEquipment zhIotEquipment) {
        startPage();
        List<ZhIotEquipment> list = zhIotEquipmentService.selectZhIotEquipmentList(zhIotEquipment);
        return getDataTable(list);
    }

    /**
     * 查询物联网设备明细列表
     */
    @ApiOperation(value = "查询设备明细列表2", response = ZhIotEquipment.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "departmentIds", value = "部门id数组", required = true, paramType = "body", dataType = "List<String>"),
            @ApiImplicitParam(name = "workshopIds", value = "车间id数组", required = true, paramType = "body", dataType = "List<String>"),
            @ApiImplicitParam(name = "deviceTypeIds", value = "设备类型id数组", required = true, paramType = "body", dataType = "List<String>"),
            @ApiImplicitParam(name = "processIds", value = "工序id数组", required = true, paramType = "body", dataType = "List<String>")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:list')")
    @GetMapping("/list2")
    public TableDataInfo list2(ZhIotEquipment2QueryVo zhIotEquipment2QueryVo) {
        startPage();
        List<ZhIotEquipment> list = zhIotEquipmentService.selectZhIotEquipmentList2(zhIotEquipment2QueryVo);
        return getDataTable(list);
    }

    /**
     * 导出物联网设备明细列表
     */
    @ApiOperation(value = "导出设备明细列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "zhIotEquipment", value = "物联网设备明细", required = true, paramType = "body", dataType = "ZhIotEquipment")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:export')")
    @Log(title = "设备明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhIotEquipment zhIotEquipment) {
        List<ZhIotEquipment> list = zhIotEquipmentService.selectZhIotEquipmentList(zhIotEquipment);
        List<ZhIotEquipmentExport> listExport = BeanUtil.copyToList(list, ZhIotEquipmentExport.class);
        ExcelUtil<ZhIotEquipmentExport> util = new ExcelUtil<>(ZhIotEquipmentExport.class);
        util.exportExcel(response, listExport, "物联网设备明细数据");
    }


    @ApiOperation(value = "导入设备模板下载")
    @ApiImplicitParams({
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:import')")
    @PostMapping("/download/template")
    public void downloadTemplate(HttpServletResponse response, ZhIotEquipment zhIotEquipment) {
        List<ZhIotEquipment> list = new ArrayList<>();
        List<ZhIotEquipmentExport> listExport = BeanUtil.copyToList(list, ZhIotEquipmentExport.class);
        ExcelUtil<ZhIotEquipmentExport> util = new ExcelUtil<>(ZhIotEquipmentExport.class);
        util.exportExcel(response, listExport, "物联网设备");
    }


    @ApiOperation(value = "导入设备表")
    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws IOException {
        ExcelUtil<ZhIotEquipment> util = new ExcelUtil<>(ZhIotEquipment.class);
        List<ZhIotEquipment> equipmentList = util.importExcel(file.getInputStream());

        if (StringUtils.isNull(equipmentList) || equipmentList.size() == 0) {
            throw new ServiceException("导入设备数据不能为空");
        }
        int successNum = 0;
        int failureNum = 0;

        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        final List<SysDept> sysDepts = deptMapper.selectDeptList(new SysDept());
        Map<String, Long> deptMap = new HashMap<>();
        for (SysDept sysDept : sysDepts) {
            deptMap.put(sysDept.getDeptName(), sysDept.getDeptId());
        }

        for (ZhIotEquipment equipment : equipmentList) {
            if(equipment.getDeptName() == null){
                failureNum++;
                failureMsg.append("<br/>" + failureNum + "、设备 " + equipment.getEquipmentName() + " 导入失败：部门不能为空");
                continue;
            }
            try {
                if (deptMap.containsKey(equipment.getDeptName())) {
                    equipment.setDeptId(deptMap.get(equipment.getDeptName()));
                } else {
                    equipment.setDeptId(SecurityUtils.getDeptId());
                }

                if (equipment.getDeviceType() != null) {
                    equipment.setDeviceTypeId(Long.parseLong(equipment.getDeviceType()));
                }
                if (equipment.getWorkshopName() != null) {
                    equipment.setWorkshopId(Long.parseLong(equipment.getWorkshopName()));

                }
                if (equipment.getProcess() != null) {
                    equipment.setProcessId(Long.parseLong(equipment.getProcess()));
                }

                zhIotEquipmentService.insertZhIotEquipment(equipment);
                successNum++;
                successMsg.append("<br/>" + successNum + "、设备 " + equipment.getEquipmentName() + " 导入成功");
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、设备 " + equipment.getEquipmentName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return AjaxResult.success(successMsg.toString());
    }

    /**
     * 获取物联网设备明细详细信息
     */
    @ApiOperation(value = "获取设备明细详细信息", response = ZhIotEquipment.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "物联网设备明细id", required = true, paramType = "path", dataType = "long")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(zhIotEquipmentService.selectZhIotEquipmentById(id));
    }

    /**
     * 新增物联网设备明细
     */
    @ApiOperation(value = "新增设备")
    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:add')")
    @Log(title = "物联网设备明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZhIotEquipment zhIotEquipment) {
        return toAjax(zhIotEquipmentService.insertZhIotEquipment(zhIotEquipment));
    }

    /**
     * 修改物联网设备明细
     */
    @ApiOperation(value = "修改设备明细")
    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:edit')")
    @Log(title = "物联网设备明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhIotEquipment zhIotEquipment) {
        return toAjax(zhIotEquipmentService.updateZhIotEquipment(zhIotEquipment));
    }

    /**
     * 删除物联网设备明细
     */
    @ApiOperation(value = "删除设备by ids")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "主键ID串", required = true, paramType = "path", dataType = "Long"),
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:remove')")
    @Log(title = "物联网设备明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(zhIotEquipmentService.deleteZhIotEquipmentByIds(ids));
    }

    @ApiOperation(value = "筛选栏接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "departmentIds", value = "部门id数组", required = true, paramType = "body", dataType = "String[]"),
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:list')")
    @GetMapping("/filter/bar")
    public AjaxResult filterBar(Long[] departmentIds) {
        if (departmentIds == null || departmentIds.length == 0) {
            return AjaxResult.success();
        }
        DeptWorkshopTypeProcess deptWorkshopTypeProcess = new DeptWorkshopTypeProcess();
        List<ZhWorkshop> workshopList = new ArrayList<>();
        List<ZhBaseProcess> processList = new ArrayList<>();
        List<ZhDeviceType> deviceTypeList = new ArrayList<>();
        for (Long oneDept : departmentIds) {
            // 车间
            ZhWorkshop zhWorkshop = new ZhWorkshop();
            PageUtils.startPage(1, 999);
            zhWorkshop.setDeptId(oneDept);
            List<ZhWorkshop> list = zhWorkshopService.selectOnlyZhWorkshopList(zhWorkshop);
            workshopList.addAll(list);
            // 设备类型
            ZhDeviceType zhDeviceType = new ZhDeviceType();
            PageUtils.startPage(1, 999);
            zhDeviceType.setDeptId(oneDept);
            List<ZhDeviceType> zhDeviceTypeList = deviceTypeMapper.selectZhDeviceTypeList(zhDeviceType);
            deviceTypeList.addAll(zhDeviceTypeList);
            // 工序
            ZhBaseProcess process = new ZhBaseProcess();
            process.setDeptId(oneDept);
            PageUtils.startPage(1, 999);
            List<ZhBaseProcess> zhBaseProcessList = baseProcessMapper.selectZhBaseProcessList(process);
            processList.addAll(zhBaseProcessList);
        }
        deptWorkshopTypeProcess.setWorkshopList(workshopList);
        deptWorkshopTypeProcess.setDeviceTypeList(deviceTypeList);
        deptWorkshopTypeProcess.setProcessList(processList);
        return AjaxResult.success(deptWorkshopTypeProcess);
    }
}
