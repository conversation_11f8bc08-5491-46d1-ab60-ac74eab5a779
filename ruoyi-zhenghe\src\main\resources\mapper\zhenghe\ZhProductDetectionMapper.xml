<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhProductDetectionMapper">

    <resultMap type="ZhProductDetection" id="ZhProductDetectionResult">
        <result property="id" column="id"/>
        <result property="productId" column="product_id"/>
        <result property="batchNo" column="batch_no"/>
        <result property="totalCount" column="total_count"/>
        <result property="qualifiedCount" column="qualified_count"/>
        <result property="defectCount" column="defect_count"/>
        <result property="defectData" column="defect_data"/>
        <result property="detectionTime" column="detection_time"/>
        <result property="operator" column="operator"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <resultMap type="ZhProductDetection" id="ZhProductDetectionWithProductResult" extends="ZhProductDetectionResult">
        <association property="product" javaType="ZhProduct">
            <result property="id" column="p_id"/>
            <result property="productModel" column="p_product_model"/>
            <result property="pieceCount" column="p_piece_count"/>
            <result property="sectionCount" column="p_section_count"/>
            <result property="timingMark" column="p_timing_mark"/>
            <result property="productImage" column="p_product_image"/>
        </association>
    </resultMap>

    <resultMap type="DetectionSummaryDto" id="DetectionSummaryResult">
        <result property="serialNumber" column="serial_number"/>
        <result property="productModel" column="product_model"/>
        <result property="pieceCount" column="piece_count"/>
        <result property="sectionCount" column="section_count"/>
        <result property="timingMark" column="timing_mark"/>
        <result property="totalCount" column="total_count"/>
        <result property="qualifiedCount" column="qualified_count"/>
        <result property="defectCount" column="defect_count"/>
        <result property="detectionDate" column="detection_date"/>
        <result property="actionLink" column="action_link"/>
        <result property="productId" column="product_id"/>
        <result property="customerName" column="customer_name"/>
        <result property="machineType" column="machine_type"/>
        <result property="printMark" column="print_mark"/>
        <result property="oe" column="oe"/>
        <result property="chainLength" column="chain_length"/>
        <result property="chainLengthTolerance" column="chain_length_tolerance"/>
    </resultMap>

    <resultMap type="ProductDetectionChartDto" id="ProductDetectionChartResult">
        <result property="productModel" column="product_model"/>
        <result property="pieceCount" column="piece_count"/>
        <result property="sectionCount" column="section_count"/>
        <result property="timingMark" column="timing_mark"/>
        <result property="qualifiedCount" column="qualified_count"/>
        <result property="defectCount" column="defect_count"/>
        <result property="totalCount" column="total_count"/>
        <result property="qualifiedRate" column="qualified_rate"/>
        <result property="defectRate" column="defect_rate"/>
        <result property="customerName" column="customer_name"/>
        <result property="machineType" column="machine_type"/>
        <result property="printMark" column="print_mark"/>
        <result property="oe" column="oe"/>
        <result property="chainLength" column="chain_length"/>
        <result property="chainLengthTolerance" column="chain_length_tolerance"/>
    </resultMap>

    <sql id="selectZhProductDetectionVo">
        SELECT d.id,
               d.product_id,
               d.batch_no,
               d.total_count,
               d.qualified_count,
               d.defect_count,
               d.defect_data,
               d.detection_time,
               d.operator,
               d.status,
               d.create_by,
               d.create_time,
               d.update_by,
               d.update_time,
               d.remark
        FROM zh_product_detection d
    </sql>

    <sql id="selectZhProductDetectionWithProductVo">
        SELECT d.id,
               d.product_id,
               d.batch_no,
               d.total_count,
               d.qualified_count,
               d.defect_count,
               d.defect_data,
               d.detection_time,
               d.operator,
               d.status,
               d.create_by,
               d.create_time,
               d.update_by,
               d.update_time,
               d.remark,
               p.id as p_id,
               p.product_model as p_product_model,
               p.piece_count as p_piece_count,
               p.section_count as p_section_count,
               p.timing_mark as p_timing_mark,
               p.product_image as p_product_image
        FROM zh_product_detection d
        LEFT JOIN zh_product p ON d.product_id = p.id
    </sql>

    <select id="selectZhProductDetectionList" parameterType="ZhProductDetection" resultMap="ZhProductDetectionWithProductResult">
        <include refid="selectZhProductDetectionWithProductVo"/>
        WHERE d.status = 1
        <if test="productId != null">
            AND d.product_id = #{productId}
        </if>
        <if test="batchNo != null and batchNo != ''">
            AND d.batch_no LIKE CONCAT('%', #{batchNo}, '%')
        </if>
        <if test="operator != null and operator != ''">
            AND d.operator LIKE CONCAT('%', #{operator}, '%')
        </if>
        <if test="detectionTime != null">
            AND DATE(d.detection_time) = DATE(#{detectionTime})
        </if>
        <if test="params.beginTime != null and params.beginTime != ''">
            AND d.detection_time &gt;= #{params.beginTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND d.detection_time &lt;= #{params.endTime}
        </if>
        ORDER BY d.detection_time DESC
    </select>

    <select id="selectZhProductDetectionById" parameterType="Long" resultMap="ZhProductDetectionWithProductResult">
        <include refid="selectZhProductDetectionWithProductVo"/>
        WHERE d.id = #{id}
    </select>

    <insert id="insertZhProductDetection" parameterType="ZhProductDetection" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO zh_product_detection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="totalCount != null">total_count,</if>
            <if test="qualifiedCount != null">qualified_count,</if>
            <if test="defectCount != null">defect_count,</if>
            <if test="defectData != null">defect_data,</if>
            <if test="detectionTime != null">detection_time,</if>
            <if test="operator != null">operator,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="totalCount != null">#{totalCount},</if>
            <if test="qualifiedCount != null">#{qualifiedCount},</if>
            <if test="defectCount != null">#{defectCount},</if>
            <if test="defectData != null">#{defectData},</if>
            <if test="detectionTime != null">#{detectionTime},</if>
            <if test="operator != null">#{operator},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateZhProductDetection" parameterType="ZhProductDetection">
        UPDATE zh_product_detection
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="totalCount != null">total_count = #{totalCount},</if>
            <if test="qualifiedCount != null">qualified_count = #{qualifiedCount},</if>
            <if test="defectCount != null">defect_count = #{defectCount},</if>
            <if test="defectData != null">defect_data = #{defectData},</if>
            <if test="detectionTime != null">detection_time = #{detectionTime},</if>
            <if test="operator != null">operator = #{operator},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <delete id="deleteZhProductDetectionById" parameterType="Long">
        DELETE FROM zh_product_detection WHERE id = #{id}
    </delete>

    <delete id="deleteZhProductDetectionByIds" parameterType="String">
        DELETE FROM zh_product_detection WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectZhProductDetectionByProductId" parameterType="Long" resultMap="ZhProductDetectionResult">
        <include refid="selectZhProductDetectionVo"/>
        WHERE d.product_id = #{productId} AND d.status = 1
        ORDER BY d.detection_time DESC
    </select>

    <!-- 查询产品检测记录分组列表（按日期分组） -->
    <select id="selectZhProductDetectionGroupList" parameterType="ZhProductDetection" resultMap="DetectionSummaryResult">
        SELECT
            ROW_NUMBER() OVER (ORDER BY DATE(d.detection_time) DESC,p.customer_name, p.product_model, p.machine_type, p.print_mark, p.timing_mark) as serial_number,
            p.customer_name,
            p.product_model,
            p.machine_type,
            p.print_mark,
            p.timing_mark,
            p.id as product_id,
            p.oe,
            p.chain_length,
            p.chain_length_tolerance,
            SUM(d.total_count) as total_count,
            SUM(d.qualified_count) as qualified_count,
            SUM(d.defect_count) as defect_count,
            DATE(d.detection_time) as detection_date,
            CONCAT('查看产品瑕疵细分') as action_link
        FROM zh_product_detection d
        LEFT JOIN zh_product p ON d.product_id = p.id
        WHERE d.status = 1
        <if test="productId != null">
            AND d.product_id = #{productId}
        </if>
        <if test="product != null">
            <if test="product.customerName != null and product.customerName != ''">
                AND p.customer_name LIKE CONCAT('%', #{product.customerName}, '%')
            </if>
            <if test="product.productModel != null and product.productModel != ''">
                AND p.product_model LIKE CONCAT('%', #{product.productModel}, '%')
            </if>
          <if test="product.machineType != null and product.machineType != ''">
                AND p.machine_type LIKE CONCAT('%', #{product.machineType}, '%')
            </if>
            <if test="product.timingMark != null and product.timingMark != ''">
                AND p.timing_mark LIKE CONCAT('%', #{product.timingMark}, '%')
            </if>
            <if test="product.printMark != null and product.printMark != ''">
                AND p.print_mark LIKE CONCAT('%', #{product.printMark}, '%')
            </if>
        </if>
        <if test="params.beginTime != null and params.beginTime != ''">
            AND d.detection_time &gt;= #{params.beginTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND d.detection_time &lt;= #{params.endTime}
        </if>
        GROUP BY DATE(d.detection_time), d.product_id, p.customer_name,p.product_model, p.machine_type, p.print_mark, p.timing_mark
        ORDER BY DATE(d.detection_time) DESC, p.customer_name,p.product_model, p.machine_type, p.print_mark, p.timing_mark
    </select>

    <!-- 根据产品ID和时间范围查询检测统计数据 -->
    <select id="selectDetectionStatisticsByProductId" resultMap="DetectionSummaryResult">
        SELECT
            ROW_NUMBER() OVER (ORDER BY DATE(d.detection_time) DESC) as serial_number,
            p.product_model,
            p.piece_count,
            p.section_count,
            p.timing_mark,
            SUM(d.total_count) as total_count,
            SUM(d.qualified_count) as qualified_count,
            SUM(d.defect_count) as defect_count,
            DATE(d.detection_time) as detection_date
        FROM zh_product_detection d
        LEFT JOIN zh_product p ON d.product_id = p.id
        WHERE d.status = 1 AND d.product_id = #{productId}
        <if test="startTime != null">
            AND d.detection_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND d.detection_time &lt;= #{endTime}
        </if>
        GROUP BY DATE(d.detection_time), d.product_id, p.product_model, p.piece_count, p.section_count, p.timing_mark
        ORDER BY DATE(d.detection_time) DESC
    </select>

    <!-- 根据产品ID和检测日期查询瑕疵细分数据 -->
    <select id="selectDefectDetailsByProductIdAndDate" resultMap="ZhProductDetectionResult">
        <include refid="selectZhProductDetectionVo"/>
        WHERE d.product_id = #{productId}
          AND DATE(d.detection_time) = DATE(#{detectionDate})
          AND d.status = 1
        ORDER BY d.detection_time DESC
    </select>

    <!-- 查询检测记录分析数据（按产品分组统计） -->
    <select id="selectDetectionAnalysisData" resultMap="DetectionSummaryResult">
        SELECT
            p.product_model,
            p.piece_count,
            p.section_count,
            p.timing_mark,
            SUM(d.total_count) as total_count,
            SUM(d.qualified_count) as qualified_count,
            SUM(d.defect_count) as defect_count
        FROM zh_product_detection d
        LEFT JOIN zh_product p ON d.product_id = p.id
        WHERE d.status = 1
        <if test="productId != null">
            AND d.product_id = #{productId}
        </if>
        <if test="startTime != null">
            AND d.detection_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND d.detection_time &lt;= #{endTime}
        </if>
        GROUP BY d.product_id, p.product_model, p.piece_count, p.section_count, p.timing_mark
        ORDER BY p.product_model, p.piece_count, p.section_count, p.timing_mark
    </select>

    <!-- 根据时间范围查询所有产品的检测汇总数据（按日期和产品分组） -->
    <select id="selectDetectionSummaryByDateRange" resultMap="DetectionSummaryResult">
        SELECT
            ROW_NUMBER() OVER (ORDER BY DATE(d.detection_time) DESC, p.product_model, p.piece_count, p.section_count, p.timing_mark) as serial_number,
            p.product_model,
            p.piece_count,
            p.section_count,
            p.timing_mark,
            SUM(d.total_count) as total_count,
            SUM(d.qualified_count) as qualified_count,
            SUM(d.defect_count) as defect_count,
            DATE(d.detection_time) as detection_date,
            CONCAT('查看产品瑕疵细分') as action_link
        FROM zh_product_detection d
        LEFT JOIN zh_product p ON d.product_id = p.id
        WHERE d.status = 1
        <if test="startTime != null">
            AND d.detection_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND d.detection_time &lt;= #{endTime}
        </if>
        GROUP BY DATE(d.detection_time), d.product_id, p.product_model, p.piece_count, p.section_count, p.timing_mark
        ORDER BY DATE(d.detection_time) DESC, p.product_model, p.piece_count, p.section_count, p.timing_mark
    </select>

    <!-- 查询柱状图数据（按产品组合分组统计） -->
    <select id="selectChartData" resultMap="ProductDetectionChartResult">
        SELECT
        p.product_model,
        p.customer_name,
        p.machine_type,
        p.print_mark,
        p.piece_count,
        p.section_count,
        p.timing_mark,
        p.oe,
        p.chain_length,
        p.chain_length_tolerance,
        SUM(d.qualified_count) as qualified_count,
        SUM(d.defect_count) as defect_count,
        SUM(d.total_count) as total_count,
        ROUND(SUM(d.qualified_count) * 100.0 / SUM(d.total_count), 2) as qualified_rate,
        ROUND(SUM(d.defect_count) * 100.0 / SUM(d.total_count), 2) as defect_rate
        FROM zh_product_detection d
        LEFT JOIN zh_product p ON d.product_id = p.id
        WHERE d.status = 1
        <if test="productModel != null and productModel != ''">
            AND p.product_model LIKE CONCAT('%', #{productModel}, '%')
        </if>


        <if test="customerName != null and customerName != ''">
            AND p.customer_name LIKE CONCAT('%', #{.customerName}, '%')
        </if>
        <if test="machineType != null and machineType != ''">
            AND p.machine_type LIKE CONCAT('%', #{machineType}, '%')
        </if>
        <if test="printMark != null and printMark != ''">
            AND p.print_mark LIKE CONCAT('%', #{printMark}, '%')
        </if>

        <if test="timingMark != null and timingMark != ''">
            AND p.timing_mark LIKE CONCAT('%', #{timingMark}, '%')
        </if>
        <if test="startTime != null">
            AND d.detection_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND d.detection_time &lt;= #{endTime}
        </if>
        GROUP BY p.product_model, p.piece_count, p.section_count, p.timing_mark
        HAVING SUM(d.total_count) > 0
        ORDER BY SUM(d.total_count) DESC, p.product_model, p.piece_count, p.section_count, p.timing_mark
    </select>

</mapper>
