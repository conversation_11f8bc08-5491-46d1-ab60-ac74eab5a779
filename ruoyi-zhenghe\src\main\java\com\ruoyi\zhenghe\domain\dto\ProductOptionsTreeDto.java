package com.ruoyi.zhenghe.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 产品选项树状结构数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
public class ProductOptionsTreeDto {

    /** 产品型号级别 */
    @ApiModelProperty(value = "产品型号")
    private String productModel;

    /** 该产品型号下的片数选项 */
    @ApiModelProperty(value = "片数选项列表")
    private List<PieceCountOption> pieceCountOptions;

    @Data
    public static class PieceCountOption {
        /** 片数 */
        @ApiModelProperty(value = "片数")
        private Integer pieceCount;

        /** 该片数下的节数选项 */
        @ApiModelProperty(value = "节数选项列表")
        private List<SectionCountOption> sectionCountOptions;
    }

    @Data
    public static class SectionCountOption {
        /** 节数 */
        @ApiModelProperty(value = "节数")
        private Integer sectionCount;

        /** 该节数下的正时标记选项 */
        @ApiModelProperty(value = "正时标记选项列表")
        private List<TimingMarkOption> timingMarkOptions;
    }

    @Data
    public static class TimingMarkOption {
        /** 正时标记 */
        @ApiModelProperty(value = "正时标记")
        private String timingMark;

        /** 产品ID（最终的叶子节点） */
        @ApiModelProperty(value = "产品ID")
        private Long productId;

        /** 是否有效（产品状态） */
        @ApiModelProperty(value = "是否有效")
        private Boolean enabled;
    }
}
