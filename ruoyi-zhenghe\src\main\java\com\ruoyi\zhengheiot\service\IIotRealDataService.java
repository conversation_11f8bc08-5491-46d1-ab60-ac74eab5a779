package com.ruoyi.zhengheiot.service;

import java.util.List;

import cn.hutool.json.JSONObject;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhengheiot.domain.DepartmentEquipmentTypeEquipmentVo;
import com.ruoyi.zhengheiot.domain.EquipmentDetail;
import com.ruoyi.zhengheiot.domain.IotRealData;

/**
 * IoT实时数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface IIotRealDataService 
{
    /**
     * 查询IoT实时数据
     * 
     * @param key IoT实时数据主键
     * @return IoT实时数据
     */
    public IotRealData selectIotRealDataByKey(String key);

    /**
     * 查询IoT实时数据列表
     * 
     * @param iotRealData IoT实时数据
     * @return IoT实时数据集合
     */
    public List<IotRealData> selectIotRealDataList(IotRealData iotRealData);

    /**
     * 新增IoT实时数据
     * 
     * @param iotRealData IoT实时数据
     * @return 结果
     */
    public int insertIotRealData(IotRealData iotRealData);

    /**
     * 修改IoT实时数据
     * 
     * @param iotRealData IoT实时数据
     * @return 结果
     */
    public int updateIotRealData(IotRealData iotRealData);

    /**
     * 批量删除IoT实时数据
     * 
     * @param keys 需要删除的IoT实时数据主键集合
     * @return 结果
     */
    public int deleteIotRealDataByKeys(String[] keys);

    /**
     * 删除IoT实时数据信息
     * 
     * @param key IoT实时数据主键
     * @return 结果
     */
    public int deleteIotRealDataByKey(String key);

    public List<DepartmentEquipmentTypeEquipmentVo> getEquipmentTypeEquipmentVoList(ZhIotEquipment equipmentName);

    /**
     * 获取设备实时数据by id
     * @param equipmentId
     * @return
     */
    public EquipmentDetail getRealDataList(Long equipmentId);


    public JSONObject realDetail(Long equipmentId);


}
