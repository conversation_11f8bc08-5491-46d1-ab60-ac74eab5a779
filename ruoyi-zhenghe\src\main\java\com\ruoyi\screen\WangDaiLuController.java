package com.ruoyi.screen;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.screen.entity.WDLErrorInfo;
import com.ruoyi.util.iotdb.IoTDBUtil;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.mapper.ZhDeviceTypeAttrMapper;
import com.ruoyi.zhenghe.service.IZhIotEquipmentService;
import com.ruoyi.zhengheiot.domain.EquipmentDetail;
import com.ruoyi.zhengheiot.service.IIotRealDataService;
import com.ruoyi.zhengheiot.service.IotHistoryDataService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Api(tags = "网带炉大屏")
@RestController
@RequestMapping("/screen/data/wangdailu")
public class WangDaiLuController {
    @Autowired
    private IZhIotEquipmentService zhIotEquipmentService;
    @Autowired
    private IIotRealDataService realDataService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private IIotRealDataService iotRealDataService;

    @Autowired
    private IoTDBUtil ioTDBUtil;

    @Resource
    private ZhDeviceTypeAttrMapper zhDeviceTypeAttrMapper;
    @Resource
    private IotHistoryDataService historyDataService;

    // 常量定义
    final int REDIS_CACHE_EXPIRATION_SECONDS = 300;// 缓存过期时间（秒）
    int a = 0;

    /**
     * 获取网带炉设备列表
     * 此方法查询网带炉设备类型（设备类型ID为80）的所有设备，并返回给前端
     * 使用GET请求映射到/get/wangdailu/device路径
     *
     * @return 包含网带炉设备列表的AjaxResult对象
     */
    @GetMapping("/get/wangdailu/device")
    public AjaxResult getWangdailu() {
        String cacheKey = "wangdailu:get_wangdailu_device";
//        if (redisCache.hasKey(cacheKey)) {
//            return AjaxResult.success(JSONObject.parseObject(redisCache.getCacheObject(cacheKey).toString()));
//        }
        ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
        zhIotEquipment.setDeviceTypeId(80L); //80是网带炉的设备类型
        // 获取设备列表
        List<ZhIotEquipment> equipmentList = zhIotEquipmentService.selectZhIotEquipmentListNoScope(zhIotEquipment);

//        redisCache.setCacheObject(cacheKey, JSONObject.toJSONString(equipmentList), REDIS_CACHE_EXPIRATION_SECONDS * 2, TimeUnit.SECONDS);

        return AjaxResult.success(equipmentList);
    }


    /**
     * 获取错误信息
     * 本接口用于获取特定设备类型的错误信息，并结合缓存中的数据进行综合处理
     *
     * @return 返回设备错误信息的列表，如果无错误信息则返回空列表
     */
    @GetMapping("/error/info")
    public AjaxResult getErrorInfo(Long id) {

        // 初始化错误信息列表
        List<WDLErrorInfo> ans = new ArrayList<>();

        try {

            // 获取设备列表
            List<ZhIotEquipment> equipmentList = new ArrayList<>();
            if (id != null) {
                final ZhIotEquipment equipment = zhIotEquipmentService.selectZhIotEquipmentById(id);
                if (equipment != null) {
                    equipmentList.add(equipment);
                }
            } else {
                // 创建设备对象并设置设备类型ID
                ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
                zhIotEquipment.setDeviceTypeId(80L);
                equipmentList = zhIotEquipmentService.selectZhIotEquipmentListNoScope(zhIotEquipment);
            }

            // 如果设备列表为空，直接返回成功响应
            if (equipmentList == null || equipmentList.isEmpty()) {
                return AjaxResult.success(new ArrayList<>());
            }

            // 遍历设备列表，获取每个设备的实时数据
            for (ZhIotEquipment equipment : equipmentList) {
                try {
                    EquipmentDetail realDataList = realDataService.getRealDataList(equipment.getId());

                    // 如果实时数据为空，跳过当前设备
                    if (realDataList == null || realDataList.getAttrList() == null) {
                        continue;
                    }

                    // 处理实时数据中的每个属性，寻找错误信息
                    realDataList.getAttrList().forEach(attr -> {
                        if (attr != null && "1".equals(attr.getAlarm()) && attr.getAttrName() != null) {
                            WDLErrorInfo wdlErrorInfo = new WDLErrorInfo();
                            wdlErrorInfo.setErrorInfo(attr.getAttrName());
                            wdlErrorInfo.setDeviceName(equipment.getEquipmentName() + equipment.getEquipmentCode());
                            wdlErrorInfo.setErrorTime(attr.getLastUpdateTime());
                            ans.add(wdlErrorInfo);
                        }
                    });
                } catch (Exception e) {
                    log.error("处理设备 {} 的实时数据时发生异常", equipment.getId(), e);
                }
            }
            // 返回错误信息列表的响应
            return AjaxResult.success(ans);
        } catch (Exception e) {
            log.error("获取错误信息时发生异常", e);
            return AjaxResult.error("获取错误信息失败");
        }
    }


    // 获取电力信息
    @GetMapping("/power/info")
    public AjaxResult powerInfo(Long id) {
        // 构建缓存键
        String cacheKey = "wangdailu:power_info:" + (id == null ? "all" : id.toString());

        // 检查缓存是否存在
        if (redisCache.hasKey(cacheKey)) {
            return AjaxResult.success(JSONObject.parseObject(redisCache.getCacheObject(cacheKey).toString()));
        }

        JSONObject ans = new JSONObject();

        // 获取设备列表
        List<ZhIotEquipment> equipmentList = new ArrayList<>();
        if (id != null) {
            final ZhIotEquipment equipment = zhIotEquipmentService.selectZhIotEquipmentById(id);
            if (equipment != null) {
                equipmentList.add(equipment);
            }
        } else {
            ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
            zhIotEquipment.setDeviceTypeId(80L);
            equipmentList = zhIotEquipmentService.selectZhIotEquipmentListNoScope(zhIotEquipment);
        }

        if (equipmentList == null || equipmentList.isEmpty()) {
            return AjaxResult.success(null);
        }
        // 返回的日期
        List<String> dateList = new ArrayList<>();
        // 返回的电量
        List<Integer> powerList = new ArrayList<>();

        final DateTime date = DateUtil.date();
        for (int i = 6; i >= 0; i--) {
            DateTime begin = DateUtil.beginOfDay(DateUtil.offsetDay(date, -i));
            DateTime end = DateUtil.endOfDay(begin);
            if (end.isAfter(date)) {
                end = DateUtil.date();
            }
            final String format = DateUtil.format(begin, "MM-dd");
            dateList.add(format);
            if (equipmentList == null || equipmentList.isEmpty()) {
                continue;
            }
            String startTime = DateUtil.format(begin, "yyyy-MM-dd HH:mm:ss");
            String endTime = DateUtil.format(end, "yyyy-MM-dd HH:mm:ss");

            int energyTotal = 0;
            for (ZhIotEquipment equipment : equipmentList) {
                final cn.hutool.json.JSONObject totalPower = historyDataService.getHistoryDataTagList(equipment.getId(), "Status_QuenchingFurnacePower", startTime, endTime, null, null);
                final cn.hutool.json.JSONObject totalPower2 = historyDataService.getHistoryDataTagList(equipment.getId(), "Status_TemperingFurnacePower", startTime, endTime, null, null);
                final int energy = ioTDBUtil.getTotalPower(totalPower);
                final int energy2 = ioTDBUtil.getTotalPower(totalPower2);
                energyTotal += (energy + energy2);
            }
            powerList.add(Math.abs(energyTotal));
        }
        ans.put("dateList", dateList);
        ans.put("powerList", powerList);
        redisCache.setCacheObject(cacheKey, ans.toJSONString(), REDIS_CACHE_EXPIRATION_SECONDS, TimeUnit.SECONDS);
        return AjaxResult.success(ans);
    }


    @GetMapping("device/monitor")
    public AjaxResult deviceMonitor() {
        JSONObject ans = new JSONObject();
        int runTime = 0;
        int standbyTime = 0;
        int errorTime = 0;
        String openRate = "";

        ans.put("runTime", runTime);
        ans.put("standbyTime", standbyTime);
        ans.put("errorTime", errorTime);
        ans.put("openRate", openRate);
        ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
        zhIotEquipment.setDeviceTypeId(80L);
        // 获取设备列表
        List<ZhIotEquipment> equipmentList = zhIotEquipmentService.selectZhIotEquipmentListNoScope(zhIotEquipment);
        final long between = DateUtil.between(DateUtil.beginOfDay(new Date()), new Date(), DateUnit.MINUTE);
        if (equipmentList == null || equipmentList.isEmpty()) {
            return AjaxResult.success(ans);
        }
        ans.put("runTime", between * equipmentList.size() / 60);
        ans.put("standbyTime", "-");
        ans.put("errorTime", "-");
        ans.put("openRate", "100%");

        return AjaxResult.success(ans);
    }


    @GetMapping("device/rate")
    public AjaxResult deviceRate() {
        JSONObject ans = new JSONObject();
        // 返回的日期
        List<String> dateList = new ArrayList<>();
        //返回的利用率
        List<String> rateList = new ArrayList<>();

        final DateTime date = DateUtil.date();
        for (int i = 6; i >= 0; i--) {
            DateTime begin = DateUtil.beginOfDay(DateUtil.offsetDay(date, -i));
            DateTime end = DateUtil.endOfDay(begin);
            if (end.isAfter(date)) {
                end = DateUtil.date();
            }
            final String format = DateUtil.format(begin, "MM-dd");
            dateList.add(format);
            rateList.add("100%");
        }
        ans.put("dateList", dateList);
        ans.put("rateList", rateList);
        return AjaxResult.success(ans);
    }


    @GetMapping("/monitor/real/data")
    public AjaxResult list(@RequestParam(required = true) Long id) {
        try {
            // 获取实时数据
            final EquipmentDetail realDataList = iotRealDataService.getRealDataList(id);
            return AjaxResult.success(realDataList);
        } catch (IllegalArgumentException e) {
            // 参数校验失败，返回具体的错误信息
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            // 记录异常日志
            log.error("查询IoT实时数据列表失败，id: {}", id, e);
            return AjaxResult.error("系统内部错误，请稍后重试");
        }
    }


}
