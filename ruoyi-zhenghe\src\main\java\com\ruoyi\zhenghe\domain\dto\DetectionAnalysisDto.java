package com.ruoyi.zhenghe.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 检测分析数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
public class DetectionAnalysisDto {

    /** 产品信息 */
    @ApiModelProperty(value = "产品型号")
    private String productModel;

    @ApiModelProperty(value = "片数")
    private Integer pieceCount;

    @ApiModelProperty(value = "节数")
    private Integer sectionCount;

    @ApiModelProperty(value = "正时标记")
    private String timingMark;

    /** 检测统计 */
    @ApiModelProperty(value = "总检测数量")
    private Integer totalCount;

    @ApiModelProperty(value = "合格数量")
    private Integer qualifiedCount;

    @ApiModelProperty(value = "瑕疵数量")
    private Integer defectCount;

    @ApiModelProperty(value = "合格率")
    private BigDecimal qualifiedRate;

    @ApiModelProperty(value = "瑕疵率")
    private BigDecimal defectRate;

    /** 瑕疵详情统计 */
    @ApiModelProperty(value = "瑕疵详情统计列表")
    private List<DefectStatistics> defectStatisticsList;

    /**
     * 瑕疵统计内部类
     */
    @Data
    public static class DefectStatistics {
        @ApiModelProperty(value = "瑕疵名称")
        private String defectName;

        @ApiModelProperty(value = "瑕疵数量")
        private Integer defectCount;

        @ApiModelProperty(value = "瑕疵占比")
        private BigDecimal defectRatio;

        @ApiModelProperty(value = "瑕疵图片")
        private String defectImage;
    }
}
