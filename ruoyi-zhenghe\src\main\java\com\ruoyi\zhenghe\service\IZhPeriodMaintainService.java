package com.ruoyi.zhenghe.service;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhPeriodMaintain;

/**
 * 尖峰平谷时段维护Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface IZhPeriodMaintainService 
{
    /**
     * 查询尖峰平谷时段维护
     * 
     * @param id 尖峰平谷时段维护主键
     * @return 尖峰平谷时段维护
     */
    public ZhPeriodMaintain selectZhPeriodMaintainById(Long id);

    /**
     * 查询尖峰平谷时段维护列表
     * 
     * @param zhPeriodMaintain 尖峰平谷时段维护
     * @return 尖峰平谷时段维护集合
     */
    public List<ZhPeriodMaintain> selectZhPeriodMaintainList(ZhPeriodMaintain zhPeriodMaintain);

    /**
     * 新增尖峰平谷时段维护
     * 
     * @param zhPeriodMaintainList 尖峰平谷时段维护
     * @return 结果
     */
    public int insertZhPeriodMaintain(List<ZhPeriodMaintain> zhPeriodMaintainList);

    /**
     * 修改尖峰平谷时段维护
     * 
     * @param zhPeriodMaintain 尖峰平谷时段维护
     * @return 结果
     */
    public int updateZhPeriodMaintain(ZhPeriodMaintain zhPeriodMaintain);

    /**
     * 批量删除尖峰平谷时段维护
     * 
     * @param ids 需要删除的尖峰平谷时段维护主键集合
     * @return 结果
     */
    public int deleteZhPeriodMaintainByIds(Long[] ids);

    /**
     * 删除尖峰平谷时段维护信息
     * 
     * @param id 尖峰平谷时段维护主键
     * @return 结果
     */
    public int deleteZhPeriodMaintainById(Long id);
}
