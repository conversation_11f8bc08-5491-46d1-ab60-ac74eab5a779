package com.ruoyi.zhengheiot.service;


import cn.hutool.json.JSONObject;
import com.ruoyi.zhenghe.domain.ZhDeviceTypeAttr;
import com.ruoyi.zhengheiot.domain.DeviceNesting;

import java.util.List;


public interface IotHistoryDataService {

    public JSONObject getHistoryData(Long id, String startTime, String endTime, Integer pageNum, Integer pageSize);

    public JSONObject getHistoryDataTagList(Long id, String tag, String startTime, String endTime, Integer pageNum, Integer pageSize);

    public List<DeviceNesting> getDeviceforHistoryData();

    /**
     * 获取设备属性by 设备id
     *
     * @param id
     * @return
     */
    public List<ZhDeviceTypeAttr> getAttrs(Long id);
}
