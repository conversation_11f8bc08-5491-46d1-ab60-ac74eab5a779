package com.ruoyi.util;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

/**
 * 拼音工具类
 * 基于pinyin4j实现汉字转拼音功能
 *
 * <AUTHOR>
 */
public class PinyinUtil {

    /**
     * 汉字转拼音
     * 将中文字符串转换为拼音，非中文字符保持原样
     *"你好世界" → "ni hao shi jie"
     * @param chinese 中文字符串
     * @return 拼音字符串，以空格分隔
     */
    public static String toPinyin(String chinese) {
        if (chinese == null || chinese.trim().isEmpty()) {
            return "";
        }

        StringBuilder pinyin = new StringBuilder();
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        // 设置拼音格式：小写、不带声调、用v表示ü
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        format.setVCharType(HanyuPinyinVCharType.WITH_V);

        try {
            for (int i = 0; i < chinese.length(); i++) {
                char c = chinese.charAt(i);
                // 判断是否为中文字符
                if (c >= 0x4e00 && c <= 0x9fa5) {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        pinyin.append(pinyinArray[0]);
                    } else {
                        pinyin.append(c);
                    }
                } else {
                    // 非中文字符直接添加
                    pinyin.append(c);
                }

                // 添加空格分隔（最后一个字符不加空格）
                if (i < chinese.length() - 1) {
                    pinyin.append(" ");
                }
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            // 格式异常时返回原字符串
            return chinese;
        }

        return pinyin.toString();
    }

    /**
     * 汉字转拼音（不带空格分隔）
     * 将中文字符串转换为拼音，非中文字符保持原样
     *"你好世界" → "NIHAOSHIJIE"
     * @param chinese 中文字符串
     * @return 拼音字符串，不带分隔符
     */
    public static String toPinyinWithoutSpace(String chinese) {
        if (chinese == null || chinese.trim().isEmpty()) {
            return "";
        }

        StringBuilder pinyin = new StringBuilder();
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.UPPERCASE); // 设置拼音格式：大写
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        format.setVCharType(HanyuPinyinVCharType.WITH_V);

        try {
            for (int i = 0; i < chinese.length(); i++) {
                char c = chinese.charAt(i);
                if (c >= 0x4e00 && c <= 0x9fa5) {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        pinyin.append(pinyinArray[0]);
                    } else {
                        pinyin.append(c);
                    }
                } else {
                    pinyin.append(c);
                }
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            return chinese;
        }

        return pinyin.toString();
    }

    /**
     * 汉字转首字母
     * 将中文字符串转换为拼音首字母，非中文字符保持原样
     *"你好世界" → "NHSJ"
     * @param chinese 中文字符串
     * @return 拼音首字母字符串
     */
    public static String toFirstLetter(String chinese) {
        if (chinese == null || chinese.trim().isEmpty()) {
            return "";
        }

        StringBuilder firstLetter = new StringBuilder();
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.UPPERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        format.setVCharType(HanyuPinyinVCharType.WITH_V);

        try {
            for (int i = 0; i < chinese.length(); i++) {
                char c = chinese.charAt(i);
                // 判断是否为中文字符
                if (c >= 0x4e00 && c <= 0x9fa5) {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        // 取拼音的第一个字母
                        firstLetter.append(pinyinArray[0].charAt(0));
                    } else {
                        firstLetter.append(c);
                    }
                } else {
                    // 非中文字符直接添加
                    firstLetter.append(c);
                }
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            // 格式异常时返回原字符串
            return chinese;
        }

        return firstLetter.toString();
    }

    /**
     * 汉字转首字母（小写）
     * 将中文字符串转换为拼音首字母小写，非中文字符保持原样
     *"你好世界" → "nhsj"
     * @param chinese 中文字符串
     * @return 拼音首字母字符串（小写）
     */
    public static String toFirstLetterLowerCase(String chinese) {
        return toFirstLetter(chinese).toLowerCase();
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        // 测试拼音转换功能
        String testName = "北京汽车制造有限公司";

        System.out.println("原始客户名称: " + testName);
        System.out.println("拼音全拼(带空格): " + toPinyin(testName));
        System.out.println("拼音全拼(不带空格): " + toPinyinWithoutSpace(testName));
        System.out.println("拼音首字母(大写): " + toFirstLetter(testName));
        System.out.println("拼音首字母(小写): " + toFirstLetterLowerCase(testName));

        // 测试包含英文和数字的情况
        String mixedName = "ABC汽车123公司";
        System.out.println("\n混合字符测试: " + mixedName);
        System.out.println("拼音全拼(不带空格): " + toPinyinWithoutSpace(mixedName));
        System.out.println("拼音首字母(大写): " + toFirstLetter(mixedName));

        // 测试客户名称搜索场景
        System.out.println("\n=== 客户名称搜索测试 ===");
        String[] testCustomers = {"北京汽车", "上海大众", "广州本田", "深圳比亚迪"};
        for (String customer : testCustomers) {
            String pinyin = toPinyinWithoutSpace(customer);
            System.out.println(String.format("客户: %s -> 拼音: %s", customer, pinyin));
            System.out.println(String.format("  搜索'bei'能匹配: %s", pinyin.toLowerCase().contains("bei")));
            System.out.println(String.format("  搜索'北京'能匹配: %s", customer.contains("北京")));
        }
    }
}
