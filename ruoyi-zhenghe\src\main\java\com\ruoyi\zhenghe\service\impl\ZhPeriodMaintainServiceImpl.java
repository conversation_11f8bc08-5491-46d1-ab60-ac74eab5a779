package com.ruoyi.zhenghe.service.impl;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.zhenghe.mapper.ZhPeriodMaintainMapper;
import com.ruoyi.zhenghe.domain.ZhPeriodMaintain;
import com.ruoyi.zhenghe.service.IZhPeriodMaintainService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 尖峰平谷时段维护Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
@Service
public class ZhPeriodMaintainServiceImpl implements IZhPeriodMaintainService 
{
    @Autowired
    private ZhPeriodMaintainMapper zhPeriodMaintainMapper;

    /**
     * 查询尖峰平谷时段维护
     * 
     * @param id 尖峰平谷时段维护主键
     * @return 尖峰平谷时段维护
     */
    @Override
    public ZhPeriodMaintain selectZhPeriodMaintainById(Long id)
    {
        return zhPeriodMaintainMapper.selectZhPeriodMaintainById(id);
    }

    /**
     * 查询尖峰平谷时段维护列表
     * 
     * @param zhPeriodMaintain 尖峰平谷时段维护
     * @return 尖峰平谷时段维护
     */
    @Override
    public List<ZhPeriodMaintain> selectZhPeriodMaintainList(ZhPeriodMaintain zhPeriodMaintain)
    {
        return zhPeriodMaintainMapper.selectZhPeriodMaintainList(zhPeriodMaintain);
    }

    /**
     * 新增尖峰平谷时段维护
     * 
     * @param zhPeriodMaintainList 尖峰平谷时段维护
     * @return 结果
     */
    @Override
    @Transactional()
    public int insertZhPeriodMaintain(List<ZhPeriodMaintain> zhPeriodMaintainList)
    {
        //先删除该年的所有数据
        int i = zhPeriodMaintainMapper.deleteZhPeriodMaintainByYearTime(zhPeriodMaintainList.get(0).getYearTime());
        //然后重新添加
        for (ZhPeriodMaintain zhPeriodMaintain : zhPeriodMaintainList) {
            zhPeriodMaintain.setCreateBy(SecurityUtils.getUsername());
            zhPeriodMaintain.setCreateTime(DateUtils.getNowDate());
            zhPeriodMaintainMapper.insertZhPeriodMaintain(zhPeriodMaintain);
        }
        return 1;
    }

    /**
     * 修改尖峰平谷时段维护
     * 
     * @param zhPeriodMaintain 尖峰平谷时段维护
     * @return 结果
     */
    @Override
    public int updateZhPeriodMaintain(ZhPeriodMaintain zhPeriodMaintain)
    {
        zhPeriodMaintain.setUpdateTime(DateUtils.getNowDate());
        return zhPeriodMaintainMapper.updateZhPeriodMaintain(zhPeriodMaintain);
    }

    /**
     * 批量删除尖峰平谷时段维护
     * 
     * @param ids 需要删除的尖峰平谷时段维护主键
     * @return 结果
     */
    @Override
    public int deleteZhPeriodMaintainByIds(Long[] ids)
    {
        return zhPeriodMaintainMapper.deleteZhPeriodMaintainByIds(ids);
    }

    /**
     * 删除尖峰平谷时段维护信息
     * 
     * @param id 尖峰平谷时段维护主键
     * @return 结果
     */
    @Override
    public int deleteZhPeriodMaintainById(Long id)
    {
        return zhPeriodMaintainMapper.deleteZhPeriodMaintainById(id);
    }
}
