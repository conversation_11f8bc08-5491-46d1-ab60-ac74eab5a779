<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhDeviceTypeAttrMapper">
    
    <resultMap type="ZhDeviceTypeAttr" id="ZhDeviceTypeAttrResult">
        <result property="id"    column="id"    />
        <result property="tslId"    column="tsl_id"    />
        <result property="attrName"    column="attr_name"    />
        <result property="attrCode"    column="attr_code"    />
        <result property="attrDesc"    column="attr_desc"    />
        <result property="attrType"    column="attr_type"    />
        <result property="attrUnit"    column="attr_unit"    />
        <result property="attrClass"    column="attr_class"    />
        <result property="attrIcon"    column="attr_icon"    />
        <result property="attrOrder"    column="attr_order"    />
        <result property="attrMultiple"    column="attr_multiple"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="ex1"    column="ex1"    />
        <result property="ex2"    column="ex2"    />
        <result property="gatewayAttr"    column="gateway_attr"    />
    </resultMap>

    <sql id="selectZhDeviceTypeAttrVo">
        select id, tsl_id, attr_name, attr_code, attr_desc, attr_type, attr_unit, attr_class, attr_icon, attr_order, attr_multiple, create_by, create_time, update_by, update_time, ex1, ex2, gateway_attr from zh_device_type_attr
    </sql>

    <select id="selectZhDeviceTypeAttrList" parameterType="ZhDeviceTypeAttr" resultMap="ZhDeviceTypeAttrResult">
        <include refid="selectZhDeviceTypeAttrVo"/>
        <where>
            <if test="tslId != null ">and tsl_id = #{tslId}</if>
            <if test="attrName != null  and attrName != ''">and attr_name like concat('%', #{attrName}, '%')</if>
            <if test="attrCode != null  and attrCode != ''">and attr_code = #{attrCode}</if>
            <if test="attrType != null  and attrType != ''">and attr_type = #{attrType}</if>
            <if test="attrUnit != null  and attrUnit != ''">and attr_unit = #{attrUnit}</if>
            <if test="attrClass != null  and attrClass != ''">and attr_class = #{attrClass}</if>
            <if test="attrIcon != null  and attrIcon != ''">and attr_icon = #{attrIcon}</if>
            <if test="attrOrder != null ">and attr_order = #{attrOrder}</if>
            <if test="attrMultiple != null ">and attr_multiple = #{attrMultiple}</if>
            <if test="ex1 != null  and ex1 != ''">and ex1 = #{ex1}</if>
            <if test="ex2 != null  and ex2 != ''">and ex2 = #{ex2}</if>
            <if test="gatewayAttr != null  and gatewayAttr != ''">and gateway_attr like concat('%', #{gatewayAttr}, '%')</if>
        </where>
        ORDER BY
        attr_order ASC,
        id DESC
    </select>
    
    <select id="selectZhDeviceTypeAttrById" parameterType="Long" resultMap="ZhDeviceTypeAttrResult">
        <include refid="selectZhDeviceTypeAttrVo"/>
        where id = #{id}
    </select>


    <insert id="insertZhDeviceTypeAttr" parameterType="ZhDeviceTypeAttr" useGeneratedKeys="true" keyProperty="id">
        insert into zh_device_type_attr
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tslId != null">tsl_id,</if>
            <if test="attrName != null">attr_name,</if>
            <if test="attrCode != null">attr_code,</if>
            <if test="attrDesc != null">attr_desc,</if>
            <if test="attrType != null">attr_type,</if>
            <if test="attrUnit != null">attr_unit,</if>
            <if test="attrClass != null">attr_class,</if>
            <if test="attrIcon != null">attr_icon,</if>
            <if test="attrOrder != null">attr_order,</if>
            <if test="attrMultiple != null">attr_multiple,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="ex1 != null">ex1,</if>
            <if test="ex2 != null">ex2,</if>
            <if test="gatewayAttr != null">gateway_attr,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tslId != null">#{tslId},</if>
            <if test="attrName != null">#{attrName},</if>
            <if test="attrCode != null">#{attrCode},</if>
            <if test="attrDesc != null">#{attrDesc},</if>
            <if test="attrType != null">#{attrType},</if>
            <if test="attrUnit != null">#{attrUnit},</if>
            <if test="attrClass != null">#{attrClass},</if>
            <if test="attrIcon != null">#{attrIcon},</if>
            <if test="attrOrder != null">#{attrOrder},</if>
            <if test="attrMultiple != null">#{attrMultiple},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="ex1 != null">#{ex1},</if>
            <if test="ex2 != null">#{ex2},</if>
            <if test="gatewayAttr != null">#{gatewayAttr},</if>
         </trim>
    </insert>

    <update id="updateZhDeviceTypeAttr" parameterType="ZhDeviceTypeAttr">
        update zh_device_type_attr
        <trim prefix="SET" suffixOverrides=",">
            <if test="tslId != null">tsl_id = #{tslId},</if>
            <if test="attrName != null">attr_name = #{attrName},</if>
            <if test="attrCode != null">attr_code = #{attrCode},</if>
            <if test="attrDesc != null">attr_desc = #{attrDesc},</if>
            <if test="attrType != null">attr_type = #{attrType},</if>
            <if test="attrUnit != null">attr_unit = #{attrUnit},</if>
            <if test="attrClass != null">attr_class = #{attrClass},</if>
            <if test="attrIcon != null">attr_icon = #{attrIcon},</if>
            <if test="attrOrder != null">attr_order = #{attrOrder},</if>
            <if test="attrMultiple != null">attr_multiple = #{attrMultiple},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="ex1 != null">ex1 = #{ex1},</if>
            <if test="ex2 != null">ex2 = #{ex2},</if>
            <if test="gatewayAttr != null">gateway_attr = #{gatewayAttr},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhDeviceTypeAttrById" parameterType="Long">
        delete from zh_device_type_attr where id = #{id}
    </delete>

    <delete id="deleteZhDeviceTypeAttrByIds" parameterType="String">
        delete from zh_device_type_attr where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据设备编号查询设备类型属性列表 -->
    <select id="selectZhDeviceTypeAttrListByequipmentCode" parameterType="String" resultMap="ZhDeviceTypeAttrResult">
        <include refid="selectZhDeviceTypeAttrVo"/>
        WHERE tsl_id = (
            SELECT device_type_id
            FROM zh_iot_equipment
            WHERE equipment_code = #{equipmentCode}
        )
        ORDER BY attr_order ASC, id DESC
    </select>

</mapper>