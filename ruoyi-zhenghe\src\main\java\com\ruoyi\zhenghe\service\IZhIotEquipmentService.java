package com.ruoyi.zhenghe.service;

import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.domain.ZhIotEquipment2QueryVo;

import java.util.List;

/**
 * 物联网设备明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface IZhIotEquipmentService 
{
    /**
     * 查询物联网设备明细
     * 
     * @param id 物联网设备明细主键
     * @return 物联网设备明细
     */
    public ZhIotEquipment selectZhIotEquipmentById(Long id);

    /**
     * 查询物联网设备明细列表
     * 
     * @param zhIotEquipment 物联网设备明细
     * @return 物联网设备明细集合
     */
    public List<ZhIotEquipment> selectZhIotEquipmentList(ZhIotEquipment zhIotEquipment);

    public List<ZhIotEquipment> selectZhIotEquipmentList2(ZhIotEquipment2QueryVo zhIotEquipment2QueryVo);

    public List<ZhIotEquipment> selectZhIotEquipmentListNoScope(ZhIotEquipment zhIotEquipment);

    /**
     * 新增物联网设备明细
     * 
     * @param zhIotEquipment 物联网设备明细
     * @return 结果
     */
    public int insertZhIotEquipment(ZhIotEquipment zhIotEquipment);

    /**
     * 修改物联网设备明细
     * 
     * @param zhIotEquipment 物联网设备明细
     * @return 结果
     */
    public int updateZhIotEquipment(ZhIotEquipment zhIotEquipment);

    /**
     * 批量删除物联网设备明细
     * 
     * @param ids 需要删除的物联网设备明细主键集合
     * @return 结果
     */
    public int deleteZhIotEquipmentByIds(Long[] ids);

    /**
     * 删除物联网设备明细信息
     * 
     * @param id 物联网设备明细主键
     * @return 结果
     */
    public int deleteZhIotEquipmentById(Long id);
}
