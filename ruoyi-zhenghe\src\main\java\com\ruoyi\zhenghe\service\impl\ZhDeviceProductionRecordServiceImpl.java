package com.ruoyi.zhenghe.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.config.ProductionDataConfig;
import com.ruoyi.util.iotdb.IoTDBUtil;
import com.ruoyi.zhenghe.domain.ZhDeviceProductionRecord;
import com.ruoyi.zhenghe.domain.dto.ProductQuantityDto;
import com.ruoyi.zhenghe.mapper.ZhDeviceProductionRecordMapper;
import com.ruoyi.zhenghe.service.IZhDeviceProductionRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备产品生产记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
@Slf4j
public class ZhDeviceProductionRecordServiceImpl implements IZhDeviceProductionRecordService 
{
    @Autowired
    private ZhDeviceProductionRecordMapper zhDeviceProductionRecordMapper;

    @Autowired
    private IoTDBUtil ioTDBUtil;

    @Autowired
    private ProductionDataConfig productionDataConfig;

    /**
     * 查询设备产品生产记录
     * 
     * @param id 设备产品生产记录主键
     * @return 设备产品生产记录
     */
    @Override
    public ZhDeviceProductionRecord selectZhDeviceProductionRecordById(Long id)
    {
        return zhDeviceProductionRecordMapper.selectZhDeviceProductionRecordById(id);
    }

    /**
     * 查询设备产品生产记录列表
     * 
     * @param zhDeviceProductionRecord 设备产品生产记录
     * @return 设备产品生产记录
     */
    @Override
    public List<ZhDeviceProductionRecord> selectZhDeviceProductionRecordList(ZhDeviceProductionRecord zhDeviceProductionRecord)
    {
        return zhDeviceProductionRecordMapper.selectZhDeviceProductionRecordList(zhDeviceProductionRecord);
    }

    /**
     * 新增设备产品生产记录
     * 
     * @param zhDeviceProductionRecord 设备产品生产记录
     * @return 结果
     */
    @Override
    public int insertZhDeviceProductionRecord(ZhDeviceProductionRecord zhDeviceProductionRecord)
    {
        zhDeviceProductionRecord.setCreateTime(DateUtils.getNowDate());
        return zhDeviceProductionRecordMapper.insertZhDeviceProductionRecord(zhDeviceProductionRecord);
    }

    /**
     * 修改设备产品生产记录
     * 
     * @param zhDeviceProductionRecord 设备产品生产记录
     * @return 结果
     */
    @Override
    public int updateZhDeviceProductionRecord(ZhDeviceProductionRecord zhDeviceProductionRecord)
    {
        zhDeviceProductionRecord.setUpdateTime(DateUtils.getNowDate());
        return zhDeviceProductionRecordMapper.updateZhDeviceProductionRecord(zhDeviceProductionRecord);
    }

    /**
     * 批量删除设备产品生产记录
     * 
     * @param ids 需要删除的设备产品生产记录主键
     * @return 结果
     */
    @Override
    public int deleteZhDeviceProductionRecordByIds(Long[] ids)
    {
        return zhDeviceProductionRecordMapper.deleteZhDeviceProductionRecordByIds(ids);
    }

    /**
     * 删除设备产品生产记录信息
     * 
     * @param id 设备产品生产记录主键
     * @return 结果
     */
    @Override
    public int deleteZhDeviceProductionRecordById(Long id)
    {
        return zhDeviceProductionRecordMapper.deleteZhDeviceProductionRecordById(id);
    }

    /**
     * 处理生产记录数据
     *
     * @param deviceCode 设备编码
     * @param productName 产品名称
     * @param currentCount 当前累计产量
     * @param timestamp 时间戳
     */
    @Override
    public void processProductionRecord(String deviceCode, String productName, int currentCount, Long timestamp) {
        try {
            Date recordTime = new Date(timestamp);

            // 查询当前正在生产的记录
            ZhDeviceProductionRecord currentRecord = zhDeviceProductionRecordMapper.selectCurrentProductionRecord(deviceCode, productName);

            if (currentRecord != null) {
                // 如果有正在进行的生产记录，实时更新累计产量和本次生产产量
                // 计算本次生产数量：当前累计产量 - 记录开始时的累计产量
                int startCumulativeCount = currentRecord.getCumulativeCount() - currentRecord.getProductionCount();
                int newProductionCount = currentCount - startCumulativeCount;

                // 确保本次生产产量不为负数
                if (newProductionCount < 0) {
                    log.warn("设备 {} 产品 {} 计数器可能重置，当前累计产量: {}，开始累计产量: {}，保持原有本次产量: {}",
                            deviceCode, productName, currentCount, startCumulativeCount, currentRecord.getProductionCount());
                    newProductionCount = currentRecord.getProductionCount(); // 保持原有产量
                }

                currentRecord.setCumulativeCount(currentCount);
                currentRecord.setProductionCount(newProductionCount);
                currentRecord.setRecordTime(recordTime);
                currentRecord.setUpdateTime(DateUtils.getNowDate());
                zhDeviceProductionRecordMapper.updateZhDeviceProductionRecord(currentRecord);

//                log.info("实时更新设备 {} 产品 {} 的生产记录，累计产量: {} -> {}，本次产量: {} -> {}",
//                        deviceCode, productName, previousCumulativeCount, currentCount,
//                        currentRecord.getProductionCount(), newProductionCount);
            } else {
                // 没有正在进行的生产记录，需要创建新记录
                // 首先检查该设备是否有其他产品的进行中记录，如果有则先结束它们
                List<ZhDeviceProductionRecord> activeRecords = zhDeviceProductionRecordMapper.selectActiveProductionRecords(deviceCode);
                if (activeRecords != null && !activeRecords.isEmpty()) {
                    for (ZhDeviceProductionRecord activeRecord : activeRecords) {
                        // 结束其他产品的生产记录
                        Date endTime = recordTime;
                        int duration = calculateDuration(activeRecord.getStartTime(), endTime);
                        zhDeviceProductionRecordMapper.finishProductionRecord(activeRecord.getId(), endTime, duration);

                        log.info("设备 {} 切换产品，结束产品 {} 的生产记录，ID: {}，生产时长: {} 分钟",
                                deviceCode, activeRecord.getProductName(), activeRecord.getId(), duration);
                    }
                }

                // 新生产记录的本次产量从0开始
                int productionCount = 0;
                int startCumulativeCount = currentCount; // 记录开始时的累计产量就是当前累计产量

                // 创建新的生产记录
                ZhDeviceProductionRecord newRecord = new ZhDeviceProductionRecord();
                newRecord.setDeviceCode(deviceCode);
                newRecord.setProductName(productName);
                newRecord.setCumulativeCount(currentCount);
                newRecord.setProductionCount(productionCount);
                newRecord.setStartTime(recordTime);
                newRecord.setStatus(1); // 进行中
                newRecord.setRecordTime(recordTime);
                newRecord.setCreateBy("system");
                newRecord.setCreateTime(DateUtils.getNowDate());

                zhDeviceProductionRecordMapper.insertZhDeviceProductionRecord(newRecord);

                log.info("创建设备 {} 产品 {} 的新生产记录，开始累计产量: {}，当前累计产量: {}，本次产量: {}",
                        deviceCode, productName, startCumulativeCount, currentCount, productionCount);
            }

        } catch (Exception e) {
            log.error("处理设备 {} 产品 {} 生产记录异常", deviceCode, productName, e);
        }
    }

    /**
     * 计算生产时长（分钟）
     */
    private int calculateDuration(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return 0;
        }
        long diffInMillis = endTime.getTime() - startTime.getTime();
        return (int) (diffInMillis / (1000 * 60)); // 转换为分钟
    }

    /**
     * 查询设备生产汇总数据
     *
     * @param deviceCode 设备编码
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 生产汇总列表
     */
    @Override
    public List<ZhDeviceProductionRecord> selectProductionSummary(String deviceCode, String beginTime, String endTime) {
        return zhDeviceProductionRecordMapper.selectProductionSummary(deviceCode, beginTime, endTime);
    }

    /**
     * 查询产品数量展示界面数据（使用IoTDB精确数据）
     *
     * @param productName 产品型号（可选）
     * @param beginTime 开始时间（可选，默认当天0点）
     * @param endTime 结束时间（可选，默认当前时间）
     * @return 产品数量列表
     */
    /**
     * 测试方法：简化版本的产品数量查询
     */
    public List<ZhDeviceProductionRecord> selectProductQuantityListTest(String productName, String beginTime, String endTime) {
        log.info("=== 测试版本：开始查询产品数量 ===");

        // 1. 从MySQL获取基础数据
        List<ZhDeviceProductionRecord> mysqlRecords = zhDeviceProductionRecordMapper.selectProductQuantityList(
                productName, beginTime, endTime);

        log.info("MySQL查询结果: {} 条记录", mysqlRecords.size());

        // 2. 为每条记录设置测试值
        for (ZhDeviceProductionRecord record : mysqlRecords) {
            record.setProductionCount(777);
            Integer retrieved = record.getProductionCount();
            log.info("产品: {}, 设置值: 777, 读取值: {}", record.getProductName(), retrieved);
        }

        return mysqlRecords;
    }

    @Override
    public List<ZhDeviceProductionRecord> selectProductQuantityList(String productName, String beginTime, String endTime) {
        // 处理默认时间
        String finalBeginTime = beginTime;
        String finalEndTime = endTime;

        // 如果时间参数为空，设置默认值
        if (beginTime == null || beginTime.trim().isEmpty()) {
            // 默认开始时间为当天0点
            finalBeginTime = LocalDate.now().atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        if (endTime == null || endTime.trim().isEmpty()) {
            // 默认结束时间为当前时间
            finalEndTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        log.info("查询产品数量（IoTDB精确数据） - 产品名称: {}, 开始时间: {}, 结束时间: {}", productName, finalBeginTime, finalEndTime);

        List<ZhDeviceProductionRecord> result = new ArrayList<>();

        try {
            // 混合查询策略：使用MySQL获取产品和设备信息，使用IoTDB获取精确的生产数量

            // 调试：检查IoTDB中是否有生产数据
            String enterpriseCode = productionDataConfig.getDefaultEnterpriseCode();
            List<String> productionTimeSeries = ioTDBUtil.debugQueryAllProductionTimeSeries(enterpriseCode);
            log.error("IoTDB中共有 {} 个生产时间序列", productionTimeSeries.size());

            // 1. 先从MySQL查询获取产品和设备的基础信息
            List<ZhDeviceProductionRecord> mysqlRecords = zhDeviceProductionRecordMapper.selectProductQuantityList(
                    productName, finalBeginTime, finalEndTime);

            log.error("从MySQL查询到 {} 条产品记录", mysqlRecords.size());

            // 2. 从IoTDB查询精确的生产数量
            for (ZhDeviceProductionRecord mysqlRecord : mysqlRecords) {
                try {
                    // 解析设备编码列表
                    String[] deviceCodes = mysqlRecord.getDeviceCode().split(",");
                    int totalPreciseCount = 0;

                    log.info("处理产品 {} 的设备: {}", mysqlRecord.getProductName(), Arrays.toString(deviceCodes));

                    // 为每个设备查询精确的生产数量并汇总
                    for (String deviceCode : deviceCodes) {
                        deviceCode = deviceCode.trim();
                        log.error("查询设备 {} 产品 {} 的精确产量", deviceCode, mysqlRecord.getProductName());

                        // 优先使用基于快照数据的查询方法，这是最准确的历史数据查询方式
                        int preciseCount = 0;
                        try {
                            preciseCount = ioTDBUtil.queryProductionCountFromSnapshot(enterpriseCode,
                                    deviceCode, mysqlRecord.getProductName(), finalBeginTime, finalEndTime);
                            log.error("设备 {} 产品 {} 快照查询产量: {}", deviceCode, mysqlRecord.getProductName(), preciseCount);
                        } catch (Exception e) {
                            log.warn("快照查询失败，回退到原始数据查询: 设备={}, 产品={}", deviceCode, mysqlRecord.getProductName(), e);
                            // 如果快照查询失败，回退到原始数据查询
                            try {
                                preciseCount = ioTDBUtil.queryProductionCountFromRawData(enterpriseCode,
                                        deviceCode, mysqlRecord.getProductName(), finalBeginTime, finalEndTime);
                                log.info("设备 {} 产品 {} 原始数据查询产量: {}", deviceCode, mysqlRecord.getProductName(), preciseCount);
                            } catch (Exception e2) {
                                log.warn("原始数据查询也失败，使用MySQL数据: 设备={}, 产品={}", deviceCode, mysqlRecord.getProductName(), e2);
                                // 如果IoTDB查询都失败，使用MySQL的估算值
                                preciseCount = 0;
                            }
                        }

                        totalPreciseCount += preciseCount;
                    }

                    log.info("产品 {} 总精确产量: {} (MySQL估算: {})",
                            mysqlRecord.getProductName(), totalPreciseCount, mysqlRecord.getProductionCount());

                    // 始终添加记录，确保与productDetail接口的数据一致性
                    if (totalPreciseCount > 0) {
                        // 使用IoTDB的精确数量替换MySQL的估算数量
                        mysqlRecord.setProductionCount(totalPreciseCount);
                        log.info("产品 {} 使用IoTDB精确数量: {}", mysqlRecord.getProductName(), totalPreciseCount);
                    } else {
                        // 如果IoTDB没有数据，使用MySQL的数据
                        log.warn("产品 {} 在IoTDB中无数据，使用MySQL估算数量: {}",
                                mysqlRecord.getProductName(), mysqlRecord.getProductionCount());
                    }
                    result.add(mysqlRecord);

                } catch (Exception ex) {
                    log.warn("从IoTDB查询产品 {} 精确产量失败，使用MySQL数据", mysqlRecord.getProductName(), ex);
                    // 如果IoTDB查询失败，使用MySQL的数据
                    result.add(mysqlRecord);
                }
            }

            // 验证数据一致性：记录汇总数据用于与明细接口对比
            int totalQuantitySum = result.stream().mapToInt(ZhDeviceProductionRecord::getProductionCount).sum();
            log.info("混合查询完成：从MySQL获取 {} 条产品记录，从IoTDB获取精确数量，最终返回 {} 条记录，总产量汇总: {}",
                    mysqlRecords.size(), result.size(), totalQuantitySum);

        } catch (Exception e) {
            log.error("混合查询失败，完全回退到MySQL查询", e);
            // 如果混合查询失败，完全回退到原有的MySQL查询
            result = zhDeviceProductionRecordMapper.selectProductQuantityList(productName, finalBeginTime, finalEndTime);
        }

        return result;
    }

    /**
     * 查询产品数量展示界面数据（用于导出）
     *
     * @param productName 产品型号（可选）
     * @param beginTime 开始时间（可选，默认当天0点）
     * @param endTime 结束时间（可选，默认当前时间）
     * @return 产品数量DTO列表
     */
    @Override
    public List<ProductQuantityDto> selectProductQuantityForExport(String productName, String beginTime, String endTime) {
        // 处理默认时间
        String finalBeginTime = beginTime;
        String finalEndTime = endTime;

        if (beginTime == null || beginTime.trim().isEmpty()) {
            // 默认开始时间为当天0点
            finalBeginTime = LocalDate.now().atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        if (endTime == null || endTime.trim().isEmpty()) {
            // 默认结束时间为当前时间
            finalEndTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        log.info("导出产品数量（IoTDB精确数据） - 产品名称: {}, 开始时间: {}, 结束时间: {}", productName, finalBeginTime, finalEndTime);

        List<ProductQuantityDto> result = new ArrayList<>();

        try {
            // 混合查询策略：使用MySQL获取产品和设备信息，使用IoTDB获取精确的生产数量

            // 1. 先从MySQL查询获取产品和设备的基础信息
            List<ProductQuantityDto> mysqlRecords = zhDeviceProductionRecordMapper.selectProductQuantityForExport(
                    productName, finalBeginTime, finalEndTime);

            // 2. 从IoTDB查询精确的生产数量
            String enterpriseCode = productionDataConfig.getDefaultEnterpriseCode();

            for (ProductQuantityDto mysqlRecord : mysqlRecords) {
                try {
                    // 查询该产品所有设备的精确生产数量
                    List<IoTDBUtil.ProductionSummaryData> summaryDataList = ioTDBUtil.queryAllProductionSummary(
                            enterpriseCode, mysqlRecord.getProductName(), finalBeginTime, finalEndTime);

                    // 找到匹配的产品数据
                    for (IoTDBUtil.ProductionSummaryData summaryData : summaryDataList) {
                        if (mysqlRecord.getProductName().equals(summaryData.getProductName())) {
                            // 使用IoTDB的精确数量替换MySQL的估算数量
                            mysqlRecord.setProductionCount(summaryData.getProductionCount());
                            break;
                        }
                    }

                    result.add(mysqlRecord);

                } catch (Exception ex) {
                    log.warn("从IoTDB查询产品 {} 精确产量失败，使用MySQL数据", mysqlRecord.getProductName(), ex);
                    // 如果IoTDB查询失败，使用MySQL的数据
                    result.add(mysqlRecord);
                }
            }

            log.info("混合查询完成：从MySQL获取 {} 条产品导出记录，从IoTDB获取精确数量，最终返回 {} 条记录",
                    mysqlRecords.size(), result.size());

        } catch (Exception e) {
            log.error("混合查询失败，完全回退到MySQL查询", e);
            // 如果混合查询失败，完全回退到原有的MySQL查询
            result = zhDeviceProductionRecordMapper.selectProductQuantityForExport(productName, finalBeginTime, finalEndTime);
        }

        return result;
    }

    /**
     * 查询产品生产明细（使用IoTDB精确数据）
     *
     * @param productName 产品名称
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 产品生产明细列表
     */
    @Override
    public List<ZhDeviceProductionRecord> selectProductDetail(String productName, String beginTime, String endTime) {
        // 处理默认时间
        String finalBeginTime;
        String finalEndTime;

        if (beginTime == null || beginTime.trim().isEmpty()) {
            // 默认开始时间为当天0点
            finalBeginTime = LocalDate.now().atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } else {
            finalBeginTime = beginTime;
        }

        if (endTime == null || endTime.trim().isEmpty()) {
            // 默认结束时间为当前时间
            finalEndTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } else {
            finalEndTime = endTime;
        }

        log.info("查询产品明细（IoTDB精确数据） - 产品名称: {}, 开始时间: {}, 结束时间: {}", productName, finalBeginTime, finalEndTime);

        List<ZhDeviceProductionRecord> result = new ArrayList<>();

        try {
            // 混合查询策略：使用MySQL获取完整的记录结构，使用IoTDB获取精确的生产数量

            // 1. 先从MySQL查询获取完整的记录结构（包含设备名称、事业部、车间等信息）
            List<ZhDeviceProductionRecord> mysqlRecords = zhDeviceProductionRecordMapper.selectProductDetail(
                    productName, finalBeginTime, finalEndTime);

            // 2. 从IoTDB查询精确的生产数量
            String enterpriseCode = productionDataConfig.getDefaultEnterpriseCode();
            Map<String, Integer> deviceProductionMap = new HashMap<>();

            // 为每个设备查询精确的生产数量
            for (ZhDeviceProductionRecord mysqlRecord : mysqlRecords) {
                try {
                    // 优先使用基于快照数据的查询方法
                    int preciseCount = 0;
                    try {
                        preciseCount = ioTDBUtil.queryProductionCountFromSnapshot(enterpriseCode,
                                mysqlRecord.getDeviceCode(), productName, finalBeginTime, finalEndTime);
                        log.debug("设备 {} 产品 {} 快照查询产量: {}", mysqlRecord.getDeviceCode(), productName, preciseCount);
                    } catch (Exception e) {
                        log.warn("快照查询失败，回退到原始数据查询: 设备={}, 产品={}", mysqlRecord.getDeviceCode(), productName, e);
                        // 如果快照查询失败，回退到原始数据查询
                        preciseCount = ioTDBUtil.queryProductionCountFromRawData(enterpriseCode,
                                mysqlRecord.getDeviceCode(), productName, finalBeginTime, finalEndTime);
                        log.debug("设备 {} 产品 {} 原始数据查询产量: {}", mysqlRecord.getDeviceCode(), productName, preciseCount);
                    }
                    deviceProductionMap.put(mysqlRecord.getDeviceCode(), preciseCount);
                } catch (Exception ex) {
                    log.warn("从IoTDB查询设备 {} 精确产量失败，使用MySQL数据", mysqlRecord.getDeviceCode(), ex);
                    // 如果IoTDB查询失败，使用MySQL的数据
                    deviceProductionMap.put(mysqlRecord.getDeviceCode(), mysqlRecord.getProductionCount());
                }
            }

            // 3. 合并数据：保留MySQL的完整结构，替换为IoTDB的精确数量
            for (ZhDeviceProductionRecord record : mysqlRecords) {
                Integer preciseCount = deviceProductionMap.get(record.getDeviceCode());

                // 保存原始数量用于对比
                record.setOriginalProductionCount(record.getProductionCount());

                if (preciseCount != null && preciseCount > 0) {
                    // 使用IoTDB的精确数量
                    record.setProductionCount(preciseCount);
                    log.info("设备 {} 使用IoTDB精确数量: {}", record.getDeviceCode(), preciseCount);
                } else {
                    // 如果IoTDB没有数据，保持MySQL的数据
                    log.warn("设备 {} 在IoTDB中无数据，使用MySQL估算数量: {}",
                            record.getDeviceCode(), record.getProductionCount());
                }

                // 始终添加记录，确保与productQuantity接口的数据一致性
                result.add(record);
            }

            // 处理时间边界
            result.forEach(record -> {
                if (record.getStartTime() != null && record.getStartTime().before(DateUtils.parseDate(finalBeginTime))) {
                    record.setStartTime(DateUtils.parseDate(finalBeginTime));
                }
                if (record.getEndTime() == null) {
                    record.setEndTime(DateUtils.parseDate(finalEndTime));
                }
            });

            // 验证数据一致性：计算明细数据的总和
            int detailTotalSum = result.stream().mapToInt(ZhDeviceProductionRecord::getProductionCount).sum();
            log.info("混合查询完成：从MySQL获取 {} 条记录结构，从IoTDB获取精确数量，最终返回 {} 条记录，明细总产量: {}",
                    mysqlRecords.size(), result.size(), detailTotalSum);

        } catch (Exception e) {
            log.error("混合查询失败，完全回退到MySQL查询", e);
            // 如果混合查询失败，完全回退到原有的MySQL查询
            result = zhDeviceProductionRecordMapper.selectProductDetail(productName, finalBeginTime, finalEndTime);

            // 处理时间边界
            result.forEach(record -> {
                if (record.getStartTime() != null && record.getStartTime().before(DateUtils.parseDate(finalBeginTime))) {
                    record.setStartTime(DateUtils.parseDate(finalBeginTime));
                }
                if (record.getEndTime() == null) {
                    record.setEndTime(DateUtils.parseDate(finalEndTime));
                }
            });
        }

        return result;
    }
}
