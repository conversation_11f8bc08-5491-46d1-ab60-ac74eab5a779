package com.ruoyi.zhengheiot.domain;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "AlarmVo", description = "报警信息")
public class AlarmVo {

    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    @ApiModelProperty(value = "设备编号")
    private String equipmentCode;

    @ApiModelProperty(value = "部门")
    private String deptName;
    @ApiModelProperty(value = "车间名称")
    private String workshopName;
    @ApiModelProperty(value = "设备类型")
    private String deviceType;
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    @ApiModelProperty(value = "报警次数")
    private Integer alarmCount;
    @ApiModelProperty(value = "报警时间")
    private Long alarmTime;
    @ApiModelProperty(value = "报警率")
    private String errorRate;

}
