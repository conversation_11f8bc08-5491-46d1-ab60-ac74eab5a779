package com.ruoyi.zhenghe.domain;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 告警配置对象 zh_alarm_configuration
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@ApiModel(value = "ZhAlarmConfiguration",description = "告警配置对象")
public class ZhAlarmConfiguration extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 设备id */
    @ApiModelProperty(value = "设备id")
    //@Excel(name = "设备id")
    private Long equipmentId;

    /** 点位id */
    @ApiModelProperty(value = "点位id")
    //@Excel(name = "点位id")
    private Long attrId;

    /** 告警等级id */
    @ApiModelProperty(value = "告警等级id")
    //@Excel(name = "告警等级id")
    private Long alarmLevelId;

    /** 第一个告警判断操作符 */
    @ApiModelProperty(value = "第一个告警判断操作符")
    //@Excel(name = "第一个告警判断操作符")
    private String oneAlarmOperator;

    /** 第一个告警判断数值 */
    @ApiModelProperty(value = "第一个告警判断数值")
    //@Excel(name = "第一个告警判断数值")
    private BigDecimal oneAlarmValue;

    /** 第二个告警判断操作符 */
    @ApiModelProperty(value = "第二个告警判断操作符")
    //@Excel(name = "第二个告警判断操作符")
    private String twoAlarmOperator;

    /** 第二个告警判断数值 */
    @ApiModelProperty(value = "第二个告警判断数值")
    //@Excel(name = "第二个告警判断数值")
    private BigDecimal twoAlarmValue;

    /** 告警判断条件表达式 */
    @ApiModelProperty(value = "告警判断条件表达式")
    @Excel(name = "告警判断条件表达式",sort = 7)
    private String alarmJudgeExpression;

    /** 告警内容 */
    @ApiModelProperty(value = "告警内容")
    @Excel(name = "告警内容",sort = 5)
    private String alarmContent;

    /** 处理建议 */
    @ApiModelProperty(value = "处理建议")
    @Excel(name = "处理建议",sort = 6)
    private String alarmSuggestion;

    /** 部门id */
    //@Excel(name = "部门id")
    private Long deptId;

    /** 设备名称 */
    @ApiModelProperty(value = "设备名称")
    @Excel(name = "设备名称",sort = 1)
    private String equipmentName;

    /** 采集项名称 */
    @ApiModelProperty(value = "采集项名称")
    @Excel(name = "采集项",sort = 3)
    private String attrName;

    /** 告警等级名称 */
    @ApiModelProperty(value = "告警等级名称")
    @Excel(name = "告警等级",sort = 4)
    private String alarmLevelName;

    /** 采集项属性编码 */
    @ApiModelProperty(value = "采集项属性编码")
    private String attrCode;

    /** 回显判断表单式 */
    @ApiModelProperty(value = "回显判断表单式")
    private String alarmJudgeEcho;

    /** 设备编码 */
    @ApiModelProperty(value = "采集项属性编码")
    @Excel(name = "设备名称",sort = 2)
    private String equipmentCode;

    /** 设备属性数据类型 */
    private String attrType;

    public String getAttrType() {
        return attrType;
    }

    public void setAttrType(String attrType) {
        this.attrType = attrType;
    }

    public String getAlarmJudgeEcho() {
        return alarmJudgeEcho;
    }

    public void setAlarmJudgeEcho(String alarmJudgeEcho) {
        this.alarmJudgeEcho = alarmJudgeEcho;
    }

    public String getEquipmentCode() {
        return equipmentCode;
    }

    public void setEquipmentCode(String equipmentCode) {
        this.equipmentCode = equipmentCode;
    }

    public String getAttrCode() {
        return attrCode;
    }

    public void setAttrCode(String attrCode) {
        this.attrCode = attrCode;
    }

    public String getEquipmentName() {
        return equipmentName;
    }

    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    public String getAttrName() {
        return attrName;
    }

    public void setAttrName(String attrName) {
        this.attrName = attrName;
    }

    public String getAlarmLevelName() {
        return alarmLevelName;
    }

    public void setAlarmLevelName(String alarmLevelName) {
        this.alarmLevelName = alarmLevelName;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setEquipmentId(Long equipmentId) 
    {
        this.equipmentId = equipmentId;
    }

    public Long getEquipmentId() 
    {
        return equipmentId;
    }

    public void setAttrId(Long attrId) 
    {
        this.attrId = attrId;
    }

    public Long getAttrId() 
    {
        return attrId;
    }

    public void setAlarmLevelId(Long alarmLevelId) 
    {
        this.alarmLevelId = alarmLevelId;
    }

    public Long getAlarmLevelId() 
    {
        return alarmLevelId;
    }

    public void setOneAlarmOperator(String oneAlarmOperator) 
    {
        this.oneAlarmOperator = oneAlarmOperator;
    }

    public String getOneAlarmOperator() 
    {
        return oneAlarmOperator;
    }

    public void setOneAlarmValue(BigDecimal oneAlarmValue) 
    {
        this.oneAlarmValue = oneAlarmValue;
    }

    public BigDecimal getOneAlarmValue() 
    {
        return oneAlarmValue;
    }

    public void setTwoAlarmOperator(String twoAlarmOperator) 
    {
        this.twoAlarmOperator = twoAlarmOperator;
    }

    public String getTwoAlarmOperator() 
    {
        return twoAlarmOperator;
    }

    public void setTwoAlarmValue(BigDecimal twoAlarmValue) 
    {
        this.twoAlarmValue = twoAlarmValue;
    }

    public BigDecimal getTwoAlarmValue() 
    {
        return twoAlarmValue;
    }

    public void setAlarmJudgeExpression(String alarmJudgeExpression) 
    {
        this.alarmJudgeExpression = alarmJudgeExpression;
    }

    public String getAlarmJudgeExpression() 
    {
        return alarmJudgeExpression;
    }

    public void setAlarmContent(String alarmContent) 
    {
        this.alarmContent = alarmContent;
    }

    public String getAlarmContent() 
    {
        return alarmContent;
    }

    public void setAlarmSuggestion(String alarmSuggestion) 
    {
        this.alarmSuggestion = alarmSuggestion;
    }

    public String getAlarmSuggestion() 
    {
        return alarmSuggestion;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("equipmentId", getEquipmentId())
            .append("attrId", getAttrId())
            .append("alarmLevelId", getAlarmLevelId())
            .append("oneAlarmOperator", getOneAlarmOperator())
            .append("oneAlarmValue", getOneAlarmValue())
            .append("twoAlarmOperator", getTwoAlarmOperator())
            .append("twoAlarmValue", getTwoAlarmValue())
            .append("alarmJudgeExpression", getAlarmJudgeExpression())
            .append("alarmContent", getAlarmContent())
            .append("alarmSuggestion", getAlarmSuggestion())
            .append("remark", getRemark())
            .append("deptId", getDeptId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
