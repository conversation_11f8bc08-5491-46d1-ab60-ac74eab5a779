<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhPeriodMapper">

    <resultMap type="ZhPeriod" id="ZhPeriodResult">
        <result property="id"    column="id"    />
        <result property="periodName"    column="period_name"    />
        <result property="periodColor"    column="period_color"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectZhPeriodVo">
        select id, period_name, period_color, dept_id, create_by, create_time, update_by, update_time from zh_period
    </sql>

    <select id="selectZhPeriodList" parameterType="ZhPeriod" resultMap="ZhPeriodResult">
        <include refid="selectZhPeriodVo"/>
        <where>
            <if test="periodName != null  and periodName != ''"> and period_name like concat('%', #{periodName}, '%')</if>
            <if test="periodColor != null  and periodColor != ''"> and period_color = #{periodColor}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
        </where>
    </select>

    <select id="selectZhPeriodById" parameterType="Long" resultMap="ZhPeriodResult">
        <include refid="selectZhPeriodVo"/>
        where id = #{id}
    </select>

    <insert id="insertZhPeriod" parameterType="ZhPeriod" useGeneratedKeys="true" keyProperty="id">
        insert into zh_period
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="periodName != null">period_name,</if>
            <if test="periodColor != null">period_color,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="periodName != null">#{periodName},</if>
            <if test="periodColor != null">#{periodColor},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateZhPeriod" parameterType="ZhPeriod">
        update zh_period
        <trim prefix="SET" suffixOverrides=",">
            <if test="periodName != null">period_name = #{periodName},</if>
            <if test="periodColor != null">period_color = #{periodColor},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhPeriodById" parameterType="Long">
        delete from zh_period where id = #{id}
    </delete>

    <delete id="deleteZhPeriodByIds" parameterType="String">
        delete from zh_period where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>