package com.ruoyi.zhenghe.service.impl;

import java.util.List;
import java.util.Objects;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.zhenghe.mapper.ZhAlarmConfigurationMapper;
import com.ruoyi.zhenghe.domain.ZhAlarmConfiguration;
import com.ruoyi.zhenghe.service.IZhAlarmConfigurationService;

/**
 * 告警配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class ZhAlarmConfigurationServiceImpl implements IZhAlarmConfigurationService 
{
    @Autowired
    private ZhAlarmConfigurationMapper zhAlarmConfigurationMapper;

    /**
     * 查询告警配置
     * 
     * @param id 告警配置主键
     * @return 告警配置
     */
    @Override
    public ZhAlarmConfiguration selectZhAlarmConfigurationById(Long id)
    {
        return zhAlarmConfigurationMapper.selectZhAlarmConfigurationById(id);
    }

    /**
     * 查询告警配置列表
     * 
     * @param zhAlarmConfiguration 告警配置
     * @return 告警配置
     */
    @Override
    @DataScope(deptAlias = "t1")
    public List<ZhAlarmConfiguration> selectZhAlarmConfigurationList(ZhAlarmConfiguration zhAlarmConfiguration)
    {
        return zhAlarmConfigurationMapper.selectZhAlarmConfigurationList(zhAlarmConfiguration);
    }

    /**
     * 新增告警配置
     * 
     * @param zhAlarmConfiguration 告警配置
     * @return 结果
     */
    @Override
    public int insertZhAlarmConfiguration(ZhAlarmConfiguration zhAlarmConfiguration)
    {
        if (zhAlarmConfiguration.getAlarmJudgeExpression().contains("AND")){
            zhAlarmConfiguration.setAlarmJudgeExpression(zhAlarmConfiguration.getAlarmJudgeExpression().replace("AND","&&"));
        }
        if (zhAlarmConfiguration.getAlarmJudgeExpression().contains("OR")){
            zhAlarmConfiguration.setAlarmJudgeExpression(zhAlarmConfiguration.getAlarmJudgeExpression().replace("OR","||"));
        }
        zhAlarmConfiguration.setDeptId(SecurityUtils.getDeptId());
        zhAlarmConfiguration.setCreateBy(SecurityUtils.getUsername());
        zhAlarmConfiguration.setCreateTime(DateUtils.getNowDate());
        return zhAlarmConfigurationMapper.insertZhAlarmConfiguration(zhAlarmConfiguration);
    }

    /**
     * 修改告警配置
     * 
     * @param zhAlarmConfiguration 告警配置
     * @return 结果
     */
    @Override
    public int updateZhAlarmConfiguration(ZhAlarmConfiguration zhAlarmConfiguration)
    {
        if (zhAlarmConfiguration.getAlarmJudgeExpression().contains("AND")){
            zhAlarmConfiguration.setAlarmJudgeExpression(zhAlarmConfiguration.getAlarmJudgeExpression().replace("AND","&&"));
        }
        if (zhAlarmConfiguration.getAlarmJudgeExpression().contains("OR")){
            zhAlarmConfiguration.setAlarmJudgeExpression(zhAlarmConfiguration.getAlarmJudgeExpression().replace("OR","||"));
        }
        zhAlarmConfiguration.setCreateBy(SecurityUtils.getUsername());
        zhAlarmConfiguration.setCreateTime(DateUtils.getNowDate());
        zhAlarmConfiguration.setUpdateTime(DateUtils.getNowDate());
        return zhAlarmConfigurationMapper.updateZhAlarmConfiguration(zhAlarmConfiguration);
    }

    /**
     * 批量删除告警配置
     * 
     * @param ids 需要删除的告警配置主键
     * @return 结果
     */
    @Override
    public int deleteZhAlarmConfigurationByIds(Long[] ids)
    {
        return zhAlarmConfigurationMapper.deleteZhAlarmConfigurationByIds(ids);
    }

    /**
     * 删除告警配置信息
     * 
     * @param id 告警配置主键
     * @return 结果
     */
    @Override
    public int deleteZhAlarmConfigurationById(Long id)
    {
        return zhAlarmConfigurationMapper.deleteZhAlarmConfigurationById(id);
    }
}
