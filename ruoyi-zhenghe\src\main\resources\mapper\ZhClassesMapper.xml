<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhClassesMapper">

    <resultMap type="ZhClasses" id="ZhClassesResult">
        <result property="id"    column="id"    />
        <result property="classesName"    column="classes_name"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="restPeriod"    column="rest_period"    />
    </resultMap>

    <sql id="selectZhClassesVo">
        select id, classes_name, start_time, end_time, dept_id, create_by, create_time, update_by, update_time,rest_period from zh_classes t1
    </sql>

    <select id="selectZhClassesList" parameterType="ZhClasses" resultMap="ZhClassesResult">
        <include refid="selectZhClassesVo"/>
        <where>
            1 = 1
            <if test="classesName != null  and classesName != ''"> and t1.classes_name like concat('%', #{classesName}, '%')</if>
            <if test="startTime != null "> and t1.start_time = #{startTime}</if>
            <if test="endTime != null "> and t1.end_time = #{endTime}</if>
            <if test="deptId != null "> and t1.dept_id = #{deptId}</if>
        </where>
        ${params.dataScope}
        order by
        t1.create_time
        desc
    </select>

    <select id="selectZhClassesById" parameterType="Long" resultMap="ZhClassesResult">
        <include refid="selectZhClassesVo"/>
        where t1.id = #{id}
    </select>

    <insert id="insertZhClasses" parameterType="ZhClasses" useGeneratedKeys="true" keyProperty="id">
        insert into zh_classes
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="classesName != null">classes_name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="restPeriod != null">rest_period,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="classesName != null">#{classesName},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="restPeriod != null">#{restPeriod},</if>
        </trim>
    </insert>

    <update id="updateZhClasses" parameterType="ZhClasses">
        update zh_classes
        <trim prefix="SET" suffixOverrides=",">
            <if test="classesName != null">classes_name = #{classesName},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="restPeriod != null">rest_period = #{restPeriod},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhClassesById" parameterType="Long">
        delete from zh_classes where id = #{id}
    </delete>

    <delete id="deleteZhClassesByIds" parameterType="String">
        delete from zh_classes where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>