package com.ruoyi.util.mq;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.util.iotdb.IoTDBUtil;
import com.ruoyi.zhenghe.domain.ZhDeviceProductionRecord;
import com.ruoyi.zhenghe.domain.ZhDeviceTypeAttr;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.mapper.ZhDeviceProductionRecordMapper;
import com.ruoyi.zhenghe.mapper.ZhDeviceTypeAttrMapper;
import com.ruoyi.zhenghe.mapper.ZhIotEquipmentMapper;
import com.ruoyi.zhenghe.service.IZhAlarmMessageService;
import com.ruoyi.zhenghe.service.IZhDeviceProductionRecordService;
import com.ruoyi.zhengheiot.domain.IotRealData;
import com.ruoyi.zhengheiot.mapper.IotRealDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/10/22 10:40
 * MQTT 消息接收回调类
 */
@Component
@Slf4j
public class MQTTReceiveCallback implements MqttCallback {

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private IoTDBUtil ioTDBUtil;

    @Resource
    ZhIotEquipmentMapper equipmentMapper;

    @Resource
    ZhDeviceTypeAttrMapper zhDeviceTypeAttrMapper;

    @Resource
    private ZhDeviceProductionRecordMapper zhDeviceProductionRecordMapper;

    @Value("${mqtt.url}")
    private String host;

    @Value("${mqtt.topic}")
    private String topic;

    @Value("${mqtt.username}")
    private String username;

    @Value("${mqtt.password}")
    private String password;

    private MqttClient client;
    private MqttConnectOptions options;
    @Resource
    IotRealDataMapper iotRealDataMapper;
    @Resource
    IZhAlarmMessageService zhAlarmMessageService;
    @Resource
    IZhDeviceProductionRecordService zhDeviceProductionRecordService;
    int logFlag = 0;

    private final String clientId = String.valueOf(System.currentTimeMillis());

    ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * 主题信息解析结果
     */
    public static class TopicInfo {
        private String enterpriseCode;
        private String deviceCode;
        private String[] topicParts;

        public TopicInfo(String enterpriseCode, String deviceCode, String[] topicParts) {
            this.enterpriseCode = enterpriseCode;
            this.deviceCode = deviceCode;
            this.topicParts = topicParts;
        }

        public String getEnterpriseCode() {
            return enterpriseCode;
        }

        public String getDeviceCode() {
            return deviceCode;
        }

        public String[] getTopicParts() {
            return topicParts;
        }
    }

    /**
     * 数据处理结果
     */
    public static class DataProcessResult {
        private List<IotRealData> tempList;
        private List<IotRealData> realList;

        public DataProcessResult() {
            this.tempList = new ArrayList<>();
            this.realList = new ArrayList<>();
        }

        public List<IotRealData> getTempList() {
            return tempList;
        }

        public List<IotRealData> getRealList() {
            return realList;
        }
    }

    @PostConstruct
    public void connect() throws InterruptedException {
        if (!validateConfig()) {
            log.error("MQTT 配置不完整，无法连接");
            return;
        }
        int retryCount = 1;
        while (retryCount > 0) {
            try {
                initializeMqttClient();
                log.error("MQTT 连接成功");
                System.out.println("MQTT 尝试连接成功");
                break;
            } catch (Exception e) {
                retryCount--;
                if (retryCount == 0) {
                    handleConnectionError(e);
                } else {
                    log.warn("MQTT 连接失败，剩余重试次数: {}", retryCount);
                    Thread.sleep(3000); // 等待 3 秒后重试
                }
            }
        }
    }

    @Override
    public void connectionLost(Throwable throwable) {
        log.warn("MQTT 断线，正在尝试重新连接...");
        try {
            if (client != null) {
                client.close();
            }
            Thread.sleep(2000);
            connect();
            log.info("MQTT 重新连接成功");
        } catch (Exception e) {
            log.error("MQTT 重连失败: {}", e.getMessage());
        }
    }

    @Override
    public void messageArrived(String topic, MqttMessage message) {

//        if (logFlag < 100) {
//            if (message.toString().contains("TotalPower")) {
////                log.error("logFlag接收到MQTT消息，主题：{}，内容：{}", topic, message.toString());
//                System.out.println("接收到MQTT消息，主题：" + topic + "，内容：" + message.toString());
//                logFlag++;
//            }
//        }

//        if (message.toString().contains("QC120")) {
        if (logFlag++ < 100) {
            log.warn("logFlag 接收到MQTT消息，主题：{}，内容：{}", topic, message.toString());
        }
//        }

        try {
            executorService.execute(() -> {
                try {
                    if (topic.startsWith("mdioq")) {
                        executeMdIOQ(topic, message);
                    } else if (topic.startsWith("mdio")) {
                        executeMdIO(topic, message);
                    } else if (topic.startsWith("mdplcq")) {
                        executeMdPlcq(topic, message);
                    } else if (topic.startsWith("mdplc")) {
                        executeMdPlc(topic, message);
                    } else if (topic.startsWith("cnc")) {
                        executeCNC(topic, message);
                    } else if (topic.startsWith("zhdc")) {
                        executeZHDC(topic, message);
                    } else if (topic.startsWith("dou")) {
                        executeHsl(topic, message);
                    } else if (topic.startsWith("mcgs")) {
                        executeMCGS(topic, message);
                    } else if (topic.startsWith("ali")) {
                        executeAli(topic, message);
                    } else {
                        log.warn("收到未识别主题消息: {}", topic);
                    }
                } catch (Exception e) {
                    log.error("消息处理异常 [主题: {}, 内容: {}]", topic, message.toString(), e);
                }
            });
        } catch (RejectedExecutionException e) {
            log.error("线程池已满，拒绝处理MQTT消息 [主题: {}]", topic);
        }
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        log.debug("消息传递完成: {}", token.isComplete());
    }

    /**
     * 初始化 MQTT 客户端
     * <p>
     * 该方法初始化MQTT客户端，包括创建客户端实例、设置连接选项、
     * 设置回调函数、建立连接以及订阅主题等操作。
     *
     * @throws MqttException 如果初始化过程中出现MQTT相关异常
     */
    private void initializeMqttClient() throws MqttException {
        // 创建MQTT客户端实例，使用内存持久化存储
        client = new MqttClient(host, clientId, new MemoryPersistence());
        // 设置MQTT连接选项
        options = createMqttConnectOptions();
        // 设置回调接口，当前类实现IMqttDeliveryToken接口
        client.setCallback(this);
        // 建立与MQTT代理的连接
        client.connect(options);
        // 订阅预定义的主题
        subscribeToTopics();
    }

    /**
     * 创建 MQTT 连接选项
     */
    private MqttConnectOptions createMqttConnectOptions() {
        MqttConnectOptions options = new MqttConnectOptions();
        options.setCleanSession(false);
        options.setUserName(username);
        options.setPassword(password.toCharArray());
        options.setConnectionTimeout(10);
        options.setKeepAliveInterval(3600);
        return options;
    }

    /**
     * 订阅主题
     */
    private void subscribeToTopics() throws MqttException {
        String[] topics = topic.split(",");
        int[] qosLevels = new int[topics.length];
        for (int i = 0; i < qosLevels.length; i++) {
            qosLevels[i] = 1;
        }
        client.subscribe(topics, qosLevels);
    }

    /**
     * 校验配置是否完整
     */
    private boolean validateConfig() {
        if (host == null || topic == null || username == null || password == null) {
            log.error("MQTT 配置缺失: host={}, topic={}, username={}, password={}", host, topic, username, password);
            return false;
        }
        return true;
    }

    /**
     * 处理连接异常
     */
    private void handleConnectionError(Exception e) {
        log.error("MQTT 连接异常: {}", e.getMessage(), e);
    }

    /**
     * 解析主题信息
     *
     * @param topic      MQTT主题
     * @param splitLimit 分割限制，-1表示不限制
     * @return 主题信息
     */
    private TopicInfo parseTopicInfo(String topic, int splitLimit) {
        String[] topics = splitLimit > 0 ? topic.split("/", splitLimit) : topic.split("/");
        if (topics.length < 2) {
            return null;
        }
        String enterpriseCode = topics[1];
        String deviceCode = topics.length > 2 ? topics[2] : "";
        return new TopicInfo(enterpriseCode, deviceCode, topics);
    }

    /**
     * 创建IoT实时数据对象
     *
     * @param deviceCode 设备编码
     * @param key        属性键
     * @param value      属性值
     * @return IoT实时数据对象
     */
    private IotRealData createIotRealData(String deviceCode, String key, Object value) {
        IotRealData realData = new IotRealData();
        realData.setDeviceCode(deviceCode);
        realData.setTag(key.trim());
        realData.setVal(Convert.toStr(value));
        realData.setKey(deviceCode + "-" + key.trim());
        return realData;
    }

    /**
     * 检查缓存并更新数据列表
     *
     * @param enterpriseCode 企业编码
     * @param deviceCode     设备编码
     * @param key            属性键
     * @param value          属性值
     * @param realData       IoT实时数据对象
     * @param tempList       临时数据列表
     * @param realList       实时数据列表
     */
    private void checkCacheAndUpdateLists(String enterpriseCode, String deviceCode, String key,
                                          Object value, IotRealData realData,
                                          List<IotRealData> tempList, List<IotRealData> realList) {
        String cacheKey = enterpriseCode + "." + deviceCode + "." + key.trim();
        Object cachedValue = redisCache.getCacheObject(cacheKey);

        if (StringUtils.isNotNull(cachedValue) && !key.contains("OEE")) {
            if (!Convert.toStr(cachedValue).equals(realData.getVal())) {
                realList.add(realData);
            }
        } else {
            realList.add(realData);
            redisCache.setCacheObject(cacheKey, Convert.toStr(value), 60, TimeUnit.SECONDS);
        }
        tempList.add(realData);
    }

    /**
     * 保存数据到存储系统
     *
     * @param enterpriseCode 企业编码
     * @param deviceCode     设备编码
     * @param tempList       临时数据列表
     * @param realList       实时数据列表
     */
    private void saveDataToStorage(String enterpriseCode, String deviceCode,
                                   List<IotRealData> tempList, List<IotRealData> realList) {
        // 保存临时数据到数据库
        if (tempList.size() > 0) {
            iotRealDataMapper.saveOrUpdate(tempList);
            // 处理告警配置信息
            try {
                zhAlarmMessageService.executeAlarmMessage(deviceCode, tempList);
            } catch (Exception e) {
                log.warn("处理告警消息异常: {}", e.getMessage());
            }
        }

        // 保存实时数据到IoTDB
        if (realList.size() > 0) {
            ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, realList);
        }
    }

    /**
     * 处理MQTT消息异常
     *
     * @param topic   主题
     * @param message 消息内容
     * @param e       异常
     */
    private void handleMqttMessageException(String topic, String message, Exception e) {
        log.error("异常MQTT消息，主题：{}，内容：{}", topic, message, e);
    }

    /**
     * 处理特殊值转换（如科学计数法）
     *
     * @param key   属性键
     * @param value 原始值
     * @return 转换后的值
     */
    private String processSpecialValue(String key, Object value) {
        if (key.contains("TotalPower")) {
            try {
                return scientificNotationConverter(Convert.toStr(value)) + "";
            } catch (Exception e) {
                return Convert.toStr(value);
            }
        }
        return Convert.toStr(value);
    }

    /**
     * 执行MDPLC消息处理
     * 该方法负责解析MQTT消息，并根据主题和消息内容执行相应的处理逻辑
     *
     * @param topic   消息主题，用于确定企业代码
     * @param message MQTT消息，包含PLC数据
     */
    private void executeMdPlc(String topic, MqttMessage message) {
        TopicInfo topicInfo = parseTopicInfo(topic, 2);
        if (topicInfo == null) {
            return;
        }

        String enterpriseCode = topicInfo.getEnterpriseCode();
        String deviceCode = "";
        String plcCode = "";

        try {
            DataProcessResult result = new DataProcessResult();

            // 解析消息内容为JSON对象
            final JSONObject objects = JSONObject.parseObject(message.toString());
            String sn = "";
            try {
                plcCode = objects.getString("plcName");
                deviceCode = objects.getString("projectName") + "_" + plcCode;
                sn = objects.getString("sn");
            } catch (Exception e) {
                log.warn("解析PLC消息失败: {}", e.getMessage());
                return;
            }

            // 处理网关缓存
            String cacheKey = enterpriseCode + ":" + deviceCode;
            if (!redisCache.hasKey(cacheKey)) {
                if (setGateway(sn, deviceCode)) {
                    redisCache.setCacheObject(cacheKey, sn, 6, TimeUnit.HOURS);
                }
            }

            // 解析数据数组
            JSONArray dataArray = objects.getJSONArray("datas");
            if (dataArray == null) {
                return;
            }

            JSONObject data = new JSONObject();
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject msg = dataArray.getJSONObject(i);
                try {
                    data.put(msg.getString("name"), msg.getString("value"));
                } catch (Exception e) {
                    continue;
                }
            }

            // 处理数据
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey().trim();
                String value = processSpecialValue(key, entry.getValue());

                IotRealData realData = createIotRealData(deviceCode, key, value);
                checkCacheAndUpdateLists(enterpriseCode, deviceCode, key, entry.getValue(),
                        realData, result.getTempList(), result.getRealList());
            }

            // 保存数据 - 使用plcCode作为告警设备编码
            if (result.getTempList().size() > 0) {
                iotRealDataMapper.saveOrUpdate(result.getTempList());
                zhAlarmMessageService.executeAlarmMessage(plcCode, result.getTempList());
            }
            if (result.getRealList().size() > 0) {
                ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, result.getRealList());
            }

        } catch (Exception e) {
            handleMqttMessageException(topic, message.toString(), e);
        }
    }

    private void executeMdPlcq(String topic, MqttMessage message) {
        /*
        {"Number":760994,"Operating_Mode":358471,"Scissors6_TotalCount":0,"Cam_Angle":23116284,"Preset_Quantity":1000000,"Cam4_CloseAngle":220,"Actual_MoldHeight":0,"Mold_Strokes":141263893,"Preset_Speed":60,"Cam2_CloseAngle":220,"Cam1_OpenAngle":0,"Power_ON":0,"Scissors7_TotalCount":0,"Mold_Name":0,"Running_Speed":3979349,"Preset_BatchCount":30,"Cam3_OpenAngle":0,"Cam3_CloseAngle":220,"Operating_Status":2242653,"device_id":"QEA003","Mold_TotalUsageTime":43839256,"Number_this":794303,"Mold_Manufacturer":0,"AirBlow2_CloseAngle":280,"AirBlow4_CloseAngle":220,"Scissors5_TotalCount":0,"Scissors1_TotalCount":0,"AirBlow3_CloseAngle":220,"AirBlow1_CloseAngle":0,"AirBlow2_OpenAngle":200,"AirBlow3_OpenAngle":0,"Alarm":0,"Cam1_CloseAngle":220,"Scissors4_TotalCount":0,"AirBlow4_OpenAngle":0,"Scissors8_TotalCount":0,"Cam4_OpenAngle":0,"Current_BatchCount":0,"Mold_Height":0,"AirBlow1_OpenAngle":0,"Preset_Count":1000000,"Scissors2_TotalCount":0,"Cam2_OpenAngle":0,"Scissors3_TotalCount":0}
         */
        TopicInfo topicInfo = parseTopicInfo(topic, 2);
        if (topicInfo == null) {
            return;
        }

        String enterpriseCode = topicInfo.getEnterpriseCode();
        try {
            DataProcessResult result = new DataProcessResult();

            // 解析消息内容为JSON对象
            final JSONObject msg = JSONObject.parseObject(message.toString());
            String deviceCode = msg.getString("device_id");
            if (deviceCode == null) {
                return;
            }
            msg.remove("device_id");

            // 处理数据 - 直接添加到realList，不进行缓存检查
            for (Map.Entry<String, Object> entry : msg.entrySet()) {
                String key = entry.getKey().trim();
                IotRealData realData = createIotRealData(deviceCode, key, entry.getValue());
                result.getRealList().add(realData);
            }

            // 保存数据
            if (result.getRealList().size() > 0) {
                ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, result.getRealList());
                iotRealDataMapper.saveOrUpdate(result.getRealList());
                zhAlarmMessageService.executeAlarmMessage(deviceCode, result.getRealList());
            }
        } catch (Exception e) {
            handleMqttMessageException(topic, message.toString(), e);
        }
    }

    private void executeAli(String topic, MqttMessage message) throws Exception {
        TopicInfo topicInfo = parseTopicInfo(topic, -1);
        if (topicInfo == null || topicInfo.getTopicParts().length < 3) {
            return;
        }

        String enterpriseCode = topicInfo.getEnterpriseCode();
        String deviceCode = topicInfo.getTopicParts()[2];

        JSONObject msg = JSONObject.parseObject(new String(message.getPayload())).getJSONObject("params");
        if (msg == null || msg.size() == 0) {
            return;
        }

        DataProcessResult result = new DataProcessResult();

        for (Map.Entry<String, Object> entry : msg.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 过滤负值
            try {
                final double doubleValue = (double) value;
                if (doubleValue < 0) {
                    continue;
                }
                value = doubleValue + "";
            } catch (Exception e) {
                value = Convert.toStr(value);
            }

            IotRealData realData = createIotRealData(deviceCode, key, value);
            checkCacheAndUpdateLists(enterpriseCode, deviceCode, key, entry.getValue(),
                    realData, result.getTempList(), result.getRealList());
        }

        // 保存数据
        saveDataToStorage(enterpriseCode, deviceCode, result.getTempList(), result.getRealList());
    }


    private void executeZHDC(String topic, MqttMessage message) {
        TopicInfo topicInfo = parseTopicInfo(topic, -1);
        if (topicInfo == null) {
            return;
        }

        String enterpriseCode = topicInfo.getEnterpriseCode();
        try {
            DataProcessResult result = new DataProcessResult();

            // 解析消息内容为JSON对象
            final JSONObject msg = JSONObject.parseObject(message.toString());
            String deviceCode = msg.getString("device_id");
            if (deviceCode == null) {
                return;
            }

            JSONObject data = new JSONObject();
            // 尝试获取和解析不同的数据字段
            try {
                final JSONObject data1 = JSONObject.parseObject(msg.getString("data"));
                for (Map.Entry<String, Object> entry : data1.entrySet()) {
                    data.put(entry.getKey().trim(), entry.getValue());
                }
            } catch (Exception w) {
                // 如果解析失败，忽略错误
            }

            // 处理数据
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey().trim();
                IotRealData realData = createIotRealData(deviceCode, key, entry.getValue());
                checkCacheAndUpdateLists(enterpriseCode, deviceCode, key, entry.getValue(),
                        realData, result.getTempList(), result.getRealList());
            }

            // 保存数据
            saveDataToStorage(enterpriseCode, deviceCode, result.getTempList(), result.getRealList());

        } catch (Exception e1) {
            handleMqttMessageException(topic, message.toString(), e1);
        }
    }

    private void executeMCGS(String topic, MqttMessage message) {
        /*
        {
            "DeviceID": "juanguanji",
                "timestamp": "2025-06-19 10:57:17",
                "shengchanzongshu": 0,
                "zhuangheshu": 0,
                "shijishu": 0,
                "cishu1": 0,
                "shouming1": 0,
                "cishu2": 0,
                "shouming2": 0,
                "cishu3": 0,
                "shouming3": 0,
                "cishu4": 0,
                "shouming4": 0,
                "cishu5": 0,
                "shouming5": 0,
                "cishu6": 0,
                "shouming6": 0,
                "cishu7": 0,
                "shouming7": 0,
                "cishu8": 0,
                "shouming8": 0
        }
         */
        TopicInfo topicInfo = parseTopicInfo(topic, -1);
        if (topicInfo == null) {
            return;
        }

        String enterpriseCode = topicInfo.getEnterpriseCode();
        try {
            DataProcessResult result = new DataProcessResult();

            // 解析消息内容为JSON对象
            final JSONObject msg = JSONObject.parseObject(message.toString());
            String deviceCode = msg.getString("DeviceID");
            if (deviceCode == null) {
                return;
            }

            // 移除设备ID和时间戳字段
            msg.remove("DeviceID");
            msg.remove("timestamp"); // 移除时间戳字段，插入iotdb 报错

            // 处理数据 - MCGS不进行缓存检查，直接添加到realList
            for (Map.Entry<String, Object> entry : msg.entrySet()) {
                String key = entry.getKey().trim();
                IotRealData realData = createIotRealData(deviceCode, key, entry.getValue());
                result.getRealList().add(realData);
                result.getTempList().add(realData);
            }

            // 保存数据
            if (result.getTempList().size() > 0) {
                ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, result.getTempList());
                iotRealDataMapper.saveOrUpdate(result.getTempList());

                try {
                    zhAlarmMessageService.executeAlarmMessage(deviceCode, result.getTempList());
                } catch (Exception e) {
                    log.warn("处理MCGS告警消息异常: {}", e.getMessage());
                }
            }

        } catch (Exception e1) {
            handleMqttMessageException(topic, message.toString(), e1);
        }
    }

    private void executeMdIO(String topic, MqttMessage message) {
        TopicInfo topicInfo = parseTopicInfo(topic, 2);
        if (topicInfo == null) {
            return;
        }

        String enterpriseCode = topicInfo.getEnterpriseCode();
        try {
            DataProcessResult result = new DataProcessResult();

            // 解析消息内容为JSON对象
            final JSONObject msg = JSONObject.parseObject(message.toString());
            String deviceCode = msg.getString("devName");

            JSONObject data = new JSONObject();

            // 解析特定的数据字段
            try {
                JSONArray diState = msg.getJSONArray("diState");
                data.put("Power_ON", diState.getString(0));
                data.put("Alarm", diState.getString(2));

                JSONArray counter = msg.getJSONArray("counter");
                if (counter != null) {
                    if ("GTJ002".equals(deviceCode) || "GTT001_01".equals(deviceCode)) {
                        data.put("Number", "" + (int) (Double.parseDouble(counter.getString(1)) + Double.parseDouble(counter.getString(2))));
                    } else {
                        data.put("Number", counter.getString(1));
                    }
                }

                JSONArray counter2 = msg.getJSONArray("diCounter");
                if (counter2 != null && counter2.size() > 2) {
                    data.put("Number", counter2.getString(1));
                }

                JSONArray cycle = msg.getJSONArray("cycle");
                data.put("Cycle", cycle.getString(1));
            } catch (Exception w) {
                // 如果解析失败，忽略错误
            }

            // 处理数据
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey().trim();
                IotRealData realData = createIotRealData(deviceCode, key, entry.getValue());
                checkCacheAndUpdateLists(enterpriseCode, deviceCode, key, entry.getValue(),
                        realData, result.getTempList(), result.getRealList());
            }

            // 保存数据
            saveDataToStorage(enterpriseCode, deviceCode, result.getTempList(), result.getRealList());

        } catch (Exception e1) {
            handleMqttMessageException(topic, message.toString(), e1);
        }
    }

    private void executeMdIOQ(String topic, MqttMessage message) {
        // 分割主题以获取企业代码
        // 格式 topic：mdioq/f88540622c5245c9856061bfe11f133c
        String[] topics = topic.split("/");
        if (topics.length < 2) {
            return;
        }

//        log.error("接收到MdIOQ MQTT消息，主题：{}，内容：{}", topic, message.toString());

        String enterpriseCode = topics[1];
        String deviceCode = "";
        try {
            // 解析消息内容为JSON对象
            final JSONObject msg = JSONObject.parseObject(message.toString());

            deviceCode = msg.getString("devName");
            // 设备编码不存在则返回
            if (StringUtils.isEmpty(deviceCode)) {
                log.warn("设备编码为空，跳过处理");
                return;
            }

            // 获取时间戳
//            Long timestamp = msg.getLong("time");
            Long timestamp = System.currentTimeMillis();

            List<IotRealData> tempList = new ArrayList<>();

            // 获取这个设备的属性配置
            final List<ZhDeviceTypeAttr> attrs = zhDeviceTypeAttrMapper.selectZhDeviceTypeAttrListByequipmentCode(deviceCode);
            if (attrs == null || attrs.isEmpty()) {
                log.warn("设备 {} 没有配置属性，跳过处理", deviceCode);
                return;
            }

            // 解析配置，分离Number字段和产品字段
            String numberArrayName = null;    // 产量数组名，如 counter
            int numberArrayIndex = -1;        // 产量数组索引，如 0

            // 遍历属性配置，解析Number字段配置
            for (ZhDeviceTypeAttr attr : attrs) {
                String gatewayAttr = attr.getGatewayAttr();
                if (StringUtils.isEmpty(gatewayAttr) || !gatewayAttr.contains("_")) {
                    continue;
                }

                // 如果是Number字段，解析产量配置
                if ("Number".equals(attr.getAttrCode())) {
                    String[] parts = gatewayAttr.split("_");
                    if (parts.length == 2) {
                        numberArrayName = parts[0];  // 如 counter
                        try {
                            numberArrayIndex = Integer.parseInt(parts[1]); // 如 0
//                            log.debug("解析Number配置: {}[{}]", numberArrayName, numberArrayIndex);
                        } catch (NumberFormatException e) {
                            log.warn("Number字段配置索引格式错误：{}", gatewayAttr);
                        }
                    }
                    break; // 找到Number配置后跳出
                }
            }

            // 如果没有找到Number配置，使用默认值
            if (numberArrayName == null) {
                numberArrayName = "counter";
                numberArrayIndex = 0;
                log.debug("使用默认Number配置: {}[{}]", numberArrayName, numberArrayIndex);
            }

            // 获取产量数组
            JSONArray numberArray = msg.getJSONArray(numberArrayName);
            if (numberArray == null || numberArray.size() == 0) {
                log.warn("设备号:{},{}数组为空，跳过处理", deviceCode, numberArrayName);
                return;
            }

            // 获取当前产量
            int currentCount = 0;
            if (numberArrayIndex >= 0 && numberArrayIndex < numberArray.size()) {
                currentCount = numberArray.getIntValue(numberArrayIndex);

                //实时数据的存储
                {
                    IotRealData realData = new IotRealData();
                    realData.setDeviceCode(deviceCode);
                    realData.setTag("Number");
                    realData.setVal(String.valueOf(currentCount));
                    realData.setKey(deviceCode + "-" + realData.getTag());
                    tempList.add(realData);


                    IotRealData powerOnData = new IotRealData();
                    powerOnData.setDeviceCode(deviceCode);
                    powerOnData.setTag("Power_ON");
//                    powerOnData.setVal("1");
                    powerOnData.setKey(deviceCode + "-" + powerOnData.getTag());

                    String key = deviceCode + "-" + powerOnData.getTag();
                    final Integer lastNumber = (Integer) redisCache.getCacheObject(key);
                    if (lastNumber != null) {
                        if (lastNumber < currentCount) {
                            powerOnData.setVal("1");
                        }else {
                            powerOnData.setVal("0");
                        }
                        tempList.add(powerOnData);

                    }
                    redisCache.setCacheObject(key, currentCount, 20, TimeUnit.SECONDS);
                }

//                log.debug("获取产量: {}[{}] = {}", numberArrayName, numberArrayIndex, currentCount);
            } else {
                log.warn("产量数组索引超出范围: {}[{}]", numberArrayName, numberArrayIndex);
                return;
            }

            // 准备产品快照数据
            List<IoTDBUtil.ProductionSnapshot> productSnapshots = new ArrayList<>();

            // 遍历产品配置，找到正在生产的产品
            for (ZhDeviceTypeAttr attr : attrs) {

                String attrCode = attr.getAttrCode();  // 属性编码  CL04CF7_9
                String gatewayAttr = attr.getGatewayAttr(); // 网关属性配置，格式：diState_1
                String productName = attr.getAttrName(); // 产品名称  CL04CF7-9

                // 跳过Number字段，它不是产品配置
                if ("Number".equals(attrCode)) {
                    continue;
                }
                if (StringUtils.isEmpty(gatewayAttr) || !gatewayAttr.contains("_")) {
                    continue; // 跳过没有配置或格式错误的属性
                }
                IotRealData realData = new IotRealData();
                realData.setDeviceCode(deviceCode);
                realData.setTag(attrCode);
                realData.setKey(deviceCode + "-" + realData.getTag());
                realData.setVal("0");

                // 解析产品状态配置
                final String[] configParts = gatewayAttr.split("_");
                if (configParts.length != 2) {
                    log.warn("设备 {} 产品 {} 的网关配置格式错误：{}", deviceCode, productName, gatewayAttr);
                    continue;
                }

                String stateArrayName = configParts[0]; // 如 diState
                int stateArrayIndex;
                try {
                    stateArrayIndex = Integer.parseInt(configParts[1]); // 索引，如 1
                } catch (NumberFormatException e) {
                    log.warn("设备 {} 产品 {} 的网关配置索引格式错误：{}", deviceCode, productName, gatewayAttr);
                    continue;
                }

                // 获取产品状态数组
                JSONArray stateArray = msg.getJSONArray(stateArrayName);
                if (stateArray == null || stateArray.size() == 0) {
                    log.warn("设备 {} 的{}数组为空", deviceCode, stateArrayName);
                    continue;
                }

                for (int i = 1; i < stateArray.size(); i++) {
                    if (1 == stateArray.getIntValue(i)) {
                        if (i > (attrs.size() - 1)) {
                            Date recordTime = new Date(timestamp);
                            List<ZhDeviceProductionRecord> activeRecords = zhDeviceProductionRecordMapper.selectActiveProductionRecords(deviceCode);
                            if (activeRecords != null && !activeRecords.isEmpty()) {
                                for (ZhDeviceProductionRecord activeRecord : activeRecords) {
                                    // 结束其他产品的生产记录
                                    Date endTime = recordTime;
                                    int duration = calculateDuration(activeRecord.getStartTime(), endTime);
                                    zhDeviceProductionRecordMapper.finishProductionRecord(activeRecord.getId(), endTime, duration);

                                    log.info("-----=====设备 {} 切换产品，结束产品 {} 的生产记录，ID: {}，生产时长: {} 分钟",
                                            deviceCode, activeRecord.getProductName(), activeRecord.getId(), duration);
                                }
                            }
                            break;
                        }
                    }
                }


                // 检查该产品是否正在生产
                if (stateArrayIndex > 0 && stateArrayIndex < stateArray.size()) {
                    int stateValue = stateArray.getIntValue(stateArrayIndex);
                    if (stateValue == 1) {
                        // 当前正在生产这个产品
                        realData.setVal("1");
                        tempList.add(realData);

                        // 添加到快照数据
                        productSnapshots.add(new IoTDBUtil.ProductionSnapshot(productName, 1, currentCount));

                        // 调用生产记录处理服务（MySQL存储）
                        zhDeviceProductionRecordService.processProductionRecord(deviceCode, productName, currentCount, timestamp);

                        // 存储原始数据到IoTDB（不做任何计算，存储MQTT原始数据）
                        try {
                            ioTDBUtil.saveRawProductionData(enterpriseCode, deviceCode, productName, currentCount, 1, timestamp);
//                            log.debug("存储设备 {} 产品 {} 原始生产数据到IoTDB: 计数器={}", deviceCode, productName, currentCount);
                        } catch (Exception e) {
                            log.warn("存储原始生产数据到IoTDB失败: 设备={}, 产品={}", deviceCode, productName, e);
                        }

                    } else {
                        realData.setVal("0");
                        tempList.add(realData);

                        // 添加到快照数据
                        productSnapshots.add(new IoTDBUtil.ProductionSnapshot(productName, 0, currentCount));

                        // 产品停止生产时，记录原始数据到IoTDB
                        try {
                            ioTDBUtil.saveRawProductionData(enterpriseCode, deviceCode, productName, currentCount, 0, timestamp);
//                            log.debug("存储设备 {} 产品 {} 停止状态原始数据到IoTDB: 计数器={}", deviceCode, productName, currentCount);
                        } catch (Exception e) {
                            log.warn("存储停止状态原始数据到IoTDB失败: 设备={}, 产品={}", deviceCode, productName, e);
                        }
                    }
                } else {
                    log.warn("设备 {} 产品 {} 的状态数组索引超出范围: {}[{}]",
                            deviceCode, productName, stateArrayName, stateArrayIndex);
                    // 对于索引超出范围的产品，也添加到快照（状态为0）
                    productSnapshots.add(new IoTDBUtil.ProductionSnapshot(productName, 0, currentCount));
                }
            }

            // 保存产品快照数据到IoTDB
            if (!productSnapshots.isEmpty()) {
                try {
                    log.info("准备保存设备 {} 产品快照数据到IoTDB: {} 个产品, 企业编码: {}",
                            deviceCode, productSnapshots.size(), enterpriseCode);
                    for (IoTDBUtil.ProductionSnapshot snapshot : productSnapshots) {
                        log.info("快照数据: 产品={}, 状态={}, 累计产量={}",
                                snapshot.getProductName(), snapshot.getStatus(), snapshot.getCumulativeCount());
                    }

                    ioTDBUtil.saveProductionSnapshot(enterpriseCode, deviceCode, productSnapshots, timestamp);
                    log.info("成功保存设备 {} 产品快照数据到IoTDB: {} 个产品", deviceCode, productSnapshots.size());
                } catch (Exception e) {
                    log.error("保存产品快照数据到IoTDB失败: 设备={}, 企业编码={}, 错误信息={}",
                            deviceCode, enterpriseCode, e.getMessage(), e);
                }
            } else {
                log.warn("设备 {} 没有产品快照数据需要保存", deviceCode);
            }

            if (tempList.size() > 0) {
                iotRealDataMapper.saveOrUpdate(tempList);
                ioTDBUtil.addDataToIot(enterpriseCode, deviceCode, tempList);
            }
        } catch (Exception e1) {
            log.error("处理MQTT消息异常，主题：{}，内容：{}", topic, message.toString(), e1);
        }
    }

    private void executeCNC(String topic, MqttMessage message) {
        TopicInfo topicInfo = parseTopicInfo(topic, -1);
        if (topicInfo == null) {
            return;
        }

        String enterpriseCode = topicInfo.getEnterpriseCode();
        String deviceCode = "";

        try {
            DataProcessResult result = new DataProcessResult();

            JSONObject msg = JSONObject.parseObject(new String(message.getPayload()));
            for (Map.Entry<String, Object> entry : msg.entrySet()) {
                deviceCode = entry.getKey().trim();
                break; // 只取第一个设备编码
            }

            JSONObject data = msg.getJSONArray(deviceCode).getJSONObject(0).getJSONObject("values");

            // 处理网关缓存
            String sn = data.getString("ts");
            String cacheKey = enterpriseCode + ":" + deviceCode;
            if (!redisCache.hasKey(cacheKey)) {
                if (setGateway(sn, deviceCode)) {
                    redisCache.setCacheObject(cacheKey, sn, 6, TimeUnit.HOURS);
                }
            }

            // 处理特定的CNC数据转换
            if (data.containsKey("Count_count")) {
                data.put("Number", data.get("Count_count"));
            }

            if (data.containsKey("CNCStatus_cncStatus")) {
                String cncStatus = data.getString("CNCStatus_cncStatus");
                if (cncStatus.contains("MANUAL")) {
                    data.put("Work_Status", "2");
                    data.put("Product_Status", "1");
                    data.put("Power_ON", "2"); //待机
                } else if (cncStatus.contains("AUTO")) {
                    data.put("Work_Status", "1");
                    data.put("Product_Status", "1");
                    data.put("Power_ON", "1"); //开机
                } else if (cncStatus.contains("OFF")) {
                    data.put("Work_Status", "0");
                    data.put("Product_Status", "0");
                    data.put("Power_ON", "0"); //关机
                } else {
                    data.put("Work_Status", "0");
                    data.put("Product_Status", "0");
                    data.put("Power_ON", "0"); //关机
                }
            }
            // 处理数据
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey().trim();
                IotRealData realData = createIotRealData(deviceCode, key, entry.getValue());
                checkCacheAndUpdateLists(enterpriseCode, deviceCode, key, entry.getValue(),
                        realData, result.getTempList(), result.getRealList());
            }

            // 保存数据
            saveDataToStorage(enterpriseCode, deviceCode, result.getTempList(), result.getRealList());

        } catch (Exception e) {
            handleMqttMessageException(topic, message.toString(), e);
        }
    }

    private void executeHsl(String topic, MqttMessage message) throws Exception {
        TopicInfo topicInfo = parseTopicInfo(topic, -1);
        if (topicInfo == null || topicInfo.getTopicParts().length != 3) {
            return;
        }

        String enterpriseCode = topicInfo.getEnterpriseCode();
        String deviceCode = topicInfo.getTopicParts()[2];

        try {
            DataProcessResult result = new DataProcessResult();

            JSONObject msg = JSONObject.parseObject(new String(message.getPayload()));
            if (msg == null) {
                return;
            }

            // 检查失败消息
            if (msg.containsKey("__failedMsg")) {
                String failedMsg = msg.getString("__failedMsg");
                if (failedMsg.contains("Exception") || failedMsg.contains("失败")) {
                    log.info("异常数据，不做处理------=====-----");
                    return;
                }
            }

            for (Map.Entry<String, Object> entry : msg.entrySet()) {
                String key = entry.getKey().trim();

                // 跳过系统字段
                if (key.startsWith("__")) {
                    continue;
                }

                // 处理键名
                key = key.replace("！", "").replace("!", "").trim();

                // 处理值
                String tempVal = Convert.toStr(entry.getValue());
                if ("0.0".equals(tempVal)) {
                    tempVal = "0";
                } else if ("1.0".equals(tempVal)) {
                    tempVal = "1";
                }

                IotRealData realData = createIotRealData(deviceCode, key, tempVal);
                realData.setUpdateTime(new Date());

                // 处理时间戳
                if (msg.containsKey("__activeTime")) {
                    try {
                        realData.setUpdateTime(DateUtil.parse(Convert.toStr(msg.get("__activeTime"))));
                    } catch (Exception e) {
                        log.warn("解析时间戳失败: {}", e.getMessage());
                    }
                }

                // 特殊的缓存检查逻辑（与其他方法略有不同）
                String cacheKey = enterpriseCode + "." + deviceCode + "." + key;
                Object cachedValue = redisCache.getCacheObject(cacheKey);

                if (ObjectUtil.isNotNull(cachedValue)) {
                    if (!Convert.toStr(cachedValue).equals(realData.getVal())) {
                        result.getRealList().add(realData);
                        redisCache.setCacheObject(cacheKey, tempVal, 60, TimeUnit.SECONDS);
                    }
                } else {
                    result.getRealList().add(realData);
                    redisCache.setCacheObject(cacheKey, tempVal, 60, TimeUnit.SECONDS);
                }
                result.getTempList().add(realData);
            }

            log.info("tempList.size()  ：" + result.getTempList().size() + "+realList.size()： " + result.getRealList().size());

            // 保存数据
            saveDataToStorage(enterpriseCode, deviceCode, result.getTempList(), result.getRealList());

        } catch (Exception e) {
            handleMqttMessageException(topic, message.toString(), e);
        }
    }


    /**
     * 设置网关
     * <p>
     * 通过设备代码获取设备信息，并设置其网关代码，然后更新数据库中的设备信息
     * 此方法解释了为什么需要设置网关代码：为了关联设备与特定的网关，实现设备的远程管理和控制
     *
     * @param sn         网关序列号，用于标识和关联网关设备
     * @param deviceCode 设备代码，用于查询和更新设备信息
     */
    private boolean setGateway(String sn, String deviceCode) {
        // 参数校验
        if (StringUtils.isBlank(sn) || StringUtils.isBlank(deviceCode)) {
//            log.warn("参数无效：sn={} 或 deviceCode={} 不合法", sn, deviceCode);
            return false;
        }
        // 根据设备代码查询设备信息
        final ZhIotEquipment equipment = equipmentMapper.selectZhIotEquipmentByEquipmentCode(deviceCode);
        if (ObjectUtil.isNotNull(equipment)) {
            equipment.setGatewayCode(sn);
            final int i = equipmentMapper.updateZhIotEquipment(equipment);
            return i > 0;
        }
        return false;
    }

    public long scientificNotationConverter(String scientificStr) {
        // 方法2：使用BigDecimal避免浮点精度问题（推荐）
        java.math.BigDecimal bigDecimal = new java.math.BigDecimal(scientificStr);
        long preciseValue = bigDecimal.longValueExact();
//            System.out.println("科学计数法字符串：" + scientificStr);
//            System.out.println("精确值：" + preciseValue);
        return preciseValue;
    }

    /**
     * 计算生产时长（分钟）
     */
    private int calculateDuration(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return 0;
        }
        long diffInMillis = endTime.getTime() - startTime.getTime();
        return (int) (diffInMillis / (1000 * 60)); // 转换为分钟
    }
}
