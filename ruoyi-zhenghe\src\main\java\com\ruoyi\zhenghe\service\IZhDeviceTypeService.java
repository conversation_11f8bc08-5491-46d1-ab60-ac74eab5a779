package com.ruoyi.zhenghe.service;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhDeviceType;

/**
 * 设备类型/Iot物模型Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface IZhDeviceTypeService 
{
    /**
     * 查询设备类型/Iot物模型
     * 
     * @param id 设备类型/Iot物模型主键
     * @return 设备类型/Iot物模型
     */
    public ZhDeviceType selectZhDeviceTypeById(Long id);

    /**
     * 查询设备类型/Iot物模型列表
     * 
     * @param zhDeviceType 设备类型/Iot物模型
     * @return 设备类型/Iot物模型集合
     */
    public List<ZhDeviceType> selectZhDeviceTypeList(ZhDeviceType zhDeviceType);

    /**
     * 新增设备类型/Iot物模型
     * 
     * @param zhDeviceType 设备类型/Iot物模型
     * @return 结果
     */
    public int insertZhDeviceType(ZhDeviceType zhDeviceType);

    /**
     * 修改设备类型/Iot物模型
     * 
     * @param zhDeviceType 设备类型/Iot物模型
     * @return 结果
     */
    public int updateZhDeviceType(ZhDeviceType zhDeviceType);

    /**
     * 批量删除设备类型/Iot物模型
     * 
     * @param ids 需要删除的设备类型/Iot物模型主键集合
     * @return 结果
     */
    public int deleteZhDeviceTypeByIds(Long[] ids);

    /**
     * 删除设备类型/Iot物模型信息
     * 
     * @param id 设备类型/Iot物模型主键
     * @return 结果
     */
    public int deleteZhDeviceTypeById(Long id);
}
