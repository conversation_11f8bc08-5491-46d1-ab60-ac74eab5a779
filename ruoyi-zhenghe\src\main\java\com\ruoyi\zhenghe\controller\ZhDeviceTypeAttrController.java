package com.ruoyi.zhenghe.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.zhenghe.domain.ZhDeviceType;
import com.ruoyi.zhenghe.domain.ZhDeviceTypeAttr;
import com.ruoyi.zhenghe.service.IZhDeviceTypeAttrService;
import com.ruoyi.zhenghe.service.IZhDeviceTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 设备类型属性/物模型属性Controller
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Api(tags = "设备类型属性配置")
@RestController
@RequestMapping("/zhenghe/deviceAttr")
public class ZhDeviceTypeAttrController extends BaseController {
    @Autowired
    private IZhDeviceTypeAttrService zhDeviceTypeAttrService;

    @Autowired
    IZhDeviceTypeService zhDeviceTypeService;

    /**
     * 查询设备类型属性/物模型属性列表
     */
    @ApiOperation(value = "设备类型属性配置列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tslId", value = "类型的id", required = true, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "attrName", value = "属性名称", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "attrCode", value = "属性编码", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "gatewayAttr", value = "网关属性配置", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, paramType = "query", dataType = "integer"),
            @ApiImplicitParam(name = "pageSize", value = "条数", required = true, paramType = "query", dataType = "integer")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:deviceType:configuration')")
    @GetMapping("/list")
    public TableDataInfo list(ZhDeviceTypeAttr zhDeviceTypeAttr) throws Exception {
        if (zhDeviceTypeAttr.getTslId() == null) {
            throw new Exception("类型tslId为空");
        }
        startPage();
        List<ZhDeviceTypeAttr> list = zhDeviceTypeAttrService.selectZhDeviceTypeAttrList(zhDeviceTypeAttr);
        return getDataTable(list);
    }

    /**
     * 导出设备类型属性/物模型属性列表
     */
    @PreAuthorize("@ss.hasPermi('zhenghe:deviceType:export')")
    @Log(title = "设备类型属性/物模型属性", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhDeviceTypeAttr zhDeviceTypeAttr) {
        List<ZhDeviceTypeAttr> list = zhDeviceTypeAttrService.selectZhDeviceTypeAttrList(zhDeviceTypeAttr);
        ExcelUtil<ZhDeviceTypeAttr> util = new ExcelUtil<ZhDeviceTypeAttr>(ZhDeviceTypeAttr.class);
        util.exportExcel(response, list, "设备类型属性");
    }

    /**
     * 获取设备类型属性/物模型属性详细信息
     */
    @PreAuthorize("@ss.hasPermi('zhenghe:deviceType:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(zhDeviceTypeAttrService.selectZhDeviceTypeAttrById(id));
    }

    /**
     * 新增设备类型属性/物模型属性
     */
    @ApiOperation(value = "新增设备类型属性/物模型属性")
    @PreAuthorize("@ss.hasPermi('zhenghe:deviceType:add')")
    @Log(title = "设备类型属性/物模型属性", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZhDeviceTypeAttr zhDeviceTypeAttr) {
        return toAjax(zhDeviceTypeAttrService.insertZhDeviceTypeAttr(zhDeviceTypeAttr));
    }


    @ApiOperation(value = "导入设备属性")
    //@PreAuthorize("@ss.hasPermi('zhenghe:level:import')")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "导入文件", required = true, paramType = "form", dataType = "multipartFile")
    })
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws IOException {
        if (file == null) {
            throw new ServiceException("导入文件不能为空");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx")) {
            throw new ServiceException("导入文件格式不正确");
        }
        ExcelUtil<ZhDeviceTypeAttr> util = new ExcelUtil<>(ZhDeviceTypeAttr.class);
        List<ZhDeviceTypeAttr> list = util.importExcel(file.getInputStream());
        if (list == null || list.size() <= 0) {
            throw new ServiceException("导入设备类型数据不能为空");
        }
        int successNum = 0;
        int failureNum = 0;

        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        ZhDeviceType zhDeviceType = zhDeviceTypeService.selectZhDeviceTypeById(Long.valueOf(list.get(0).getTslId()));
        if (zhDeviceType == null) {
            throw new ServiceException("导入设备类型数据不存在");
        }


        for (ZhDeviceTypeAttr attr : list) {
            try {
                attr.setTslId(zhDeviceType.getId());
                attr.setCreateBy(getUsername());
                attr.setCreateTime(new Date());
                zhDeviceTypeAttrService.insertZhDeviceTypeAttr(attr);
                successNum++;
                successMsg.append("<br/>" + successNum + "、告警等级 " + attr.getAttrName() + " 导入成功");
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、告警等级 " + attr.getAttrName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return AjaxResult.success(successMsg.toString());
    }


    /**
     * 导出设备类型属性/物模型属性列表
     */
    @ApiOperation(value = "设备属性模板下载")
    @PostMapping("/template/download")
    public void download(HttpServletResponse DeviceTypeAttr, ZhDeviceTypeAttr zhDeviceTypeAttr) {
        List<ZhDeviceTypeAttr> list = new ArrayList<>();
        ExcelUtil<ZhDeviceTypeAttr> util = new ExcelUtil<ZhDeviceTypeAttr>(ZhDeviceTypeAttr.class);
        util.exportExcel(DeviceTypeAttr, list, "设备类型属性");
    }

    /**
     * 修改设备类型属性/物模型属性
     */
    @ApiOperation(value = "修改设备类型属性/物模型属性")
    @PreAuthorize("@ss.hasPermi('zhenghe:deviceType:edit')")
    @Log(title = "设备类型属性/物模型属性")
    @PutMapping
    public AjaxResult edit(@RequestBody ZhDeviceTypeAttr zhDeviceTypeAttr) {
        return toAjax(zhDeviceTypeAttrService.updateZhDeviceTypeAttr(zhDeviceTypeAttr));
    }

    /**
     * 删除设备类型属性/物模型属性
     */
    @ApiOperation(value = "删除设备类型属性/物模型属性")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "主键ID串", required = true, paramType = "path", dataType = "Long")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:deviceType:remove')")
    @Log(title = "设备类型属性/物模型属性", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(zhDeviceTypeAttrService.deleteZhDeviceTypeAttrByIds(ids));
    }

    /**
     * 根据设备编号查询设备类型属性列表
     */
    @ApiOperation(value = "根据设备编号查询设备类型属性列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "equipmentCode", value = "设备编号", required = true, paramType = "path", dataType = "String")
    })
    @GetMapping("/equipment/{equipmentCode}")
    public AjaxResult getAttrListByEquipmentCode(@PathVariable String equipmentCode) {
        List<ZhDeviceTypeAttr> list = zhDeviceTypeAttrService.selectZhDeviceTypeAttrListByequipmentCode(equipmentCode);
        return AjaxResult.success(list);
    }
}
