package com.ruoyi.screen.service;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.screen.service.MultiLevelCacheService;
import com.ruoyi.util.iotdb.IoTDBUtil;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 优化的IoTDB查询服务
 * 提供批量查询、查询合并、结果缓存等优化策略
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
@Slf4j
@Service
public class OptimizedIoTDBService {

    @Autowired
    private IoTDBUtil ioTDBUtil;

    @Autowired
    private MultiLevelCacheService cacheService;

    // 批量查询专用线程池
    private final ExecutorService batchQueryExecutor = Executors.newFixedThreadPool(8);

    /**
     * 批量查询设备生产计数（带缓存）
     */
    public Map<String, Integer> batchQueryProductionCount(List<ZhIotEquipment> equipmentList, 
                                                         String startTime, String endTime) {
        Map<String, Integer> results = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // 分批处理，每批10个设备
        int batchSize = 10;
        for (int i = 0; i < equipmentList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, equipmentList.size());
            List<ZhIotEquipment> batch = equipmentList.subList(i, endIndex);
            
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                processBatchProductionCount(batch, startTime, endTime, results);
            }, batchQueryExecutor);
            
            futures.add(future);
        }

        // 等待所有批次完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .get(60, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("批量查询生产计数超时", e);
        }

        return results;
    }

    /**
     * 批量查询设备生产时长（带缓存）
     */
    public Map<String, Integer> batchQueryProductionDuration(List<ZhIotEquipment> equipmentList, 
                                                           Map<String, String> equipmentCodes,
                                                           String startTime, String endTime) {
        Map<String, Integer> results = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // 分批处理
        int batchSize = 10;
        for (int i = 0; i < equipmentList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, equipmentList.size());
            List<ZhIotEquipment> batch = equipmentList.subList(i, endIndex);
            
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                processBatchProductionDuration(batch, equipmentCodes, startTime, endTime, results);
            }, batchQueryExecutor);
            
            futures.add(future);
        }

        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .get(60, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("批量查询生产时长超时", e);
        }

        return results;
    }

    /**
     * 智能查询生产计数（优先使用缓存）
     */
    public Integer smartQueryProductionCount(String equipmentCode, String startTime, String endTime) {
        // 先尝试从缓存获取
        Integer cached = cacheService.getDeviceProductionCount(equipmentCode, startTime, endTime);
        if (cached != null) {
            return cached;
        }

        // 缓存未命中，查询数据库
        try {
            int count = ioTDBUtil.queryIotEquipmentProductionCount(Constants.TENANT, equipmentCode, null, startTime, endTime);
            // 缓存结果
            cacheService.setDeviceProductionCount(equipmentCode, startTime, endTime, count);
            return count;
        } catch (Exception e) {
            log.error("查询设备 {} 生产计数失败", equipmentCode, e);
            return 0;
        }
    }

    /**
     * 智能查询生产时长（优先使用缓存）
     */
    public Integer smartQueryProductionDuration(String equipmentCode, String code, String startTime, String endTime) {
        // 先尝试从缓存获取
        Integer cached = cacheService.getDeviceProductionDuration(equipmentCode, code, startTime, endTime);
        if (cached != null) {
            return cached;
        }

        // 缓存未命中，查询数据库
        try {
            int duration = ioTDBUtil.queryIotEquipmentProductionDuration(Constants.TENANT, equipmentCode, code, startTime, endTime);
            // 缓存结果
            cacheService.setDeviceProductionDuration(equipmentCode, code, startTime, endTime, duration);
            return duration;
        } catch (Exception e) {
            log.error("查询设备 {} 生产时长失败", equipmentCode, e);
            return 0;
        }
    }

    /**
     * 时间段聚合查询（多个时间段一次性查询）
     */
    public Map<String, Integer> aggregateQueryByTimeSegments(List<ZhIotEquipment> equipmentList, 
                                                           List<TimeSegment> timeSegments) {
        Map<String, Integer> results = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (TimeSegment segment : timeSegments) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    int totalCount = 0;
                    for (ZhIotEquipment equipment : equipmentList) {
                        if (!equipment.getEquipmentName().contains("电表")) {
                            int count = smartQueryProductionCount(equipment.getEquipmentCode(), 
                                segment.getStartTime(), segment.getEndTime());
                            totalCount += count;
                        }
                    }
                    results.put(segment.getKey(), totalCount);
                } catch (Exception e) {
                    log.error("聚合查询时间段 {} 失败", segment.getKey(), e);
                    results.put(segment.getKey(), 0);
                }
            }, batchQueryExecutor);
            
            futures.add(future);
        }

        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .get(120, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("聚合查询超时", e);
        }

        return results;
    }

    /**
     * 处理批次生产计数查询
     */
    private void processBatchProductionCount(List<ZhIotEquipment> batch, String startTime, String endTime, 
                                           Map<String, Integer> results) {
        for (ZhIotEquipment equipment : batch) {
            try {
                if (!equipment.getEquipmentName().contains("电表")) {
                    Integer count = smartQueryProductionCount(equipment.getEquipmentCode(), startTime, endTime);
                    results.put(equipment.getEquipmentCode(), count);
                }
            } catch (Exception e) {
                log.error("批量查询设备 {} 生产计数失败", equipment.getEquipmentName(), e);
                results.put(equipment.getEquipmentCode(), 0);
            }
        }
    }

    /**
     * 处理批次生产时长查询
     */
    private void processBatchProductionDuration(List<ZhIotEquipment> batch, Map<String, String> equipmentCodes,
                                              String startTime, String endTime, Map<String, Integer> results) {
        for (ZhIotEquipment equipment : batch) {
            try {
                if (!equipment.getEquipmentName().contains("电表")) {
                    String code = equipmentCodes.getOrDefault(equipment.getEquipmentCode(), "Power_ON");
                    Integer duration = smartQueryProductionDuration(equipment.getEquipmentCode(), code, startTime, endTime);
                    results.put(equipment.getEquipmentCode(), duration);
                }
            } catch (Exception e) {
                log.error("批量查询设备 {} 生产时长失败", equipment.getEquipmentName(), e);
                results.put(equipment.getEquipmentCode(), 0);
            }
        }
    }

    /**
     * 获取查询统计信息
     */
    public Map<String, Object> getQueryStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("batchSize", 10);
        stats.put("threadPoolSize", 8);
        stats.put("cacheEnabled", true);
        return stats;
    }

    /**
     * 时间段类
     */
    public static class TimeSegment {
        private String key;
        private String startTime;
        private String endTime;

        public TimeSegment(String key, String startTime, String endTime) {
            this.key = key;
            this.startTime = startTime;
            this.endTime = endTime;
        }

        public String getKey() { return key; }
        public String getStartTime() { return startTime; }
        public String getEndTime() { return endTime; }
    }

    /**
     * 创建日期时间段列表
     */
    public static List<TimeSegment> createDailySegments(int days) {
        List<TimeSegment> segments = new ArrayList<>();
        Date now = new Date();
        
        for (int i = days - 1; i >= 0; i--) {
            Date dayStart = DateUtil.beginOfDay(DateUtil.offsetDay(now, -i));
            Date dayEnd = DateUtil.endOfDay(dayStart);
            
            String key = DateUtil.format(dayStart, "MM-dd");
            String startTime = DateUtil.format(dayStart, "yyyy-MM-dd HH:mm:ss");
            String endTime = DateUtil.format(dayEnd, "yyyy-MM-dd HH:mm:ss");
            
            segments.add(new TimeSegment(key, startTime, endTime));
        }
        
        return segments;
    }

    /**
     * 创建小时时间段列表
     */
    public static List<TimeSegment> createHourlySegments(int hours) {
        List<TimeSegment> segments = new ArrayList<>();
        Date now = new Date();
        Date dayStart = DateUtil.beginOfDay(now);
        
        for (int i = hours - 1; i >= 0; i--) {
            Date hourEnd = DateUtil.offsetHour(now, -i);
            if (hourEnd.before(dayStart)) {
                continue;
            }
            
            String key = DateUtil.format(hourEnd, "HH") + ":00";
            String startTime = DateUtil.format(dayStart, "yyyy-MM-dd HH:mm:ss");
            String endTime = DateUtil.format(hourEnd, "yyyy-MM-dd HH:mm:ss");
            
            segments.add(new TimeSegment(key, startTime, endTime));
        }
        
        return segments;
    }
}
