package com.ruoyi.zhenghe.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.zhenghe.domain.ZhClasses;
import com.ruoyi.zhenghe.service.IZhClassesService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 班次Controller
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
@Api(tags = "班次Controller")
@RestController
@RequestMapping("/zhenghe/classes")
public class ZhClassesController extends BaseController
{
    @Autowired
    private IZhClassesService zhClassesService;

    /**
     * 查询班次列表
     */
    @ApiOperation(value = "查询班次列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum",value = "页数",required = true,paramType = "query",dataType = "integer"),
            @ApiImplicitParam(name = "pageSize",value = "条数",required = true,paramType = "query",dataType = "integer")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:classes:list')")
    @GetMapping("/list")
    public TableDataInfo list(ZhClasses zhClasses)
    {
        startPage();
        List<ZhClasses> list = zhClassesService.selectZhClassesList(zhClasses);
        return getDataTable(list);
    }

    /**
     * 导出班次列表
     */
    @ApiOperation(value = "导出班次列表")
    @PreAuthorize("@ss.hasPermi('zhenghe:classes:export')")
    @Log(title = "班次", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhClasses zhClasses)
    {
        List<ZhClasses> list = zhClassesService.selectZhClassesList(zhClasses);
        ExcelUtil<ZhClasses> util = new ExcelUtil<ZhClasses>(ZhClasses.class);
        util.exportExcel(response, list, "班次数据");
    }

    /**
     * 获取班次详细信息
     */
    @ApiOperation(value = "获取班次详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "班次id",required = true,paramType = "path",dataType = "long")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:classes:query')")
    @GetMapping(value = "/{id}")
    public R<ZhClasses> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(zhClassesService.selectZhClassesById(id));
    }

    /**
     * 新增班次
     */
    @ApiOperation(value = "新增班次")
    @PreAuthorize("@ss.hasPermi('zhenghe:classes:add')")
    @Log(title = "班次", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZhClasses zhClasses)
    {
        return toAjax(zhClassesService.insertZhClasses(zhClasses));
    }

    /**
     * 修改班次
     */
    @ApiOperation(value = "修改班次")
    @PreAuthorize("@ss.hasPermi('zhenghe:classes:edit')")
    @Log(title = "班次", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhClasses zhClasses)
    {
        return toAjax(zhClassesService.updateZhClasses(zhClasses));
    }

    /**
     * 删除班次
     */
    @ApiOperation(value = "删除班次")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids",value = "班次id",required = true,paramType = "path",dataType = "long")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:classes:remove')")
    @Log(title = "班次", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(zhClassesService.deleteZhClassesByIds(ids));
    }
}
