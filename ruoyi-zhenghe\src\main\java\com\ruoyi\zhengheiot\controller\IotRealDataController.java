package com.ruoyi.zhengheiot.controller;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.util.iotdb.IoTDBUtil;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhengheiot.domain.DepartmentEquipmentTypeEquipmentVo;
import com.ruoyi.zhengheiot.domain.EquipmentDetail;
import com.ruoyi.zhengheiot.domain.IotRealData;
import com.ruoyi.zhengheiot.service.IIotRealDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * IoT实时数据Controller
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Api(tags = "实时数据")
@RestController
@Slf4j
@RequestMapping("/zhengheiot/iotdata")
public class IotRealDataController extends BaseController {

    @Autowired
    private IIotRealDataService iotRealDataService;
    @Autowired
    private IoTDBUtil ioTDBUtil;

    /**
     * 查询设备
     */
    @ApiOperation("查询设备类型设备列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "equipmentName", value = "设备名称", required = false, paramType = "query", dataType = "String")
    })
    @PreAuthorize("@ss.hasPermi('zhengheiot:iotdata:list')")
    @GetMapping("/devices/list")
    public AjaxResult devicesList(ZhIotEquipment equipment) {

        List<DepartmentEquipmentTypeEquipmentVo> equipmentTypeEquipmentVoList = iotRealDataService.getEquipmentTypeEquipmentVoList(equipment);

        return success(equipmentTypeEquipmentVoList);

    }


    /**
     * 查询IoT实时数据列表
     */
    @ApiOperation("查询设备实时数据")
    @ApiImplicitParams(
            @ApiImplicitParam(name = "id", value = "设备id", required = true, paramType = "query", dataType = "Long")
    )
//    @PreAuthorize("@ss.hasPermi('zhengheiot:iotdata:list')")
    @GetMapping("/devices/real/data")
    public AjaxResult list(@RequestParam(required = true) Long id) {
        try {
            // 参数校验
            validateIdParameter(id);
            // 获取实时数据
            final EquipmentDetail realDataList = iotRealDataService.getRealDataList(id);
            return success(realDataList);
        } catch (IllegalArgumentException e) {
            // 参数校验失败，返回具体的错误信息
            return error(e.getMessage());
        } catch (Exception e) {
            // 记录异常日志
            log.error("查询IoT实时数据列表失败，id: {}", id, e);
            return error("系统内部错误，请稍后重试");
        }
    }

    @ApiOperation("设备实时详情")
    @ApiImplicitParams(
            @ApiImplicitParam(name = "id", value = "设备id", required = true, paramType = "query", dataType = "Long")
    )
    @GetMapping("/devices/real/data/detail")
    public AjaxResult realDetail(@RequestParam(required = true) Long id) {
        double workTime = 0;
        try {
            // 参数校验
            validateIdParameter(id);

            // 获取实时数据
            final JSONObject entries = iotRealDataService.realDetail(id);
            return success(entries);
        } catch (IllegalArgumentException e) {
            // 参数校验失败，返回具体的错误信息
            return error(e.getMessage());
        } catch (Exception e) {
            // 记录异常日志
            log.error("查询设备实时数据，id: {}", id, e);
            return error("系统内部错误，请稍后重试");
        }
    }

    /**
     * 导出IoT实时数据列表
     */
    @PreAuthorize("@ss.hasPermi('zhengheiot:iotdata:export')")
    @Log(title = "IoT实时数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IotRealData iotRealData) {
        List<IotRealData> list = iotRealDataService.selectIotRealDataList(iotRealData);
        ExcelUtil<IotRealData> util = new ExcelUtil<IotRealData>(IotRealData.class);
        util.exportExcel(response, list, "IoT实时数据数据");
    }

    /**
     * 获取IoT实时数据详细信息
     */
//    @PreAuthorize("@ss.hasPermi('zhengheiot:iotdata:query')")
    @GetMapping(value = "/{key}")
    public AjaxResult getInfo(@PathVariable("key") String key) {
        return success(iotRealDataService.selectIotRealDataByKey(key));
    }

    /**
     * 新增IoT实时数据
     */
    @PreAuthorize("@ss.hasPermi('zhengheiot:iotdata:add')")
    @Log(title = "IoT实时数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IotRealData iotRealData) {
        return toAjax(iotRealDataService.insertIotRealData(iotRealData));
    }

    /**
     * 修改IoT实时数据
     */
    @PreAuthorize("@ss.hasPermi('zhengheiot:iotdata:edit')")
    @Log(title = "IoT实时数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IotRealData iotRealData) {
        return toAjax(iotRealDataService.updateIotRealData(iotRealData));
    }

    /**
     * 删除IoT实时数据
     */
    @PreAuthorize("@ss.hasPermi('zhengheiot:iotdata:remove')")
    @Log(title = "IoT实时数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{keys}")
    public AjaxResult remove(@PathVariable String[] keys) {
        return toAjax(iotRealDataService.deleteIotRealDataByKeys(keys));
    }


    /**
     * iot实时数据查询
     * 开放给第三方调用
     * @return
     * @param iotRealData
     */
    @ApiOperation("iot实时数据查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deviceCode", value = "设备编码", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "tag", value = "属性编码", required = false, paramType = "query", dataType = "String")
    })
    @GetMapping("/realdata/list")
    public TableDataInfo realDataList(IotRealData iotRealData) {

        startPage();
        final List<IotRealData> iotRealData1 = iotRealDataService.selectIotRealDataList(iotRealData);
        return getDataTable(iotRealData1);

    }

    /**
     * 校验id参数是否合法
     *
     * @param id 待校验的id
     * @throws IllegalArgumentException 如果id为空或不符合业务逻辑
     */
    private void validateIdParameter(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        // 如果有其他业务逻辑校验，可以在此扩展
    }
}