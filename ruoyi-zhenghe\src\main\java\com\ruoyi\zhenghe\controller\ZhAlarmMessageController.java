package com.ruoyi.zhenghe.controller;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.zhengheiot.domain.IotRealData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.zhenghe.domain.ZhAlarmMessage;
import com.ruoyi.zhenghe.service.IZhAlarmMessageService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 告警信息Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@Api(tags = "告警信息Controller")
@RestController
@RequestMapping("/zhenghe/message")
public class ZhAlarmMessageController extends BaseController
{
    @Autowired
    private IZhAlarmMessageService zhAlarmMessageService;

    /**
     * 查询告警信息列表
     */
    @ApiOperation(value = "查询告警信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId",value = "部门id",required = false,paramType = "query",dataType = "integer"),
            @ApiImplicitParam(name = "workshopId",value = "车间id",required = false,paramType = "query",dataType = "integer"),
            @ApiImplicitParam(name = "deviceTypeId",value = "设备类型id",required = false,paramType = "query",dataType = "integer"),
            @ApiImplicitParam(name = "startTime",value = "开始时间",required = false,paramType = "query",dataType = "date"),
            @ApiImplicitParam(name = "endTime",value = "结束时间",required = false,paramType = "query",dataType = "date"),
            @ApiImplicitParam(name = "pageNum",value = "页数",required = true,paramType = "query",dataType = "integer"),
            @ApiImplicitParam(name = "pageSize",value = "条数",required = true,paramType = "query",dataType = "integer")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:message:list')")
    @GetMapping("/list")
    public TableDataInfo list(ZhAlarmMessage zhAlarmMessage)
    {
        startPage();
        List<ZhAlarmMessage> list = zhAlarmMessageService.selectZhAlarmMessageList(zhAlarmMessage);
        return getDataTable(list);
    }

    /**
     * 导出告警信息列表
     */
    @ApiOperation(value = "导出告警信息列表")
    @PreAuthorize("@ss.hasPermi('zhenghe:message:export')")
    @Log(title = "告警信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhAlarmMessage zhAlarmMessage)
    {
        List<ZhAlarmMessage> list = zhAlarmMessageService.selectZhAlarmMessageList(zhAlarmMessage);
        ExcelUtil<ZhAlarmMessage> util = new ExcelUtil<ZhAlarmMessage>(ZhAlarmMessage.class);
        util.exportExcel(response, list, "告警信息数据");
    }

    /**
     * 获取告警信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('zhenghe:message:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(zhAlarmMessageService.selectZhAlarmMessageById(id));
    }

    /**
     * 新增告警信息
     */
    @PreAuthorize("@ss.hasPermi('zhenghe:message:add')")
    @Log(title = "告警信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZhAlarmMessage zhAlarmMessage)
    {
        return toAjax(zhAlarmMessageService.insertZhAlarmMessage(zhAlarmMessage));
    }

    /**
     * 修改告警信息
     */
    @PreAuthorize("@ss.hasPermi('zhenghe:message:edit')")
    @Log(title = "告警信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhAlarmMessage zhAlarmMessage)
    {
        return toAjax(zhAlarmMessageService.updateZhAlarmMessage(zhAlarmMessage));
    }

    /**
     * 删除告警信息
     */
    @PreAuthorize("@ss.hasPermi('zhenghe:message:remove')")
    @Log(title = "告警信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(zhAlarmMessageService.deleteZhAlarmMessageByIds(ids));
    }

//    @PostMapping("/executeAlarmMessage")
//    public void executeAlarmMessage(@RequestParam("deviceCode") String deviceCode,@RequestBody List<IotRealData> tempList){
//        zhAlarmMessageService.executeAlarmMessage(deviceCode,tempList);
//    }

}
