package com.ruoyi.zhenghe.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 产品生产明细DTO
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@ApiModel(value = "ProductDetailDto", description = "产品生产明细DTO")
public class ProductDetailDto
{
    /** 设备编码 */
    @ApiModelProperty(value = "设备编码")
    private String deviceCode;

    /** 设备名称 */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    /** 所属事业部 */
    @ApiModelProperty(value = "所属事业部")
    private String businessUnit;

    /** 所属车间 */
    @ApiModelProperty(value = "所属车间")
    private String workshopName;

    /** 生产时间 */
    @ApiModelProperty(value = "生产时间")
    private String productionTime;

    /** 生产产品计数 */
    @ApiModelProperty(value = "生产产品计数")
    private Integer productionCount;

    /** 生产开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生产开始时间")
    private Date startTime;

    /** 生产结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生产结束时间")
    private Date endTime;
}
