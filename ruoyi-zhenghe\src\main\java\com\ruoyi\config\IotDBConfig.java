package com.ruoyi.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.iotdb.isession.util.Version;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.apache.iotdb.session.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

@Configuration
@Slf4j
public class IotDBConfig {

    private final String ip;
    private final int port;
    private final String user;
    private final String password;
    private final int fetchSize;

    private Session session;

    @Autowired
    public IotDBConfig(
            @Value("${spring.iotdb.ip}")
            String ip,
            @Value("${spring.iotdb.port}")
            int port,
            @Value("${spring.iotdb.user}")
            String user,
            @Value("${spring.iotdb.password}")
            String password,
            @Value("${spring.iotdb.fetchSize}")
            int fetchSize) {
        this.ip = ip;
        this.port = port;
        this.user = user;
        this.password = password;
        this.fetchSize = fetchSize;
    }

    @PostConstruct
    private void initialize() {
        if (!validateConfig()) {
            log.error("IoTDB 配置不完整，无法初始化");
            return;
        }
        try {
            session = createSession();
            log.info("IoTDB 连接成功，设置时区为 +08:00");
        } catch (IoTDBConnectionException | StatementExecutionException e) {
            log.error("IoTDB 连接失败: {}", e.getMessage(), e);
            log.error("详细异常信息: ", e); // 增加详细异常打印
        }
    }

    @PreDestroy
    private void cleanup() {
        if (session != null && session.isEnableRedirection()) {
            try {
                session.close();
                log.info("IoTDB 连接已关闭");
            } catch (IoTDBConnectionException e) {
                log.error("关闭 IoTDB 连接时出错: {}", e.getMessage(), e);
            }
        }
    }

    @Bean
    public Session getSession() {
        if (session == null) {
            log.warn("IoTDB 会话尚未初始化，尝试重新初始化...");
            try {
                session = createSession();
                log.info("IoTDB 连接成功，设置时区为 +08:00");
            } catch (Exception e) {
                log.error("IoTDB 会话初始化失败: {}", e.getMessage(), e);
                throw new IllegalStateException("IoTDB 会话初始化失败", e);
            }
        }
        return session;
    }

    private Session createSession() throws IoTDBConnectionException, StatementExecutionException {
        Session.Builder builder = new Session.Builder()
                .host(ip)
                .port(port)
                .username(user)
                .password(password)
                .version(Version.V_0_13);

        Session session = builder.build();
        session.open(false);
        session.setFetchSize(fetchSize);
        session.setTimeZone("+08:00");
        return session;
    }

    private boolean validateConfig() {
        if (ip == null || ip.isEmpty() || port <= 0 || user == null || user.isEmpty() || password == null || password.isEmpty()) {
            log.error("IoTDB 配置缺失: ip={}, port={}, user={}, password={}", ip, port, user, "******");
            return false;
        }
        return true;
    }
}
