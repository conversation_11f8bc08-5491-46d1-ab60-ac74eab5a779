package com.ruoyi.zhengheiot.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.ConstantZH;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.util.iotdb.IoTDBUtil;
import com.ruoyi.zhenghe.domain.ZhDeviceType;
import com.ruoyi.zhenghe.domain.ZhDeviceTypeAttr;
import com.ruoyi.zhenghe.domain.ZhEquipmentProp;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.mapper.ZhDeviceTypeAttrMapper;
import com.ruoyi.zhenghe.mapper.ZhDeviceTypeMapper;
import com.ruoyi.zhenghe.mapper.ZhEquipmentPropMapper;
import com.ruoyi.zhenghe.mapper.ZhIotEquipmentMapper;
import com.ruoyi.zhengheiot.domain.DepartmentEquipmentTypeEquipmentVo;
import com.ruoyi.zhengheiot.domain.EquipmentDetail;
import com.ruoyi.zhengheiot.domain.EquipmentTypeEquipmentVo;
import com.ruoyi.zhengheiot.domain.IotRealData;
import com.ruoyi.zhengheiot.mapper.IotRealDataMapper;
import com.ruoyi.zhengheiot.service.IIotRealDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * IoT实时数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Slf4j
@Service
public class IotRealDataServiceImpl implements IIotRealDataService {
    @Autowired
    private IotRealDataMapper iotRealDataMapper;

    @Resource
    private ZhDeviceTypeMapper zhDeviceTypeMapper;

    @Resource
    private ZhIotEquipmentMapper equipmentMapper;

    @Resource
    private ZhEquipmentPropMapper equipmentPropMapper;

    @Resource
    private ZhDeviceTypeAttrMapper typeAttrMapper;

    @Resource
    private SysDeptMapper deptMapper;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private IoTDBUtil ioTDBUtil;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询IoT实时数据
     *
     * @param key IoT实时数据主键
     * @return IoT实时数据
     */
    @Override
    public IotRealData selectIotRealDataByKey(String key) {
        return iotRealDataMapper.selectIotRealDataByKey(key);
    }

    /**
     * 查询IoT实时数据列表
     *
     * @param iotRealData IoT实时数据
     * @return IoT实时数据
     */
    @Override
    public List<IotRealData> selectIotRealDataList(IotRealData iotRealData) {
        return iotRealDataMapper.selectIotRealDataList(iotRealData);
    }

    /**
     * 新增IoT实时数据
     *
     * @param iotRealData IoT实时数据
     * @return 结果
     */
    @Override
    public int insertIotRealData(IotRealData iotRealData) {
        return iotRealDataMapper.insertIotRealData(iotRealData);
    }

    /**
     * 修改IoT实时数据
     *
     * @param iotRealData IoT实时数据
     * @return 结果
     */
    @Override
    public int updateIotRealData(IotRealData iotRealData) {
        iotRealData.setUpdateTime(DateUtils.getNowDate());
        return iotRealDataMapper.updateIotRealData(iotRealData);
    }

    /**
     * 批量删除IoT实时数据
     *
     * @param keys 需要删除的IoT实时数据主键
     * @return 结果
     */
    @Override
    public int deleteIotRealDataByKeys(String[] keys) {
        return iotRealDataMapper.deleteIotRealDataByKeys(keys);
    }

    /**
     * 删除IoT实时数据信息
     *
     * @param key IoT实时数据主键
     * @return 结果
     */
    @Override
    public int deleteIotRealDataByKey(String key) {
        return iotRealDataMapper.deleteIotRealDataByKey(key);
    }

    @Override
    @DataScope(deptAlias = "d", permission = "zhengheiot:iotdata:list")
    public List<DepartmentEquipmentTypeEquipmentVo> getEquipmentTypeEquipmentVoList(ZhIotEquipment equipment) {
        List<DepartmentEquipmentTypeEquipmentVo> ans = new ArrayList<>();
        SysDept queryDept = new SysDept();
        // 不设置部门ID，让数据权限控制返回哪些部门

        final List<SysDept> sysDepts = deptService.selectDeptList(queryDept);

        if (sysDepts != null) {
            for (SysDept sysDept : sysDepts) {
                if (sysDept.getParentId() == 0L || sysDept.getParentId() == 100L) {
                    continue;
                }
                DepartmentEquipmentTypeEquipmentVo one = new DepartmentEquipmentTypeEquipmentVo();
                one.setDepartmentName(sysDept.getDeptName());

            List<EquipmentTypeEquipmentVo> typeEquipmentVoList = new ArrayList<>();
            ZhDeviceType queryDeviceType = new ZhDeviceType();
            queryDeviceType.setDeptId(sysDept.getDeptId());
            final List<ZhDeviceType> zhDeviceTypes = zhDeviceTypeMapper.selectZhDeviceTypeList(queryDeviceType);

            for (ZhDeviceType zhDeviceType : zhDeviceTypes) {
                EquipmentTypeEquipmentVo typeEquipmentVo = new EquipmentTypeEquipmentVo();
                BeanUtil.copyProperties(zhDeviceType, typeEquipmentVo);

                ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
                zhIotEquipment.setEquipmentName(equipment.getEquipmentName());
                zhIotEquipment.setDeviceTypeId(zhDeviceType.getId());
                final List<ZhIotEquipment> zhIotEquipments = equipmentMapper.selectZhIotEquipmentList(zhIotEquipment);
                typeEquipmentVo.setChildren(zhIotEquipments);
                typeEquipmentVoList.add(typeEquipmentVo);
            }
                one.setChildren(typeEquipmentVoList);
                ans.add(one);
            }
        }

        return ans;
    }

    public List<DepartmentEquipmentTypeEquipmentVo> getEquipmentTypeEquipmentVoList1(String equipmentName) {

        List<DepartmentEquipmentTypeEquipmentVo> ans = new ArrayList<>();

        final List<SysDept> sysDepts = deptMapper.selectDeptList(new SysDept());

        for (SysDept sysDept : sysDepts) {
            DepartmentEquipmentTypeEquipmentVo one = new DepartmentEquipmentTypeEquipmentVo();
            one.setDepartmentName(sysDept.getDeptName());

            final List<ZhDeviceType> zhDeviceTypes = zhDeviceTypeMapper.selectZhDeviceTypeList(new ZhDeviceType());

            for (ZhDeviceType zhDeviceType : zhDeviceTypes) {
                BeanUtil.copyProperties(zhDeviceType, one);
                EquipmentTypeEquipmentVo typeEquipmentVo = new EquipmentTypeEquipmentVo();

                ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
                zhIotEquipment.setEquipmentName(equipmentName);
                zhIotEquipment.setDeviceTypeId(zhDeviceType.getId());
                final List<ZhIotEquipment> zhIotEquipments = equipmentMapper.selectZhIotEquipmentList(zhIotEquipment);
                typeEquipmentVo.setChildren(zhIotEquipments);
                ans.add(one);
            }
        }
        return ans;
    }


    @Override
    public EquipmentDetail getRealDataList(Long equipmentId) {

        String cashKey = "realDataList:" + equipmentId;
        Object cashValue = redisCache.getCacheObject(cashKey);
        if (cashValue != null) {
            return (EquipmentDetail) cashValue;
        }
        // 参数校验，避免传入无效的 equipmentId
        if (equipmentId == null || equipmentId <= 0) {
            throw new IllegalArgumentException("设备ID不能为空或小于等于0");
        }

        EquipmentDetail detail = null;
        try {
            // 查询设备详情
            detail = equipmentMapper.selectZhIotEquipmentDetailById(equipmentId);
        } catch (Exception e) {
            // 捕获异常并记录日志，避免程序崩溃
            log.error("查询设备详情失败，设备ID: {}", equipmentId, e);
            // 根据业务需求决定是否抛出异常或返回默认值
            throw new RuntimeException("查询设备详情失败", e);
        }

        // 如果查询结果为空，返回一个默认值或抛出异常
        if (detail == null) {
            log.warn("未找到设备详情，设备ID: {}", equipmentId);
            return null; // 或者抛出自定义异常，视业务需求而定
        }

        detail.setOnLine(false);
        try {
            final List<ZhEquipmentProp> attrList = detail.getAttrList();
            // 判断设备是否在线
            for (ZhEquipmentProp attr : attrList) {
                if (detail.isOnLine()) {

                } else {
                    if (attr.getLastUpdateTime() != null &&
                            DateUtil.between(DateUtil.date(), DateUtil.parse(attr.getLastUpdateTime()), DateUnit.SECOND) < 180) {
                        detail.setOnLine(true);
//                    break;
                    }
                }

                // 解析枚举数据
                if (StringUtils.isNotEmpty(attr.getEnumList())) {
                    try {
                        JSONObject jsonObject = JSONUtil.parseObj(attr.getEnumList());
                        if (jsonObject.get(attr.getLastVal()) != null) {
                            attr.setLastVal(jsonObject.get(attr.getLastVal()).toString());
                        }
                    } catch (Exception e) {
                        log.error("解析枚举数据失败"+attr.getEnumList());
                    }
                }
                // 判断是否是报警属性，如果是，则判断当前值是否等于报警值，如果是，则将报警值置为1，否则置为0
                if (StringUtils.isNotEmpty(attr.getAttrCode())) {
                    if (attr.getAttrCode().startsWith("Alarm")) {
                        attr.setAlarm("0");
                        if (StringUtils.isNotEmpty(attr.getLastVal())) {
                            if (attr.getLastVal().equals(attr.getFaultVal())) {
                                attr.setAlarm("1");
                                attr.setLastVal(ConstantZH.FAULT);
                            } else {
                                attr.setLastVal(ConstantZH.NORMAL);
                            }
                        }
                    }
                }
                if (attr != null && attr.getLastVal() != null) {
                    final ZhDeviceTypeAttr attr1 = typeAttrMapper.selectZhDeviceTypeAttrById(attr.getAttrId());
                    // 检查 attr1 是否为 null
                    if (attr1 != null && StringUtils.isNotEmpty(attr1.getAttrType()) && StringUtils.isNotEmpty(attr1.getAttrMultiple().toString())) {
                        String attrType = attr1.getAttrType().toLowerCase();

                        // 检查 attrType 是否为数字类型
                        if ("double".equals(attrType) || "int".equals(attrType) || "integer".equals(attrType)) {
                            try {
                                // 确保 lastVal 和 attrMultiple 能正确转换为 double
                                Double lastValAsDouble = Convert.toDouble(attr.getLastVal(), 0.0);
                                Double attrMultipleAsDouble = Convert.toDouble(attr1.getAttrMultiple(), 1.0);

                                if (lastValAsDouble != null && attrMultipleAsDouble != null) {
                                    attr.setLastVal(NumberUtil.round(lastValAsDouble * attrMultipleAsDouble, 3).toString());
                                }
                            } catch (NumberFormatException e) {
                                // 记录异常日志，避免程序崩溃
                                log.error("数据转换失败：{}", e.getMessage(), e);
                            }
                        }
                    }
                    if(attr1 != null&& attr1.getAttrUnit()!=null){
                       attr.setAttrUnit(attr1.getAttrUnit());
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        redisCache.setCacheObject(cashKey, detail, ConstantZH.CASH_RRALDATA_LIST, TimeUnit.SECONDS);
        return detail;
    }


    @Override
    public JSONObject realDetail(Long equipmentId) {
        JSONObject ans = new JSONObject();
        if (equipmentId != null) {
            final String beginToday = DateUtil.format(DateUtil.beginOfDay(new Date()), "yyyy-MM-dd HH:mm:ss");
            final String beginYear = DateUtil.format(DateUtil.beginOfYear(new Date()), "yyyy-MM-dd HH:mm:ss");
            final String end = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
            String code = "Power_ON";
            double workTimeToday = 0; //当班运行时间  今日工作时间
            double workTimeAll = 0;//累计运行时间  年工作时间
            double offTimeToday = 0; //当班关机时间  今日关机时间
            double offTimeAll = 0;//累计关机时间  今年关机时间
            double standbyTimeToday = 0; //当班待机时间  今日待机时间
            double standbyTimeAll = 0;//累计待机时间  年待机时间
            int stopCountToday = 0;//当班停机次数  今日停机次数
            int stopCountAll = 0;//累计停机次数 今年停机次数

            final EquipmentDetail equipment = equipmentMapper.selectZhIotEquipmentDetailById(equipmentId);
            if (equipment != null) {
                try {
                    workTimeToday = ioTDBUtil.queryIotEquipmentProductionDuration(Constants.TENANT, equipment.getEquipmentCode(), code, beginToday, end);
                    workTimeAll = ioTDBUtil.queryIotEquipmentProductionDuration(Constants.TENANT, equipment.getEquipmentCode(), code, beginYear, end);
                } catch (IoTDBConnectionException e) {
                    throw new RuntimeException(e);
                } catch (StatementExecutionException e) {
                    throw new RuntimeException(e);
                }

                offTimeToday = DateUtil.between(DateUtil.beginOfDay(new Date()), DateUtil.parse(end), DateUnit.MINUTE) - workTimeToday;
                offTimeAll = DateUtil.between(DateUtil.beginOfYear(new Date()), DateUtil.parse(end), DateUnit.MINUTE) - workTimeAll;

                ans.put("workTimeToday", workTimeToday);
                ans.put("workTimeAll", workTimeAll);
                ans.put("offTimeToday", offTimeToday);
                ans.put("offTimeAll", offTimeAll);
                ans.put("standbyTimeToday", "-");
                ans.put("standbyTimeAll", "-");
                ans.put("stopCountToday", stopCountToday);
                ans.put("stopCountAll", stopCountAll);


            }
        }
        return ans;
    }

}
