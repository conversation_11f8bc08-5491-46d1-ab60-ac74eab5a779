package com.ruoyi.screen;


import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.screen.entity.ScreenEquipment;
import com.ruoyi.screen.entity.WDLErrorInfo;
import com.ruoyi.screen.util.PerformanceMonitor;
import com.ruoyi.util.iotdb.IoTDBUtil;
import com.ruoyi.zhenghe.domain.ZhDeviceTypeAttr;
import com.ruoyi.zhenghe.domain.ZhEquipmentProp;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.domain.ZhWorkshop;
import com.ruoyi.zhenghe.mapper.ZhDeviceTypeAttrMapper;
import com.ruoyi.zhenghe.mapper.ZhWorkshopMapper;
import com.ruoyi.zhenghe.service.IZhIotEquipmentService;
import com.ruoyi.zhengheiot.domain.EquipmentDetail;
import com.ruoyi.zhengheiot.service.IIotRealDataService;
import com.ruoyi.zhengheiot.service.IotHistoryDataService;
import com.ruoyi.screen.service.MultiLevelCacheService;
import com.ruoyi.screen.service.AsyncComputeService;
import com.ruoyi.screen.service.FallbackService;
import com.ruoyi.screen.service.OptimizedIoTDBService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "链轮事业部大屏")
@RestController
@RequestMapping("/screen/data/lianlun")
public class LianLunDeptController {

    @Autowired
    private IZhIotEquipmentService zhIotEquipmentService;
    @Autowired
    private IIotRealDataService realDataService;
    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IoTDBUtil ioTDBUtil;

    @Resource
    private ZhDeviceTypeAttrMapper zhDeviceTypeAttrMapper;
    @Resource
    private IotHistoryDataService historyDataService;

    @Autowired
    private ObjectMapper objectMapper; // Jackson 的 ObjectMapper

    final int REDIS_CACHE_EXPIRATION_SECONDS = 300;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ZhWorkshopMapper zhWorkshopMapper;

    @Autowired
    private PerformanceMonitor performanceMonitor;

    @Autowired
    private MultiLevelCacheService multiLevelCacheService;

    @Autowired
    private AsyncComputeService asyncComputeService;

    @Autowired
    private FallbackService fallbackService;

    @Autowired
    private OptimizedIoTDBService optimizedIoTDBService;

    // 线程池配置 - 用于并发查询优化
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * 应用关闭时优雅关闭线程池
     */
    @PreDestroy
    public void destroy() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    @ApiOperation(value = "设备数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "部门id", required = false, paramType = "query", dataType = "string")
            , @ApiImplicitParam(name = "workshopId", value = "车间id", required = false, paramType = "query", dataType = "long")
    })
    @GetMapping("/device/info")
    public AjaxResult deviceInfo(@RequestParam(name = "deptId", required = false) String deptId, @RequestParam(name = "workshopId", required = false) Long workshopId) {
        long startTime = performanceMonitor.startTiming("deviceInfo");
        if (deptId == null && workshopId != null) {
            return AjaxResult.error("请输入部门id");
        }
        // 构建缓存键，避免 deptId 为 null 时的冲突
        String cacheKey = "deviceInfo:" + (deptId == null ? "all" : deptId) + ":" + (workshopId == null ? "all" : workshopId);

        // 检查缓存是否存在
        if (redisCache.hasKey(cacheKey)) {
            return AjaxResult.success(JSONObject.parseObject(redisCache.getCacheObject(cacheKey).toString()));
        }

        try {
            JSONObject ans = new JSONObject();
            int totalCount = 0;   // 设备总数
            AtomicInteger onlineCount = new AtomicInteger(0); // 在线设备数
            AtomicInteger offlineCount = new AtomicInteger(0); // 离线设备数
            AtomicInteger errorCount = new AtomicInteger(0);// 报警设备数

            List<String> totalDeviceList = new ArrayList<>();
            List<String> onlineDeviceList = Collections.synchronizedList(new ArrayList<>());
            List<String> offlineDeviceList = Collections.synchronizedList(new ArrayList<>());
            List<String> errorDeviceList = Collections.synchronizedList(new ArrayList<>());

            ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
            if (deptId != null) {
                zhIotEquipment.setDeptId(Long.valueOf(deptId));
            }
            if (workshopId != null) {
                zhIotEquipment.setWorkshopId(workshopId);
            }

            // 获取设备列表
            List<ZhIotEquipment> list = zhIotEquipmentService.selectZhIotEquipmentListNoScope(zhIotEquipment);
            if (list == null || list.isEmpty()) {
                return AjaxResult.success(null);
            }
            //去掉电表设备
            list = list.stream()
                    .filter(equipment -> !equipment.getEquipmentName().contains("电表"))
                    .collect(Collectors.toList());

            totalCount = list.size();


            // 使用并发查询优化设备状态统计
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (ZhIotEquipment equipment : list) {
                if (equipment == null) {
                    continue; // 跳过空设备
                }
                totalDeviceList.add(equipment.getEquipmentName() + "-" + equipment.getEquipmentCode());

                // 异步查询设备实时数据
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        EquipmentDetail realDataList = realDataService.getRealDataList(equipment.getId());
                        String deviceName = equipment.getEquipmentName() + "-" + equipment.getEquipmentCode();

                        if (realDataList == null) {
                            offlineCount.incrementAndGet();
                            offlineDeviceList.add(deviceName);
                            return;
                        }

                        if (realDataList.isOnLine()) {
                            onlineCount.incrementAndGet();
                            onlineDeviceList.add(deviceName);
                        } else {
                            offlineCount.incrementAndGet();
                            offlineDeviceList.add(deviceName);
                        }

                        // 检查报警状态
                        if (realDataList.getAttrList() != null && realDataList.isOnLine()) {
                            for (ZhEquipmentProp attr : realDataList.getAttrList()) {
                                if (attr.getAttrCode() != null) {
                                    if (attr.getAttrCode().startsWith("Alarm") && "1".equals(attr.getAlarm())) {
                                        errorCount.incrementAndGet();
                                        errorDeviceList.add(deviceName);
                                        break;
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("查询设备 {} 实时数据失败", equipment.getEquipmentName(), e);
                    }
                }, executorService);

                futures.add(future);
            }

            // 等待所有异步任务完成，设置超时时间避免长时间等待
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .get(30, TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                log.warn("设备状态查询超时，部分数据可能不完整");
            } catch (Exception e) {
                log.error("设备状态查询异常", e);
            }
            int onlineCountValue = onlineCount.get();
            int offlineCountValue = offlineCount.get();
            int errorCountValue = errorCount.get();

            ans.put("totalCount", totalCount);
//            ans.put("onlineCount", onlineCountValue);
            ans.put("onlineCount", 0);
            ans.put("offlineCount", offlineCountValue);
            ans.put("workCount", onlineCountValue); //Todo 工作状态未实现，在线状态就是工作状态。
            ans.put("errorCount", errorCountValue);

            ans.put("onlineRate", String.format("%.3f", (onlineCountValue * 100.0) / totalCount));
            ans.put("useRate", String.format("%.3f", onlineCountValue > 0 ? (onlineCountValue * 100.0) / onlineCountValue : 0.0));
            ans.put("errorRate", String.format("%.3f", (errorCountValue * 100.0) / totalCount));


            ans.put("totalDeviceList", totalDeviceList);
            ans.put("onlineDeviceList", null); //先写死，待机暂时无法判断
            ans.put("offlineDeviceList", offlineDeviceList);
            ans.put("errorDeviceList", errorDeviceList);
            ans.put("workDeviceList", onlineDeviceList);// Todo 工作状态未实现，在线状态就是工作状态。

            // 写入缓存
            redisCache.setCacheObject(cacheKey, ans.toJSONString(), REDIS_CACHE_EXPIRATION_SECONDS, TimeUnit.SECONDS);

            return AjaxResult.success(ans);

        } catch (Exception e) {
            // 捕获异常并返回友好错误信息
            log.error("设备数据查询失败", e);
            return AjaxResult.error("系统异常，请稍后再试");
        } finally {
            performanceMonitor.endTiming("deviceInfo", startTime);
        }
    }


    @ApiOperation(value = "设备分析")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "部门id", required = false, paramType = "query", dataType = "string")
            , @ApiImplicitParam(name = "workshopId", value = "车间id", required = false, paramType = "query", dataType = "long")

    })
    @GetMapping("/device/detail")
    public AjaxResult deviceDetail(@RequestParam(name = "deptId", required = false) String deptId, @RequestParam(name = "workshopId", required = false) Long workshopId) {
        if (deptId == null && workshopId != null) {
            return AjaxResult.error("请输入部门id");
        }
        // 构建缓存键，避免 deptId 为 null 时的冲突
        String cacheKey = "deviceDetail:" + (deptId == null ? "all" : deptId) + ":" + (workshopId == null ? "all" : workshopId);

        // 检查缓存是否存在
        if (redisCache.hasKey(cacheKey)) {
            final String string = redisCache.getCacheObject(cacheKey).toString();
            return AjaxResult.success(com.alibaba.fastjson2.JSONArray.parseArray(string));
        }

        List<ScreenEquipment> ansList = new ArrayList<>();
        ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
        if (deptId != null) {
            zhIotEquipment.setDeptId(Long.valueOf(deptId));
        }
        if (workshopId != null) {
            zhIotEquipment.setWorkshopId(workshopId);
        }

        // 获取设备列表
        List<ZhIotEquipment> list = zhIotEquipmentService.selectZhIotEquipmentListNoScope(zhIotEquipment);
//        List<ZhIotEquipment> list = zhIotEquipmentService.selectZhIotEquipmentList(zhIotEquipment);
        if (list == null || list.isEmpty()) {
            return AjaxResult.success(null);
        }

        final String end = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        final String begin = DateUtil.format(DateUtil.beginOfDay(new Date()), "yyyy-MM-dd HH:mm:ss");

        // 使用并发查询优化设备详情获取
        List<CompletableFuture<ScreenEquipment>> futures = new ArrayList<>();

        for (ZhIotEquipment equipment : list) {
            if (equipment.getEquipmentName().contains("电表")) {
                continue;
            }

            CompletableFuture<ScreenEquipment> future = CompletableFuture.supplyAsync(() -> {
                ScreenEquipment screenEquipment = new ScreenEquipment();
                screenEquipment.setEquipmentName(equipment.getEquipmentName());
                screenEquipment.setEquipmentCode(equipment.getEquipmentCode());
                screenEquipment.setEquipmentImage(equipment.getEquipmentImg());

                String code = "Power_ON";
                double count = 0;
                double operTime = 0;

                try {
                    ZhDeviceTypeAttr zhDeviceTypeAttr = new ZhDeviceTypeAttr();
                    zhDeviceTypeAttr.setTslId(equipment.getDeviceTypeId());
                    zhDeviceTypeAttr.setAttrCode("Status");
                    List<ZhDeviceTypeAttr> zhDeviceTypeAttrList = zhDeviceTypeAttrMapper.selectZhDeviceTypeAttrList(zhDeviceTypeAttr);
                    if (ObjectUtil.isNotNull(zhDeviceTypeAttrList) && zhDeviceTypeAttrList.size() > 0) {
                        code = zhDeviceTypeAttrList.get(0).getAttrCode();
                    }

                    //读取设备生产计数
                    count = ioTDBUtil.queryIotEquipmentProductionCount(Constants.TENANT, equipment.getEquipmentCode(), null, begin, end);
                    //读取设备生产计加时长
                    operTime = ioTDBUtil.queryIotEquipmentProductionDuration(Constants.TENANT, equipment.getEquipmentCode(), code, begin, end);
                } catch (Exception e) {
                    log.error("设备 {} 数据查询失败", equipment.getEquipmentName(), e);
                }

                screenEquipment.setWorkRate(String.format("%.3f", 100.0 * operTime / DateUtil.between(DateUtil.beginOfDay(new Date()), new Date(), DateUnit.MINUTE)));
                screenEquipment.setWorkTime(String.format("%.1f", operTime));
                screenEquipment.setWorkCount("" + count);

                return screenEquipment;
            }, executorService);

            futures.add(future);
        }

        // 等待所有异步任务完成并收集结果
        try {
            List<ScreenEquipment> results = futures.stream()
                    .map(future -> {
                        try {
                            return future.get(30, TimeUnit.SECONDS);
                        } catch (Exception e) {
                            log.error("获取设备详情超时", e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            ansList.addAll(results);
        } catch (Exception e) {
            log.error("设备详情查询异常", e);
        }

        redisCache.setCacheObject(cacheKey, ansList, REDIS_CACHE_EXPIRATION_SECONDS, TimeUnit.SECONDS);
        return AjaxResult.success(ansList);
    }


    @ApiOperation(value = "产量信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "部门id", required = false, paramType = "query", dataType = "string")
            , @ApiImplicitParam(name = "workshopId", value = "车间id", required = false, paramType = "query", dataType = "long")

    })
    @GetMapping("/yield")
    public AjaxResult yield(@RequestParam(name = "deptId", required = false) String deptId, @RequestParam(name = "workshopId", required = false) Long workshopId) {
        String interfaceName = "yield";
        long startTime = performanceMonitor.startTiming(interfaceName);

        try {
            if (deptId == null && workshopId != null) {
                return AjaxResult.error("请输入部门id");
            }

            // 检查熔断器状态
            if (fallbackService.shouldCircuitBreak(interfaceName)) {
                log.warn("产量信息接口熔断，返回降级数据");
                JSONObject fallbackData = fallbackService.getYieldFallback(deptId, workshopId == null ? null : workshopId.toString());
                return AjaxResult.success(fallbackData);
            }

            // 检查预计算结果缓存
            JSONObject precomputedResult = multiLevelCacheService.getPrecomputedResult("yield", deptId, workshopId == null ? null : workshopId.toString(), "7days");
            if (precomputedResult != null) {
                fallbackService.recordSuccess(interfaceName);
                return AjaxResult.success(precomputedResult);
            }

            // 构建缓存键，避免 deptId 为 null 时的冲突
            String cacheKey = "yield:" + (deptId == null ? "all" : deptId) + ":" + (workshopId == null ? "all" : workshopId);

            // 检查传统缓存是否存在
            if (redisCache.hasKey(cacheKey)) {
                fallbackService.recordSuccess(interfaceName);
                return AjaxResult.success(JSONObject.parseObject(redisCache.getCacheObject(cacheKey).toString()));
            }

            // 触发异步计算
            asyncComputeService.asyncComputeYieldData(deptId, workshopId == null ? null : workshopId.toString());

            // 尝试快速计算，如果超时则返回降级数据
            JSONObject ans = new JSONObject();
        ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
        if (deptId != null) {
            zhIotEquipment.setDeptId(Long.valueOf(deptId));
        }
        if (workshopId != null) {
            zhIotEquipment.setWorkshopId(workshopId);
        }

        // 获取设备列表
        List<ZhIotEquipment> equipmentList = zhIotEquipmentService.selectZhIotEquipmentListNoScope(zhIotEquipment);
        if (equipmentList == null || equipmentList.isEmpty()) {
            return AjaxResult.success(null);
        }

        List<String> dateList = new ArrayList<>();
        List<Integer> yileList = new ArrayList<>();

        final DateTime date = DateUtil.date();

        // 使用并发查询优化产量统计
        List<CompletableFuture<Integer>> dayFutures = new ArrayList<>();

        for (int i = 6; i >= 0; i--) {
            DateTime begin = DateUtil.beginOfDay(DateUtil.offsetDay(date, -i));
            DateTime end = DateUtil.endOfDay(begin);
            final String format = DateUtil.format(begin, "MM-dd");
            dateList.add(format);

            CompletableFuture<Integer> dayFuture = CompletableFuture.supplyAsync(() -> {
                AtomicInteger totalCount = new AtomicInteger(0);
                List<CompletableFuture<Void>> equipmentFutures = new ArrayList<>();

                for (ZhIotEquipment equipment : equipmentList) {
                    CompletableFuture<Void> equipmentFuture = CompletableFuture.runAsync(() -> {
                        try {
                            int count = ioTDBUtil.queryIotEquipmentProductionCount(Constants.TENANT, equipment.getEquipmentCode(),
                                    null, DateUtil.format(begin, "yyyy-MM-dd HH:mm:ss"), DateUtil.format(end, "yyyy-MM-dd HH:mm:ss"));
                            totalCount.addAndGet(count);
                        } catch (Exception e) {
                            log.error("查询设备 {} 产量失败", equipment.getEquipmentName(), e);
                        }
                    }, executorService);
                    equipmentFutures.add(equipmentFuture);
                }

                try {
                    CompletableFuture.allOf(equipmentFutures.toArray(new CompletableFuture[0]))
                            .get(30, TimeUnit.SECONDS);
                } catch (Exception e) {
                    log.error("设备产量查询超时", e);
                }

                return totalCount.get();
            }, executorService);

            dayFutures.add(dayFuture);
        }

        // 等待所有日期的查询完成
        try {
            for (CompletableFuture<Integer> future : dayFutures) {
                yileList.add(future.get(60, TimeUnit.SECONDS));
            }
        } catch (Exception e) {
            log.error("产量统计查询异常", e);
            // 如果查询失败，填充默认值
            while (yileList.size() < dateList.size()) {
                yileList.add(0);
            }
        }
            ans.put("dateList", dateList);
            ans.put("yileList", yileList);
            redisCache.setCacheObject(cacheKey, ans.toJSONString(), REDIS_CACHE_EXPIRATION_SECONDS, TimeUnit.SECONDS);
            fallbackService.recordSuccess(interfaceName);
            return AjaxResult.success(ans);

        } catch (Exception e) {
            log.error("产量统计查询失败", e);
            fallbackService.recordFailure(interfaceName);
            // 返回降级数据
            JSONObject fallbackData = fallbackService.getYieldFallback(deptId, workshopId == null ? null : workshopId.toString());
            return AjaxResult.success(fallbackData);
        } finally {
            performanceMonitor.endTiming(interfaceName, startTime);
        }
    }

    @ApiOperation(value = "产量信息按小时")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "部门id", required = false, paramType = "query", dataType = "string")
            , @ApiImplicitParam(name = "workshopId", value = "车间id", required = false, paramType = "query", dataType = "long")

    })
    @GetMapping("/yield/hour")
    public AjaxResult yieldHour(@RequestParam(name = "deptId", required = false) String deptId, @RequestParam(name = "workshopId", required = false) Long workshopId) {
        if (deptId == null && workshopId != null) {
            return AjaxResult.error("请输入部门id");
        }

        // 构建缓存键，避免 deptId 为 null 时的冲突
        String cacheKey = "yieldHour:" + (deptId == null ? "all" : deptId) + ":" + (workshopId == null ? "all" : workshopId);

        // 检查缓存是否存在
        if (redisCache.hasKey(cacheKey)) {
            return AjaxResult.success(JSONObject.parseObject(redisCache.getCacheObject(cacheKey).toString()));
        }

        JSONObject ans = new JSONObject();
        ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
        if (deptId != null) {
            zhIotEquipment.setDeptId(Long.valueOf(deptId));
        }
        if (workshopId != null) {
            zhIotEquipment.setWorkshopId(workshopId);
        }

        // 获取设备列表
        List<ZhIotEquipment> equipmentList = zhIotEquipmentService.selectZhIotEquipmentListNoScope(zhIotEquipment);
        if (equipmentList == null || equipmentList.isEmpty()) {
            return AjaxResult.success(null);
        }

        List<String> dateList = new ArrayList<>();
        List<Integer> yileList = new ArrayList<>();

        final DateTime nowDate = DateUtil.date();
        DateTime begin = DateUtil.beginOfDay(nowDate);

        // 使用并发查询优化小时产量统计
        List<CompletableFuture<Integer>> hourFutures = new ArrayList<>();

        for (int i = 4; i >= 0; i--) {
            DateTime end = DateUtil.offset(nowDate, DateField.HOUR, -i);
            if (end.isBefore(begin)) {
                continue;
            }
            final String format = DateUtil.format(end, "HH") + ":00";
            dateList.add(format);

            CompletableFuture<Integer> hourFuture = CompletableFuture.supplyAsync(() -> {
                AtomicInteger totalCount = new AtomicInteger(0);
                List<CompletableFuture<Void>> equipmentFutures = new ArrayList<>();

                for (ZhIotEquipment equipment : equipmentList) {
                    CompletableFuture<Void> equipmentFuture = CompletableFuture.runAsync(() -> {
                        try {
                            int count = ioTDBUtil.queryIotEquipmentProductionCount(Constants.TENANT, equipment.getEquipmentCode(),
                                    null, DateUtil.format(begin, "yyyy-MM-dd HH:mm:ss"), DateUtil.format(end, "yyyy-MM-dd HH:mm:ss"));
                            totalCount.addAndGet(count);
                        } catch (Exception e) {
                            log.error("查询设备 {} 小时产量失败", equipment.getEquipmentName(), e);
                        }
                    }, executorService);
                    equipmentFutures.add(equipmentFuture);
                }

                try {
                    CompletableFuture.allOf(equipmentFutures.toArray(new CompletableFuture[0]))
                            .get(30, TimeUnit.SECONDS);
                } catch (Exception e) {
                    log.error("设备小时产量查询超时", e);
                }

                return totalCount.get();
            }, executorService);

            hourFutures.add(hourFuture);
        }

        // 等待所有小时的查询完成
        try {
            for (CompletableFuture<Integer> future : hourFutures) {
                yileList.add(future.get(60, TimeUnit.SECONDS));
            }
        } catch (Exception e) {
            log.error("小时产量统计查询异常", e);
            // 如果查询失败，填充默认值
            while (yileList.size() < dateList.size()) {
                yileList.add(0);
            }
        }
        ans.put("dateList", dateList);
        ans.put("yileList", yileList);
        redisCache.setCacheObject(cacheKey, ans.toJSONString(), REDIS_CACHE_EXPIRATION_SECONDS, TimeUnit.SECONDS);
        return AjaxResult.success(ans);
    }

    @ApiOperation(value = "产量信息分工序")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "部门id", required = false, paramType = "query", dataType = "string")
            , @ApiImplicitParam(name = "workshopId", value = "车间id", required = false, paramType = "query", dataType = "long")

    })
    @GetMapping("/yield/class/process")
    public AjaxResult yieldWorkshop(@RequestParam(name = "deptId", required = false) String deptId, @RequestParam(name = "workshopId", required = false) Long workshopId) {
        if (deptId == null && workshopId != null) {
            return AjaxResult.error("请输入部门id");
        }

        // 构建缓存键
        String cacheKey = "yieldProcess:" + (deptId == null ? "all" : deptId) + ":" + (workshopId == null ? "all" : workshopId);

        // 检查缓存是否存在
        if (redisCache.hasKey(cacheKey)) {
            return AjaxResult.success(JSONObject.parseObject(redisCache.getCacheObject(cacheKey).toString()));
        }

        JSONObject ans = new JSONObject();
        ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
        if (deptId != null) {
            zhIotEquipment.setDeptId(Long.valueOf(deptId));
        }
        if (workshopId != null) {
            zhIotEquipment.setWorkshopId(workshopId);
        }

        // 获取设备列表
        List<ZhIotEquipment> equipmentList = zhIotEquipmentService.selectZhIotEquipmentListNoScope(zhIotEquipment);
        if (equipmentList == null || equipmentList.isEmpty()) {
            return AjaxResult.success(null);
        }
        Map<String, Integer> processMap = new HashMap<>();
        List<String> processList = new ArrayList<>();
        List<Integer> yileList = new ArrayList<>();

        DateTime end = DateUtil.date();
        DateTime begin = DateUtil.beginOfDay(end);

        // 使用并发查询优化工序产量统计
        Map<String, AtomicInteger> processCountMap = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (ZhIotEquipment equipment : equipmentList) {
            if (equipment.getProcess() == null) {
                continue;
            }

            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    int count = ioTDBUtil.queryIotEquipmentProductionCount(Constants.TENANT, equipment.getEquipmentCode(),
                            null, DateUtil.format(begin, "yyyy-MM-dd HH:mm:ss"), DateUtil.format(end, "yyyy-MM-dd HH:mm:ss"));
                    processCountMap.computeIfAbsent(equipment.getProcess(), k -> new AtomicInteger(0))
                            .addAndGet(count);
                } catch (Exception e) {
                    log.error("查询设备 {} 工序产量失败", equipment.getEquipmentName(), e);
                }
            }, executorService);

            futures.add(future);
        }

        // 等待所有查询完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(60, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("工序产量统计查询异常", e);
        }

        // 转换结果
        for (Map.Entry<String, AtomicInteger> entry : processCountMap.entrySet()) {
            processMap.put(entry.getKey(), entry.getValue().get());
        }
        for (Map.Entry<String, Integer> entry : processMap.entrySet()) {
            processList.add(entry.getKey());
            yileList.add(entry.getValue());
        }
        ans.put("processList", processList);
        ans.put("yileList", yileList);
        redisCache.setCacheObject(cacheKey, ans.toJSONString(), REDIS_CACHE_EXPIRATION_SECONDS, TimeUnit.SECONDS);
        return AjaxResult.success(ans);
    }

    @ApiOperation(value = "能耗分析")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "部门id", required = false, paramType = "query", dataType = "string")
            , @ApiImplicitParam(name = "workshopId", value = "车间id", required = false, paramType = "query", dataType = "long")

    })
    @GetMapping("/energy")
    public AjaxResult energy(@RequestParam(name = "deptId", required = false) String deptId, @RequestParam(name = "workshopId", required = false) Long workshopId) {
        if (deptId == null && workshopId != null) {
            return AjaxResult.error("请输入部门id");
        }
        // 构建缓存键，避免 deptId 为 null 时的冲突
        String cacheKey = "energy:" + (deptId == null ? "all" : deptId);

        // 检查缓存是否存在
        if (redisCache.hasKey(cacheKey)) {
            return AjaxResult.success(JSONObject.parseObject(redisCache.getCacheObject(cacheKey).toString()));
        }

        JSONObject ans = new JSONObject();
        List<String> dateList = new ArrayList<>();
        List<Integer> energyList = new ArrayList<>();
        final DateTime date = DateUtil.date();
        String[] week = new String[]{"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        for (int i = 6; i >= 0; i--) {
            DateTime begin = DateUtil.beginOfDay(DateUtil.offsetDay(date, -i));
            DateTime end = DateUtil.endOfDay(begin);
            final int i1 = begin.dayOfWeek();
            dateList.add(week[i1 - 1]);
            energyList.add((int) ((Math.random() * 100) + (Math.random() * 100)));

        }
        ans.put("dateList", dateList);
        ans.put("energyList", energyList);

        redisCache.setCacheObject(cacheKey, ans.toJSONString(), REDIS_CACHE_EXPIRATION_SECONDS, TimeUnit.SECONDS);
        return AjaxResult.success(ans);
    }

    @ApiOperation(value = "能耗分析2")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "部门id", required = false, paramType = "query", dataType = "string")
            , @ApiImplicitParam(name = "workshopId", value = "车间id", required = false, paramType = "query", dataType = "long")

    })
    @GetMapping("/energy2")
    public AjaxResult energy2(@RequestParam(name = "deptId", required = false) String deptId, @RequestParam(name = "workshopId", required = false) Long workshopId) {
        if (deptId == null && workshopId != null) {
            return AjaxResult.error("请输入部门id");
        }
        // 构建缓存键，避免 deptId 为 null 时的冲突
        String cacheKey = "energy2:" + (deptId == null ? "all" : deptId) + ":" + (workshopId == null ? "all" : workshopId);

//         检查缓存是否存在
        if (redisCache.hasKey(cacheKey)) {
            return AjaxResult.success(JSONObject.parseObject(redisCache.getCacheObject(cacheKey).toString()));
        }

        JSONObject ans = new JSONObject();

        ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
        if (deptId != null) {
            zhIotEquipment.setDeptId(Long.valueOf(deptId));
        }
        if (workshopId != null) {
            zhIotEquipment.setWorkshopId(workshopId);
        }

        // 获取设备列表
        List<ZhIotEquipment> allEquipmentList = zhIotEquipmentService.selectZhIotEquipmentListNoScope(zhIotEquipment);
        // 过滤掉非电表设备
        final List<ZhIotEquipment> equipmentList = allEquipmentList.stream()
                .filter(equipment -> equipment.getEquipmentName() == null || equipment.getEquipmentName().contains("电表"))
                .collect(Collectors.toList());

        //车间 每日电量 map
        Map<String, List<Integer>> workshopEnergyMap = new ConcurrentHashMap<>();

        //时间的列表
        List<String> dateList = new ArrayList<>();

        final DateTime date = DateUtil.date();

        if (equipmentList == null || equipmentList.isEmpty()) {
            ans.put("dateList", dateList);
            ans.put("energySum", null);
            ans.put("workshopEnergyMap", null);
            return AjaxResult.success(ans);
        }

        // 使用并发查询优化能耗分析
        List<CompletableFuture<Map<String, Integer>>> dayFutures = new ArrayList<>();

        for (int i = 6; i >= 0; i--) {
            DateTime begin = DateUtil.beginOfDay(DateUtil.offsetDay(date, -i));
            DateTime end = DateUtil.endOfDay(begin);
            if (end.isAfter(date)) {
                end = DateUtil.date();
            }
            final String format = DateUtil.format(begin, "MM-dd");
            dateList.add(format);

            String startTime = DateUtil.format(begin, "yyyy-MM-dd HH:mm:ss");
            String endTime = DateUtil.format(end, "yyyy-MM-dd HH:mm:ss");

            CompletableFuture<Map<String, Integer>> dayFuture = CompletableFuture.supplyAsync(() -> {
                Map<String, AtomicInteger> tempMap = new ConcurrentHashMap<>();
                List<CompletableFuture<Void>> equipmentFutures = new ArrayList<>();

                for (ZhIotEquipment equipment : equipmentList) {
                    CompletableFuture<Void> equipmentFuture = CompletableFuture.runAsync(() -> {
                        try {
                            final cn.hutool.json.JSONObject totalPower = historyDataService.getHistoryDataTagList(
                                    equipment.getId(), "TotalPower", startTime, endTime, null, null);
                            final int energy = ioTDBUtil.getTotalPower(totalPower);

                            String key;
                            if (workshopId == null) {
                                key = equipment.getWorkshopName();
                            } else {
                                key = equipment.getEquipmentName();
                            }

                            if (key != null) {
                                tempMap.computeIfAbsent(key, k -> new AtomicInteger(0))
                                        .addAndGet(energy);
                            }
                        } catch (Exception e) {
                            log.error("查询设备 {} 能耗数据失败", equipment.getEquipmentName(), e);
                        }
                    }, executorService);
                    equipmentFutures.add(equipmentFuture);
                }

                try {
                    CompletableFuture.allOf(equipmentFutures.toArray(new CompletableFuture[0]))
                            .get(60, TimeUnit.SECONDS);
                } catch (Exception e) {
                    log.error("设备能耗查询超时", e);
                }

                // 转换为普通Map
                Map<String, Integer> result = new HashMap<>();
                for (Map.Entry<String, AtomicInteger> entry : tempMap.entrySet()) {
                    result.put(entry.getKey(), entry.getValue().get());
                }
                return result;
            }, executorService);

            dayFutures.add(dayFuture);
        }

        // 等待所有日期的查询完成并组装结果
        try {
            for (int i = 0; i < dayFutures.size(); i++) {
                Map<String, Integer> dayResult = dayFutures.get(i).get(120, TimeUnit.SECONDS);
                for (Map.Entry<String, Integer> entry : dayResult.entrySet()) {
                    workshopEnergyMap.computeIfAbsent(entry.getKey(), k -> new ArrayList<>())
                            .add(entry.getValue());
                }
            }
        } catch (Exception e) {
            log.error("能耗统计查询异常", e);
        }
        //总电量的
        List<Integer> energySum = new ArrayList<>();

        // 计算总电量
        for (int i = 0; i < dateList.size(); i++) {
            int tempSum = 0;
            for (Map.Entry<String, List<Integer>> entry : workshopEnergyMap.entrySet()) {
                List<Integer> energyList = entry.getValue();
                if (energyList.size() > i) {
                    tempSum += energyList.get(i);
                }
            }
            energySum.add(tempSum);
        }

        ans.put("dateList", dateList);
        ans.put("energySum", energySum);
        ans.put("workshopEnergyMap", workshopEnergyMap);

        redisCache.setCacheObject(cacheKey, ans.toJSONString(), REDIS_CACHE_EXPIRATION_SECONDS, TimeUnit.SECONDS);
        return AjaxResult.success(ans);
    }


    @GetMapping("/error/info")
    public AjaxResult getErrorInfo(@RequestParam(name = "deptId", required = false) String deptId, @RequestParam(name = "workshopId", required = false) Long workshopId) throws JsonProcessingException {

        // 初始化错误信息列表
        List<WDLErrorInfo> ans = new ArrayList<>();

        // 定义缓存键
        String cacheKey = "alarm:" + (deptId == null ? "all" : deptId) + ":" + (workshopId == null ? "all" : workshopId);

//        try {
//            String json = stringRedisTemplate.opsForValue().get(cacheKey);
//            if (json != null) {
//                JavaType type = objectMapper.getTypeFactory().constructCollectionType(List.class, WDLErrorInfo.class);
//                return AjaxResult.success(objectMapper.readValue(json, type));
//            }
//        } catch (JsonProcessingException e) {
//            throw new RuntimeException(e);
//        }


        try {
            // 创建设备对象并设置设备类型ID
            ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
            if (deptId != null) {
                zhIotEquipment.setDeptId(Long.valueOf(deptId));
            }
            if (workshopId != null) {
                zhIotEquipment.setWorkshopId(workshopId);
            }

            // 获取设备列表
            List<ZhIotEquipment> equipmentList = zhIotEquipmentService.selectZhIotEquipmentListNoScope(zhIotEquipment);

            // 如果设备列表为空，直接返回成功响应
            if (equipmentList == null || equipmentList.isEmpty()) {
                return AjaxResult.success(new ArrayList<>());
            }

            // 使用并发查询优化错误信息获取
            List<WDLErrorInfo> errorInfoList = Collections.synchronizedList(new ArrayList<>());
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (ZhIotEquipment equipment : equipmentList) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        EquipmentDetail realDataList = realDataService.getRealDataList(equipment.getId());

                        // 如果实时数据为空，跳过当前设备
                        if (realDataList == null || realDataList.getAttrList() == null) {
                            return;
                        }
                        if (!realDataList.isOnLine()) {
                            return;
                        }

                        // 处理实时数据中的每个属性，寻找错误信息
                        realDataList.getAttrList().forEach(attr -> {
                            if (attr != null && "1".equals(attr.getAlarm()) && attr.getAttrName() != null) {
                                WDLErrorInfo wdlErrorInfo = new WDLErrorInfo();
                                wdlErrorInfo.setErrorInfo(attr.getAttrName());
                                wdlErrorInfo.setDeviceName(equipment.getEquipmentName() + equipment.getEquipmentCode());
                                wdlErrorInfo.setErrorTime(attr.getLastUpdateTime());
                                errorInfoList.add(wdlErrorInfo);
                            }
                        });
                    } catch (Exception e) {
                        log.error("处理设备 {} 的实时数据时发生异常", equipment.getId(), e);
                    }
                }, executorService);

                futures.add(future);
            }

            // 等待所有异步任务完成
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .get(30, TimeUnit.SECONDS);
                ans.addAll(errorInfoList);
            } catch (Exception e) {
                log.error("错误信息查询异常", e);
            }

            // 更新缓存中的错误信息列表
            try {
                String json1 = objectMapper.writeValueAsString(ans);
                stringRedisTemplate.opsForValue().set(cacheKey, json1, REDIS_CACHE_EXPIRATION_SECONDS, TimeUnit.SECONDS);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            // 返回错误信息列表的响应
            return AjaxResult.success(ans);
        } catch (Exception e) {
            log.error("获取错误信息时发生异常", e);
            return AjaxResult.error("获取错误信息失败");
        }
    }


    @GetMapping("/workshop/list")
    public AjaxResult selectZhWorkshopList(ZhWorkshop zhWorkshop) {
        List<ZhWorkshop> zhWorkshopList = zhWorkshopMapper.selectZhWorkshopList(zhWorkshop);
        for (ZhWorkshop workshop : zhWorkshopList) {
            List<ZhWorkshop> childrenList = zhWorkshopMapper.selectZhWorkshopByParentId(workshop.getId());
            workshop.setChildren(childrenList);
        }
        return AjaxResult.success(zhWorkshopList);
    }

}
