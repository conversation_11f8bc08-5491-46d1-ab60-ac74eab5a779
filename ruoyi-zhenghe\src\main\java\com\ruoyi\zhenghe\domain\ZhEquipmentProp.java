package com.ruoyi.zhenghe.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 设备属性配置对象 zh_equipment_prop
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */

@Data
@ApiModel(value = "ZhEquipmentProp",description = "设备属性配置对象")
public class ZhEquipmentProp extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 设备id */
    @Excel(name = "设备id")
    @ApiModelProperty(value = "设备id")
    private Long equipmentId;

    /** 属性id */
    @Excel(name = "属性id")
    @ApiModelProperty(value = "属性id")
    private Long attrId;

    @Excel(name = "属性名称")
    @ApiModelProperty(value = "属性名称")
    private String attrName;

    /** 故障值 */
    @ApiModelProperty(value = "故障值")
    @Excel(name = "故障值")
    private String faultVal;

    /** 最小值 */
    @Excel(name = "最小值")
    @ApiModelProperty(value = "最小值")
    private String minVal;

    /** 最小值 */
    @Excel(name = "最大值")
    @ApiModelProperty(value = "最大值")
    private String maxVal;

    /** 枚举数据类型 */
    @Excel(name = "枚举数据类型")
    @ApiModelProperty(value = "枚举数据类型")
    private String enumList;

    /** 显示方式：num 数值  line 折线 */
    @Excel(name = "显示方式：num 数值  line 折线")
    @ApiModelProperty(value = "显示方式：num 数值  line 折线")
    private String showType;

    /** 自动记录故障 */
    @Excel(name = "自动记录故障")
    @ApiModelProperty(value = "自动记录故障")
    private String autoFault;

    /** 故障持续多长时间开始记录，单位秒 */
    @Excel(name = "故障持续多长时间开始记录，单位秒")
    @ApiModelProperty(value = "故障持续多长时间开始记录，单位秒")
    private Long faultTime;

    /** 自动生成工单 */
    @Excel(name = "自动生成工单")
    @ApiModelProperty(value = "自动生成工单")
    private Integer autoBill;

    /** 故障持续多长时间生成工单，单位秒 */
    @Excel(name = "故障持续多长时间生成工单，单位秒")
    @ApiModelProperty(value = "故障持续多长时间生成工单，单位秒")
    private Long billTime;

    /** 工单接收人 */
    @Excel(name = "工单接收人")
    @ApiModelProperty(value = "工单接收人")
    private String billUser;

    /** 倍率 */
    @Excel(name = "倍率")
    @ApiModelProperty(value = "倍率")
    private BigDecimal customMultiple;



    @ApiModelProperty(value = "数据类型")
    private  String arrType;


    @ApiModelProperty(value = "数据分类 1数据 2故障")
    private String attrClass;

    @ApiModelProperty(value = "属性编码")
    private String attrCode;

    @ApiModelProperty(value = "最后更新值")
    private String lastVal;
    @ApiModelProperty(value = "最后更新时间")
    private String lastUpdateTime;

    @ApiModelProperty(value = "是否报警(1报警；0不报)")
    private String alarm;

    @ApiModelProperty(value = "单位")
    private String attrUnit;

}
