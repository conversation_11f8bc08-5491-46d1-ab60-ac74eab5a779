package com.ruoyi.zhenghe.domain.dto;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 产品数量展示DTO
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@ApiModel(value = "ProductQuantityDto", description = "产品数量展示DTO")
public class ProductQuantityDto
{
    /** 产品型号 */
    @Excel(name = "产品型号", sort = 1)
    @ApiModelProperty(value = "产品型号")
    private String productName;

    /** 生产设备数量 */
    @Excel(name = "生产设备数量", sort = 2)
    @ApiModelProperty(value = "生产设备数量")
    private Integer deviceCount;

    /** 生产产品计数 */
    @Excel(name = "生产产品计数", sort = 3)
    @ApiModelProperty(value = "生产产品计数")
    private Integer productionCount;
}
