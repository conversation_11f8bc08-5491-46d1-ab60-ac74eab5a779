package com.ruoyi.zhenghe.mapper;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhBaseProcess;

/**
 * 基础数据-工序Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface ZhBaseProcessMapper 
{
    /**
     * 查询基础数据-工序
     * 
     * @param id 基础数据-工序主键
     * @return 基础数据-工序
     */
    public ZhBaseProcess selectZhBaseProcessById(Long id);

    /**
     * 查询基础数据-工序列表
     * 
     * @param zhBaseProcess 基础数据-工序
     * @return 基础数据-工序集合
     */
    public List<ZhBaseProcess> selectZhBaseProcessList(ZhBaseProcess zhBaseProcess);

    /**
     * 新增基础数据-工序
     * 
     * @param zhBaseProcess 基础数据-工序
     * @return 结果
     */
    public int insertZhBaseProcess(ZhBaseProcess zhBaseProcess);

    /**
     * 修改基础数据-工序
     * 
     * @param zhBaseProcess 基础数据-工序
     * @return 结果
     */
    public int updateZhBaseProcess(ZhBaseProcess zhBaseProcess);

    /**
     * 删除基础数据-工序
     * 
     * @param id 基础数据-工序主键
     * @return 结果
     */
    public int deleteZhBaseProcessById(Long id);

    /**
     * 批量删除基础数据-工序
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZhBaseProcessByIds(Long[] ids);
}
