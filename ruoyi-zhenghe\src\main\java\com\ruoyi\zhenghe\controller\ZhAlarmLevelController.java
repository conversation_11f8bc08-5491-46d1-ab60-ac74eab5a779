package com.ruoyi.zhenghe.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.zhenghe.domain.ZhWorkshop;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.zhenghe.domain.ZhAlarmLevel;
import com.ruoyi.zhenghe.service.IZhAlarmLevelService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 告警等级管理Controller
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@Api(tags = "告警等级管理Controller")
@RestController
@RequestMapping("/zhenghe/level")
public class ZhAlarmLevelController extends BaseController
{
    @Autowired
    private IZhAlarmLevelService zhAlarmLevelService;

    /**
     * 查询告警等级管理列表
     */
    @ApiOperation(value = "查询告警等级管理列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "alarmName",value = "告警等级名称",required = false,paramType = "query",dataType = "String"),
            @ApiImplicitParam(name = "alarmCode",value = "告警编码",required = false,paramType = "query",dataType = "String"),
            @ApiImplicitParam(name = "pageNum",value = "页数",required = true,paramType = "query",dataType = "integer"),
            @ApiImplicitParam(name = "pageSize",value = "条数",required = true,paramType = "query",dataType = "integer")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:level:list')")
    //@DataScope(deptAlias = "a")
    @GetMapping("/list")
    public TableDataInfo list(ZhAlarmLevel zhAlarmLevel)
    {
        startPage();
        List<ZhAlarmLevel> list = zhAlarmLevelService.selectZhAlarmLevelList(zhAlarmLevel);
        return getDataTable(list);
    }

    /**
     * 导出告警等级管理列表
     */
    @ApiOperation(value = "导出告警等级管理列表")
    @PreAuthorize("@ss.hasPermi('zhenghe:level:export')")
    @Log(title = "告警等级管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhAlarmLevel zhAlarmLevel)
    {
        List<ZhAlarmLevel> list = zhAlarmLevelService.selectZhAlarmLevelList(zhAlarmLevel);
        ExcelUtil<ZhAlarmLevel> util = new ExcelUtil<ZhAlarmLevel>(ZhAlarmLevel.class);
        util.exportExcel(response, list, "告警等级管理数据");
    }

    /**
     * 导入基础数据-告警等级列表
     */
    @ApiOperation(value = "导入基础数据-告警等级列表")
    //@PreAuthorize("@ss.hasPermi('zhenghe:level:import')")
    @Log(title = "基础数据-告警等级", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws IOException {
        ExcelUtil<ZhAlarmLevel> util = new ExcelUtil<ZhAlarmLevel>(ZhAlarmLevel.class);
        List<ZhAlarmLevel> zhAlarmLevelList = util.importExcel(file.getInputStream());
        if (StringUtils.isNull(zhAlarmLevelList) || zhAlarmLevelList.size()==0){
            throw new ServiceException("导入告警等级数据不能为空");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ZhAlarmLevel zhAlarmLevel : zhAlarmLevelList) {
            try {
                zhAlarmLevel.setDeptId(SecurityUtils.getDeptId());
                zhAlarmLevelService.insertZhAlarmLevel(zhAlarmLevel);
                successNum++;
                successMsg.append("<br/>" + successNum + "、告警等级 " + zhAlarmLevel.getAlarmName() + " 导入成功");
            }
            catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、告警等级 " + zhAlarmLevel.getAlarmName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum>0){
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return AjaxResult.success(successMsg.toString());
    }

    /**
     * 导出告警等级管理模板
     */
    @ApiOperation(value = "导出告警等级管理模板")
    @Log(title = "告警等级模板", businessType = BusinessType.EXPORT)
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response, ZhAlarmLevel zhAlarmLevel)
    {
        ExcelUtil<ZhAlarmLevel> util = new ExcelUtil<ZhAlarmLevel>(ZhAlarmLevel.class);
        util.exportExcel(response, new ArrayList<>(), "告警等级管理数据");
    }

    /**
     * 获取告警等级管理详细信息
     */
    @ApiOperation(value = "获取告警等级管理详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "告警等级id",required = true,paramType = "path",dataType = "long")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:level:query')")
    @GetMapping(value = "/{id}")
    public R<ZhAlarmLevel> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(zhAlarmLevelService.selectZhAlarmLevelById(id));
    }

    /**
     * 新增告警等级管理
     */
    @ApiOperation(value = "新增告警等级管理")
    @PreAuthorize("@ss.hasPermi('zhenghe:level:add')")
    @Log(title = "告警等级管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZhAlarmLevel zhAlarmLevel)
    {
        return toAjax(zhAlarmLevelService.insertZhAlarmLevel(zhAlarmLevel));
    }

    /**
     * 修改告警等级管理
     */
    @ApiOperation(value = "修改告警等级管理")
    @PreAuthorize("@ss.hasPermi('zhenghe:level:edit')")
    @Log(title = "告警等级管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhAlarmLevel zhAlarmLevel)
    {
        return toAjax(zhAlarmLevelService.updateZhAlarmLevel(zhAlarmLevel));
    }

    /**
     * 删除告警等级管理
     */
    @ApiOperation(value = "删除告警等级管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids",value = "告警等级id",required = true,paramType = "path",dataType = "long")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:level:remove')")
    @Log(title = "告警等级管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(zhAlarmLevelService.deleteZhAlarmLevelByIds(ids));
    }
}
