# API一致性测试脚本
$headers = @{
    'Accept' = 'application/json, text/plain, */*'
    'Accept-Language' = 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
    'Authorization' = 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZmMzZWU4ZTgtZThkZi00OWY3LTk5NmQtN2YxMjFmNzljMTRmIn0.hM_SnS9599Oian-DHaZtWjmD36cd75wuz7WB4cClfeNDC--baiwzX1oLXoc6zfh7d7J52_WkDQ_Lmqyc5vxaXg'
    'Connection' = 'keep-alive'
    'Referer' = 'http://**********/analysis/ProductCount'
    'User-Agent' = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
}

$session = New-Object Microsoft.PowerShell.Commands.WebRequestSession
$session.Cookies.Add((New-Object System.Net.Cookie('Admin-Token', 'eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZmMzZWU4ZTgtZThkZi00OWY3LTk5NmQtN2YxMjFmNzljMTRmIn0.hM_SnS9599Oian-DHaZtWjmD36cd75wuz7WB4cClfeNDC--baiwzX1oLXoc6zfh7d7J52_WkDQ_Lmqyc5vxaXg', '/', '**********')))

$url = 'http://**********/prod-api/zhenghe/deviceProductionRecord/productQuantity?pageNum=1&pageSize=10&beginTime=2025-07-22%2012%3A00%3A00&endTime=2025-07-22%2013%3A00%3A00'

Write-Host "=== API一致性测试 ==="
Write-Host "测试URL: $url"
Write-Host "测试时间段: 2025-07-22 12:00:00 - 2025-07-22 13:00:00"
Write-Host ""

# 进行5次查询测试
for ($i = 1; $i -le 5; $i++) {
    Write-Host "=== 第 $i 次查询 ==="
    Write-Host "时间: $(Get-Date)"
    
    try {
        $response = Invoke-WebRequest -UseBasicParsing -Uri $url -WebSession $session -Headers $headers
        $json = $response.Content | ConvertFrom-Json
        
        Write-Host "状态码: $($response.StatusCode)"
        Write-Host "数据行数: $($json.rows.Count)"
        
        if ($json.rows.Count -gt 0) {
            $totalProduction = 0
            Write-Host "产品详情:"
            foreach ($row in $json.rows) {
                Write-Host "  产品: $($row.productName), 产量: $($row.productionCount)"
                $totalProduction += $row.productionCount
            }
            Write-Host "总产量: $totalProduction"
        } else {
            Write-Host "无数据返回"
        }
        
    } catch {
        Write-Host "请求失败: $($_.Exception.Message)"
    }
    
    Write-Host ""
    
    if ($i -lt 5) {
        Write-Host "等待3秒..."
        Start-Sleep -Seconds 3
    }
}

Write-Host "=== 测试完成 ==="
Write-Host "请检查上述5次查询的产量数据是否一致"
Write-Host "如果数据一致，说明历史数据一致性修复成功"
Write-Host "如果数据不一致，说明仍需进一步修复"
