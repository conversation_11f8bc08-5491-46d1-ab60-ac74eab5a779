package com.ruoyi.util.iotdb;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.NumericFieldCasts;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.zhenghe.domain.PeriodMaintainData;
import com.ruoyi.zhengheiot.domain.HistoryData;
import com.ruoyi.zhengheiot.domain.IotRealData;
import com.ruoyi.zhengheiot.domain.ProductionCountData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.iotdb.isession.SessionDataSet;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.apache.iotdb.session.Session;
import org.apache.tsfile.read.common.Field;
import org.apache.tsfile.read.common.RowRecord;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.util.DataTimeUtil.toTimestamp;

@Slf4j
@Component
public class IoTDBUtil {
    @Resource
    private Session iotSession;

    /**
     * 将物联网实时数据添加到指定的企业设备中。
     * <p>
     * 该函数首先检查传入的企业标识和设备标识是否为空，若为空则抛出异常。
     * 接着检查数据列表是否非空，若数据列表有效，则遍历数据列表，提取有效的数据项，
     * 并将其插入到物联网数据库中。插入过程中会捕获并处理可能出现的数据库连接异常和语句执行异常。
     *
     * @param enterprise 企业标识，表示数据所属的企业，不能为空
     * @param device     设备标识，表示数据所属的设备，不能为空
     * @param dataList   物联网实时数据列表，包含多个数据项，每个数据项包含标签和值
     */
    public void addDataToIot(String enterprise, String device, List<IotRealData> dataList) {
        // 检查传入的企业标识和设备标识是否为空
        if (StringUtils.isEmpty(enterprise) || StringUtils.isEmpty(device)) {
            throw new IllegalArgumentException("企业标识和设备标识不能为空");
        }

        // 检查传入的数据列表是否非空且长度大于0
        if (dataList != null && !dataList.isEmpty()) {
            // 初始化测量点列表和值列表
            List<String> measurementPoint = new ArrayList<>();
            List<String> values = new ArrayList<>();

            // 遍历数据列表，提取有效的数据进行插入准备
            for (IotRealData data : dataList) {
                // 确保当前数据项的值不为空
                if (StringUtils.isNotEmpty(data.getVal())) {
                    String s = data.getTag().trim();
                    if (s.matches("^[A-Za-z_].*")) {
                        measurementPoint.add(data.getTag().trim());
                        values.add(data.getVal());
                    } else {
                        log.error("插入iotdb时，{}标签格式错误：" + s, device);
                    }
                }
            }

            // 尝试执行数据插入操作
            try {
                if (!values.isEmpty()) {
                    iotSession.insertRecord("root." + enterprise + "." + device, System.currentTimeMillis(), measurementPoint, values);
                }
            } catch (IoTDBConnectionException e) {
                // 处理物联网数据库连接异常，记录日志并重试或通知相关人员
                log.error("物联网数据库连接异常: ", e);
                // 这里可以加入重试逻辑或通知机制
            } catch (StatementExecutionException e) {
                // 处理语句执行异常，记录日志并重试或通知相关人员
                log.error("语句执行异常:", e);
                log.error("语句:" + "root." + enterprise + "." + device);
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < measurementPoint.size(); i++) {
                    sb.append(measurementPoint.get(i)).append(":");
                    sb.append(values.get(i)).append("\n");
                }
                log.error("数据:" + sb.toString());
                // 这里可以加入重试逻辑或通知机制
            }
        }
    }

    /**
     * 查询指定企业、设备、标签列表在给定时间范围内的历史数据，并返回JSON格式的结果。
     *
     * @param enterprise 企业码，用于构建查询路径。
     * @param deviceCode 设备编码，用于构建查询路径。
     * @param tagList    需要查询的点位List
     * @param startTime  查询的起始时间，格式为字符串。
     * @param endTime    查询的结束时间，格式为字符串。
     * @return JSONArray 包含查询结果的JSON数组，每个元素为一个时间点的数据记录。
     * @throws IoTDBConnectionException    当与IoTDB数据库连接失败时抛出。
     * @throws StatementExecutionException 当执行SQL语句失败时抛出。
     */
    public JSONArray listDataHistoryNew(String enterprise, String deviceCode, List<String> tagList, String startTime, String endTime, Integer pageNum, Integer pageSize) throws IoTDBConnectionException, StatementExecutionException {
        JSONArray result = new JSONArray();
        SessionDataSet dataSet = null;
        try {
            // 构建查询的标签字符串，每个标签使用last_value函数进行查询
            StringBuilder tagStr = new StringBuilder();
            StringBuilder tagStr2 = new StringBuilder();
            String selectStr = "root." + enterprise + "." + deviceCode;
            for (String tag : tagList) {
                tagStr.append("last_value(").append(tag).append(") AS ").append(tag).append(",");
            }
            for (String tag : tagList) {
                tagStr2.append(tag).append(" AS ").append(tag).append(",");
            }
            // 构建完整的SQL查询语句，按1分钟间隔分组，并使用前值填充缺失数据
            String sql = "SELECT " + tagStr.substring(0, tagStr.length() - 1) + " FROM " + selectStr + " GROUP BY([" + startTime + ", " + endTime + "),1m) FILL(PREVIOUS) ";
//            if (pageNum != null && pageSize != null) {
//                sql = sql + " LIMIT " + pageSize + " OFFSET " + (pageNum - 1) * pageSize;
//            }

            // 执行SQL查询并获取结果集
            dataSet = iotSession.executeQueryStatement(sql);
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");

            // 遍历结果集，将每条记录转换为JSON对象并添加到结果数组中
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                JSONObject object = new JSONObject();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
                for (int i = 0; i < measurements.size(); i++) {
                    String value = record.getFields().get(i).getStringValue();
                    if (StringUtils.isEmpty(value) || value.equals("null")) {
                        value = "/";
                    }
                    object.put(measurements.get(i).substring(measurements.get(i).lastIndexOf(".") + 1), value);
                }
                result.add(object);
            }
        } catch (Exception e) {
            // 捕获异常并抛出运行时异常，包含原始异常信息
            log.error("listDataHistoryNew Error", e);
        } finally {
            // 确保结果集被关闭，释放资源
            if (dataSet != null) {
                dataSet.close();
            }
        }
        return result;
    }


    public JSONArray listDataHistoryList(String enterprise,
                                         String deviceCode,
                                         List<String> tagList,
                                         String startTime,
                                         String endTime,
                                         Integer pageNum,
                                         Integer pageSize
    ) throws IoTDBConnectionException, StatementExecutionException {
        JSONArray result = new JSONArray();
        SessionDataSet mainSet = null;
        String path = String.format("root.%s.%s", enterprise, deviceCode);

        try {
            long startTs = toTimestamp(startTime);
            long endTs = toTimestamp(endTime);

            // 按需缓存：首次缺失时按记录时间查询历史最近值
            Map<String, String> lastCache = new HashMap<>();

            // 构建主查询 SQL
            String cols = tagList.stream()
                    .map(tag -> tag + " AS " + tag)
                    .collect(Collectors.joining(", "));
            StringBuilder sql = new StringBuilder(
                    String.format(
                            "SELECT %s FROM %s WHERE time >= %d AND time < %d",
                            cols, path, startTs, endTs
                    )
            );
//            if (pageNum != null && pageSize != null) {
//                int offset = (pageNum - 1) * pageSize;
//                sql.append(String.format(" LIMIT %d OFFSET %d", pageSize, offset));
//            }

            mainSet = iotSession.executeQueryStatement(sql.toString());
            List<String> columns = mainSet.getColumnNames();
            columns.remove("Time");

            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            df.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));

            while (mainSet.hasNext()) {
                RowRecord row = mainSet.next();
                JSONObject obj = new JSONObject();
                long ts = row.getTimestamp();
                obj.put("time", df.format(new Date(ts)));

                for (int i = 0; i < columns.size(); i++) {
                    String tag = columns.get(i);
                    String value = row.getFields().get(i).getStringValue();
                    if (StringUtils.isEmpty(value) || "null".equals(value)) {
                        // 首次缺失时，根据当前记录时间回查
                        if (!lastCache.containsKey(tag)) {
                            String historySql = String.format(
                                    "SELECT %s FROM %s WHERE time < %d ORDER BY time DESC LIMIT 1",
                                    tag, path, ts
                            );
                            try (SessionDataSet histSet = iotSession.executeQueryStatement(historySql)) {
                                if (histSet.hasNext()) {
                                    String v = histSet.next().getFields().get(0).getStringValue();
                                    lastCache.put(tag, StringUtils.isEmpty(v) ? "/" : v);
                                } else {
                                    lastCache.put(tag, "/");
                                }
                            }
                        }
                        value = lastCache.get(tag);
                    } else {
                        // 有值时更新缓存
                        lastCache.put(tag, value);
                    }
                    obj.put(tag, value);
                }
                result.add(obj);
            }

        } catch (Exception e) {
            throw new RuntimeException("查询历史数据失败"+ e);
        } finally {
            if (mainSet != null) {
                mainSet.close();
            }
        }
        return result;
    }


    public Map<String, Long> queryCount(String enterprise, String deviceCode, String startTime, String endTime) throws IoTDBConnectionException, StatementExecutionException {
        Map<String, Long> countMap = new HashMap<>();

        String sql = "SELECT COUNT(*) FROM root." + enterprise + "." + deviceCode +
                " WHERE time >= " + toTimestamp(startTime) + " AND time < " + toTimestamp(endTime);

        try {
            SessionDataSet dataSet = iotSession.executeQueryStatement(sql);

            // 提取列名
            List<String> columns = dataSet.getColumnNames();
            if (dataSet.hasNext()) {
                List<Field> row = dataSet.next().getFields();  // 获取第一行的数据
                for (int i = 0; i < columns.size(); i++) {
                    String col = columns.get(i);
                    String val = String.valueOf(row.get(i));
                    if (val != null && !val.equals("null")) {
                        countMap.put(col, Long.parseLong(val));
                    }
                }
            }
        } catch (Exception e) {
            // 捕获异常并抛出运行时异常，包含原始异常信息
            log.error("queryCount Error", e);
        }
        return countMap;
    }

    public Long queryCountMax(String enterprise, String deviceCode, String startTime, String endTime) throws IoTDBConnectionException, StatementExecutionException {
        Long maxVal = 0L;
        final Map<String, Long> countMap = queryCount(enterprise, deviceCode, startTime, endTime);
        for (Map.Entry<String, Long> entry : countMap.entrySet()) {
            if (entry.getValue() > maxVal) {
                maxVal = entry.getValue();
            }
        }
        return maxVal;
    }

    public List<HistoryData> getDataListFillWithZero(String enterprise, String device, String code, String start, String end) throws IoTDBConnectionException, StatementExecutionException {
        List<HistoryData> result = new ArrayList<>();
        SessionDataSet dataSet = null;
        try {
            String aim = "root." + enterprise + "." + device;
            String sql = "SELECT last_value(" + code + ") FROM " + aim + " GROUP BY([" + start + ", " + end + "),1m) FILL(PREVIOUS,2m) align by device";
//            dataSet = iotSession.executeQueryStatement("select last_value(" + code + ") from " + aim + " GROUP BY([" + start + ", " + end + "),1m) fill(PREVIOUS,2m) align by device");
            dataSet = iotSession.executeQueryStatement(sql);
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                HistoryData dataVO = new HistoryData();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                dataVO.setTime(dateFormat.format(new Date(record.getTimestamp())));
                dataVO.setDevice((record.getFields().get(0).getStringValue().equals(null) || record.getFields().get(0).getStringValue() == "null") ? "0" : record.getFields().get(0).getStringValue());
                dataVO.setVal((record.getFields().get(1).getStringValue().equals(null) || record.getFields().get(1).getStringValue() == "null") ? "0" : record.getFields().get(1).getStringValue());
                result.add(dataVO);
            }
        } catch (Exception e) {
//            e.printStackTrace();
        } finally {
//            iotSession.closeResultSet(dataSet);
        }
        return result;
    }

    /**
     * @param enterprise 企业编码
     * @param device     设备编码
     * @param code       设备属性
     * @param start      开始时间
     * @param end        结束时间
     * @return java.util.List<com.ruoyi.zhengheiot.domain.ProductionCountData>
     * <AUTHOR>
     * @remark 查询 设备一段时间内的生产计数
     * @date 2025/3/18 15:07
     */
    public Integer queryIotEquipmentProductionCount(String enterprise, String device, String code, String start, String end) throws IoTDBConnectionException, StatementExecutionException {
        List<ProductionCountData> result = new ArrayList<>();
        SessionDataSet dataSet = null;
        Integer count = 0;
        try {
            String aim = "root." + enterprise + "." + device;
            if (ObjectUtils.isEmpty(start) || ObjectUtils.isEmpty(end)) {
                dataSet = iotSession.executeQueryStatement("select " + code + " from " + aim + " order by time desc limit 1");
            } else {
                String sqlString = "select (MAX_VALUE(Number) - MIN_VALUE(Number)) AS count from " + aim + " where time>= " + start + "  and time <=" + end + "";
                dataSet = iotSession.executeQueryStatement(sqlString);
            }
            List<String> measurements = dataSet.getColumnNames();
            //measurements.remove("Time");
            ProductionCountData countData = new ProductionCountData();
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                ProductionCountData productionCountData = new ProductionCountData();
                for (int i = 0; i < measurements.size(); i++) {
                    if (measurements.get(i).equalsIgnoreCase("count")) {
                        try {
                            count = (int) record.getFields().get(i).getDoubleV();
                        } catch (Exception e) {
                        }
                    }
                }
            }
            return count;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.(dataSet);
        }
        return count;
    }


    /**
     * @param enterprise 企业编码
     * @param device     设备编码
     * @param code       设备属性
     * @param start      开始时间
     * @param end        结束时间
     * @return java.util.List<com.ruoyi.zhengheiot.domain.ProductionCountData>
     * <AUTHOR>
     * @remark 查询 设备一段时间内的生产时长
     * @date 2025/3/18 15:07
     */
    public Integer queryIotEquipmentProductionDuration(String enterprise, String device, String code, String start, String end) throws IoTDBConnectionException, StatementExecutionException {
        SessionDataSet dataSet = null;
        Integer operTime = 0;
        try {
            String aim = "root." + enterprise + "." + device;
            //String sqlString = "select count_if(Status=1, 1) AS operTime from " + aim + " GROUP BY([" + start + ", " + end + "),1m) having count_if(Status=1, 1) !=0";
            String sqlString = "select count_if(" + code + "=1, 1) AS operTime from " + aim + " GROUP BY([" + start + ", " + end + "),1m) having count_if(" + code + "=1, 1) !=0";
            dataSet = iotSession.executeQueryStatement(sqlString);
            List<String> measurements = dataSet.getColumnNames();
            measurements.remove("Time");
            ProductionCountData countData = new ProductionCountData();
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                for (int i = 0; i < measurements.size(); i++) {
                    if (measurements.get(i).equalsIgnoreCase("operTime")) {
                        operTime++;
                    }
                }
            }
            return operTime;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.(dataSet);
        }
        return operTime;
    }

/**
 * 统计指定时间范围内设备标签为特定值的分钟数
 *
 * <p>使用FILL(PREVIOUS)填充策略确保每分钟都有数据，避免因数据上传间隔导致的统计遗漏。
 * 当某分钟没有数据时，使用前一个有效值进行填充，然后统计目标值的分钟数。</p>
 *
 * @param enterprise 企业编码
 * @param deviceCode 设备编码
 * @param tag 标签名称（如Power_ON）
 * @param value 标签值（如1.0表示开机，0.0表示待机）
 * @param start 开始时间（格式：yyyy-MM-dd HH:mm:ss）
 * @param end 结束时间（格式：yyyy-MM-dd HH:mm:ss）
 * @return 匹配条件的分钟数
 * @throws IoTDBConnectionException IoTDB连接异常
 * @throws StatementExecutionException SQL执行异常
 */
public long getCountTag(String enterprise, String deviceCode, String tag, String value, String start, String end)
    throws IoTDBConnectionException, StatementExecutionException {
    SessionDataSet sessionDataSet = null;

    // 构建SQL查询 - 使用LAST_VALUE和FILL(PREVIOUS)确保每分钟都有数据
    // 这样可以避免某分钟没有数据上传导致的统计遗漏
    String sql = "SELECT LAST_VALUE(" + tag + ") AS " + tag + "_value " +
                "FROM root." + enterprise + "." + deviceCode + " " +
                "WHERE time >= " + start + " AND time < " + end + " " +
                "GROUP BY ([" + start + ", " + end + "), 1m) " +
                "FILL(PREVIOUS)";

    try {
        sessionDataSet = iotSession.executeQueryStatement(sql);
        long totalMinutes = 0;

        // 遍历所有分钟区间，统计目标值的分钟数
        while (sessionDataSet.hasNext()) {
            RowRecord record = sessionDataSet.next();
            if (record.getFields().size() > 0) {
                String fieldValue = record.getFields().get(0).getStringValue();
                // 检查该分钟的值是否等于目标值
                if (value.equals(fieldValue)) {
                    totalMinutes++;
                }
            }
        }

        return totalMinutes;

    } catch (Exception e) {
        log.error("设备状态查询异常 - 设备: {}, 标签: {}", deviceCode, tag, e);

        // 如果填充查询失败，尝试使用原来的分组查询作为降级方案
        try {
            if (sessionDataSet != null) {
                sessionDataSet.close();
                sessionDataSet = null;
            }

            String fallbackSql = "SELECT COUNT(*) FROM root." + enterprise + "." + deviceCode +
                                " WHERE " + tag + " = " + value +
                                " AND time >= " + start + " AND time < " + end +
                                " GROUP BY ([" + start + ", " + end + "), 1m)" +
                                " HAVING COUNT(*) > 0";

            sessionDataSet = iotSession.executeQueryStatement(fallbackSql);
            long totalMinutes = 0;
            while (sessionDataSet.hasNext()) {
                sessionDataSet.next();
                totalMinutes++;
            }
            log.warn("使用降级查询，返回分钟数: {}", totalMinutes);
            return totalMinutes;

        } catch (Exception fallbackException) {
            log.error("降级查询失败 - 设备: {}", deviceCode, fallbackException);
        }
    } finally {
        if (sessionDataSet != null) {
            try {
                sessionDataSet.close();
            } catch (Exception e) {
                // 静默处理资源关闭异常
            }
        }
    }
    return 0;
}


    /**
     * @param enterprise 企业编码
     * @param device     设备编码
     * @param code       设备属性
     * @param start      开始时间
     * @param end        结束时间
     * @return java.util.List<com.ruoyi.zhengheiot.domain.ProductionCountData>
     * <AUTHOR>
     * @remark 查询 设备一段时间内的属性差值
     * @date 2025/3/19 10:07
     */
    public double queryIotEquipmentAttributeDifference(String enterprise, String device, String code, String start, String end) throws IoTDBConnectionException, StatementExecutionException {
        List<ProductionCountData> result = new ArrayList<>();
        SessionDataSet dataSet = null;
        double count = 0.00;
        try {
            String aim = "root." + enterprise + "." + device;
            if (ObjectUtils.isEmpty(start) || ObjectUtils.isEmpty(end)) {
                dataSet = iotSession.executeQueryStatement("select " + code + " from " + aim + " order by time desc limit 1");
            } else {
                String sqlString = "select (MAX_VALUE(" + code + ") - MIN_VALUE(" + code + ")) AS count from " + aim + " where time>= " + start + "  and time <=" + end + "";
                dataSet = iotSession.executeQueryStatement(sqlString);
            }
            List<String> measurements = dataSet.getColumnNames();
            //measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                for (int i = 0; i < measurements.size(); i++) {
                    if (measurements.get(i).equalsIgnoreCase("count")) {
                        try {
                            count = record.getFields().get(i).getDoubleV();
                        } catch (Exception e) {
//                            throw new RuntimeException(e);
                        }
                    }
                }
            }
            String format = String.format("%.3f", count);
            return Double.parseDouble(format);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.(dataSet);
        }
        return count;
    }


    /**
     * 存储产品生产数据到IoTDB
     *
     * @param enterprise 企业编码
     * @param deviceCode 设备编码
     * @param productName 产品名称
     * @param cumulativeCount 累计产量
     * @param batchCount 当前批次产量
     * @param status 生产状态 (0-停止, 1-生产中)
     * @param batchStartTime 批次开始时间戳
     * @param startCumulativeCount 批次开始时的累计产量
     * @throws IoTDBConnectionException IoTDB连接异常
     * @throws StatementExecutionException SQL执行异常
     */
    public void saveProductionData(String enterprise, String deviceCode, String productName,
                                 int cumulativeCount, int batchCount, int status, long batchStartTime, int startCumulativeCount)
            throws IoTDBConnectionException, StatementExecutionException {

        if (StringUtils.isEmpty(enterprise) || StringUtils.isEmpty(deviceCode) || StringUtils.isEmpty(productName)) {
            throw new IllegalArgumentException("企业编码、设备编码和产品名称不能为空");
        }

        try {
            // 构建时间序列路径
            String basePath = String.format("root.%s.%s.production.%s", enterprise, deviceCode, productName.replace("-", "_"));

            // 准备测量点和值
            List<String> measurements = new ArrayList<>();
            List<String> values = new ArrayList<>();

            measurements.add("count");
            values.add(String.valueOf(cumulativeCount));

            measurements.add("status");
            values.add(String.valueOf(status));

            measurements.add("batch_count");
            values.add(String.valueOf(batchCount));

            measurements.add("batch_start");
            values.add(String.valueOf(batchStartTime));

            measurements.add("start_count");
            values.add(String.valueOf(startCumulativeCount));

            // 插入数据
            iotSession.insertRecord(basePath, System.currentTimeMillis(), measurements, values);

//            log.debug("保存产品生产数据到IoTDB: 设备={}, 产品={}, 累计产量={}, 批次产量={}, 开始累计产量={}, 状态={}",
//                    deviceCode, productName, cumulativeCount, batchCount, startCumulativeCount, status);

        } catch (Exception e) {
            log.error("保存产品生产数据到IoTDB失败: 设备={}, 产品={}", deviceCode, productName, e);
            throw e;
        }
    }

    /**
     * 存储产品生产数据到IoTDB（兼容旧版本方法）
     */
    public void saveProductionData(String enterprise, String deviceCode, String productName,
                                 int cumulativeCount, int batchCount, int status, long batchStartTime)
            throws IoTDBConnectionException, StatementExecutionException {
        // 调用新版本方法，开始累计产量默认为当前累计产量
        saveProductionData(enterprise, deviceCode, productName, cumulativeCount, batchCount, status, batchStartTime, cumulativeCount);
    }

    /**
     * 存储设备原始生产数据到IoTDB（存储MQTT原始数据）
     * 这个方法存储设备级别的原始计数器数据，不做任何产品级别的计算
     *
     * @param enterprise 企业编码
     * @param deviceCode 设备编码
     * @param productName 当前生产的产品名称
     * @param rawCount 设备原始计数器值（Number值）
     * @param productionStatus 生产状态 (0-停止, 1-生产中)
     * @param timestamp 数据时间戳
     * @throws IoTDBConnectionException IoTDB连接异常
     * @throws StatementExecutionException SQL执行异常
     */
    public void saveRawProductionData(String enterprise, String deviceCode, String productName,
                                    int rawCount, int productionStatus, long timestamp)
            throws IoTDBConnectionException, StatementExecutionException {

        if (StringUtils.isEmpty(enterprise) || StringUtils.isEmpty(deviceCode)) {
            throw new IllegalArgumentException("企业编码和设备编码不能为空");
        }

        try {
            // 构建设备级别的时间序列路径
            String devicePath = String.format("root.%s.%s.raw", enterprise, deviceCode);

            // 准备测量点和值
            List<String> measurements = new ArrayList<>();
            List<String> values = new ArrayList<>();

            // 存储原始计数器值
            measurements.add("counter");
            values.add(String.valueOf(rawCount));

            // 存储当前生产的产品
            measurements.add("current_product");
            values.add(productName != null ? productName : "");

            // 存储生产状态
            measurements.add("status");
            values.add(String.valueOf(productionStatus));

            // 插入数据
            iotSession.insertRecord(devicePath, timestamp, measurements, values);

//            log.debug("保存设备原始生产数据到IoTDB: 设备={}, 产品={}, 原始计数={}, 状态={}, 时间={}",
//                    deviceCode, productName, rawCount, productionStatus, new Date(timestamp));

        } catch (Exception e) {
            log.error("保存设备原始生产数据到IoTDB失败: 设备={}, 产品={}", deviceCode, productName, e);
            throw e;
        }
    }

    /**
     * 保存产品生产快照数据到IoTDB
     * 每次MQTT消息都保存一个完整的生产快照，包含所有产品的状态和累计产量
     *
     * @param enterprise 企业编码
     * @param deviceCode 设备编码
     * @param productSnapshots 产品快照数据
     * @param timestamp 时间戳
     */
    public void saveProductionSnapshot(String enterprise, String deviceCode,
                                     List<ProductionSnapshot> productSnapshots, long timestamp)
            throws IoTDBConnectionException, StatementExecutionException {

        if (StringUtils.isEmpty(enterprise) || StringUtils.isEmpty(deviceCode)) {
            throw new IllegalArgumentException("企业编码和设备编码不能为空");
        }

        try {
            // 构建设备快照数据路径
            String snapshotPath = String.format("root.%s.%s.snapshot", enterprise, deviceCode);
            log.info("构建快照数据路径: {}", snapshotPath);

            // 准备测量点和值
            List<String> measurements = new ArrayList<>();
            List<String> values = new ArrayList<>();

            // 为每个产品保存快照数据
            for (ProductionSnapshot snapshot : productSnapshots) {
                String productKey = normalizeProductKey(snapshot.getProductName());

                // 产品状态 (1=生产中, 0=停止)
                String statusMeasurement = productKey + "_status";
                String countMeasurement = productKey + "_count";

                measurements.add(statusMeasurement);
                values.add(String.valueOf(snapshot.getStatus()));

                // 产品累计产量
                measurements.add(countMeasurement);
                values.add(String.valueOf(snapshot.getCumulativeCount()));

                log.info("添加快照测量点: {} = {}, {} = {}",
                        statusMeasurement, snapshot.getStatus(),
                        countMeasurement, snapshot.getCumulativeCount());
            }

            // 插入快照数据
            if (!measurements.isEmpty()) {
                log.info("准备插入IoTDB快照数据: 路径={}, 时间戳={}, 测量点数={}",
                        snapshotPath, timestamp, measurements.size());
                log.info("测量点列表: {}", measurements);
                log.info("数值列表: {}", values);

                iotSession.insertRecord(snapshotPath, timestamp, measurements, values);
                log.info("成功保存设备 {} 生产快照到IoTDB: {} 个产品, {} 个测量点, 时间={}",
                        deviceCode, productSnapshots.size(), measurements.size(), new Date(timestamp));
            } else {
                log.warn("没有测量点数据需要保存到IoTDB");
            }

        } catch (Exception e) {
            log.error("保存生产快照数据到IoTDB失败: 设备={}", deviceCode, e);
            throw e;
        }
    }

    /**
     * 产品生产快照数据类
     */
    public static class ProductionSnapshot {
        private String productName;
        private int status;  // 1=生产中, 0=停止
        private int cumulativeCount;  // 累计产量

        public ProductionSnapshot(String productName, int status, int cumulativeCount) {
            this.productName = productName;
            this.status = status;
            this.cumulativeCount = cumulativeCount;
        }

        // Getters and setters
        public String getProductName() { return productName; }
        public void setProductName(String productName) { this.productName = productName; }
        public int getStatus() { return status; }
        public void setStatus(int status) { this.status = status; }
        public int getCumulativeCount() { return cumulativeCount; }
        public void setCumulativeCount(int cumulativeCount) { this.cumulativeCount = cumulativeCount; }
    }

    /**
     * 规范化产品键，确保符合IoTDB时间序列命名规范
     * 1. 替换 "-" 为 "_"
     * 2. 如果以数字开头，添加 "prod_" 前缀
     * 3. 替换其他特殊字符为 "_"
     *
     * @param productName 产品名称
     * @return 规范化的产品键
     */
    private String normalizeProductKey(String productName) {
        if (StringUtils.isEmpty(productName)) {
            return "unknown_product";
        }

        // 替换 "-" 为 "_"
        String productKey = productName.replace("-", "_");

        // 替换其他可能的特殊字符
        productKey = productKey.replaceAll("[^a-zA-Z0-9_]", "_");

        // 如果以数字开头，添加 "prod_" 前缀
        if (Character.isDigit(productKey.charAt(0))) {
            productKey = "prod_" + productKey;
        }

        log.debug("产品名称 '{}' 规范化为产品键 '{}'", productName, productKey);
        return productKey;
    }

    /**
     * 反规范化产品键，将IoTDB时间序列键转换回原始产品名称
     * 1. 如果以 "prod_" 开头，去掉前缀
     * 2. 替换 "_" 为 "-"
     *
     * @param productKey 产品键
     * @return 原始产品名称
     */
    private String denormalizeProductKey(String productKey) {
        if (StringUtils.isEmpty(productKey)) {
            return "";
        }

        String productName = productKey;

        // 如果以 "prod_" 开头，去掉前缀
        if (productName.startsWith("prod_")) {
            productName = productName.substring(5);
        }

        // 替换 "_" 为 "-"
        productName = productName.replace("_", "-");

        log.debug("产品键 '{}' 反规范化为产品名称 '{}'", productKey, productName);
        return productName;
    }

    /**
     * 基于快照数据查询产品在指定时间范围内的精确生产数量
     * 这是新的推荐查询方法，基于完整的生产快照数据
     *
     * @param enterprise 企业编码
     * @param deviceCode 设备编码
     * @param productName 产品名称
     * @param startTime 开始时间 (格式: yyyy-MM-dd HH:mm:ss)
     * @param endTime 结束时间 (格式: yyyy-MM-dd HH:mm:ss)
     * @return 生产数量
     */
    public int queryProductionCountFromSnapshot(String enterprise, String deviceCode, String productName,
                                              String startTime, String endTime)
            throws IoTDBConnectionException, StatementExecutionException {

        if (StringUtils.isEmpty(enterprise) || StringUtils.isEmpty(deviceCode) || StringUtils.isEmpty(productName)) {
            return 0;
        }

        try {
            // 构建快照数据路径
            String snapshotPath = String.format("root.%s.%s.snapshot", enterprise, deviceCode);
            String productKey = normalizeProductKey(productName);

            log.info("开始基于快照数据查询产品生产数量: 企业编码={}, 设备={}, 产品={}, 产品键={}, 时间范围=[{}, {}], 快照路径={}",
                    enterprise, deviceCode, productName, productKey, startTime, endTime, snapshotPath);

            // 首先检查该产品的快照时间序列是否存在数据
            if (!checkSnapshotTimeSeriesExists(snapshotPath, productKey)) {
                log.warn("设备 {} 产品 {} 快照数据时间序列不存在: {}", deviceCode, productName, snapshotPath);
                return 0;
            }

            // 查询指定时间范围内该产品的生产数量
            int productionCount = calculateProductionFromSnapshot(snapshotPath, productKey, startTime, endTime);

            log.info("基于快照数据查询产品生产数量完成: 设备={}, 产品={}, 时间范围=[{}, {}], 产量={}",
                    deviceCode, productName, startTime, endTime, productionCount);

            return productionCount;

        } catch (Exception e) {
            log.error("基于快照数据查询产品生产数量失败: 设备={}, 产品={}, 时间范围=[{}, {}]",
                    deviceCode, productName, startTime, endTime, e);
            return 0;
        }
    }

    /**
     * 从快照数据计算指定产品的生产数量
     * 分析产品状态变化，计算每个生产段的数量差值
     */
    private int calculateProductionFromSnapshot(String snapshotPath, String productKey, String startTime, String endTime)
            throws IoTDBConnectionException, StatementExecutionException {

        SessionDataSet dataSet = null;
        int totalProduction = 0;

        try {
            // 将时间字符串转换为时间戳
            long startTimestamp = convertTimeStringToTimestamp(startTime);
            long endTimestamp = convertTimeStringToTimestamp(endTime);

            // 查询时间范围内的所有快照数据，按时间排序
            String sql = String.format(
                "SELECT %s_status, %s_count FROM %s WHERE time >= %d AND time <= %d ORDER BY time ASC",
                productKey, productKey, snapshotPath, startTimestamp, endTimestamp
            );

            log.debug("执行快照查询SQL: {}", sql);
            dataSet = iotSession.executeQueryStatement(sql);

            // 分析数据，找出该产品的生产段
            int segmentStartCount = 0;
            boolean inProductionSegment = false;
            int lastCount = 0;

            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                if (record.getFields().size() >= 2) {
                    String statusStr = record.getFields().get(0).getStringValue();
                    String countStr = record.getFields().get(1).getStringValue();

                    if (statusStr == null || countStr == null ||
                        statusStr.equals("null") || countStr.equals("null")) {
                        continue;
                    }

                    int status = (int) Double.parseDouble(statusStr);
                    int count = (int) Double.parseDouble(countStr);
                    boolean isProducing = (status == 1);

                    if (isProducing) {
                        if (!inProductionSegment) {
                            // 开始新的生产段
                            segmentStartCount = count;
                            inProductionSegment = true;
                            log.debug("产品 {} 开始生产段，起始计数: {}", productKey, segmentStartCount);
                        }
                        lastCount = count; // 更新最后的计数
                    } else {
                        if (inProductionSegment) {
                            // 结束当前生产段
                            int segmentProduction = lastCount - segmentStartCount;
                            totalProduction += Math.max(0, segmentProduction);
                            inProductionSegment = false;
                            log.debug("产品 {} 结束生产段，结束计数: {}, 段产量: {}, 累计产量: {}",
                                    productKey, lastCount, segmentProduction, totalProduction);
                        }
                    }
                }
            }

            // 如果查询结束时仍在生产该产品，需要计算最后一段
            if (inProductionSegment) {
                // 查询结束时间点的计数器值
                int endCount = queryLatestCountFromSnapshot(snapshotPath, productKey, endTimestamp);
                if (endCount > segmentStartCount) {
                    int segmentProduction = endCount - segmentStartCount;
                    totalProduction += segmentProduction;
                    log.debug("产品 {} 最后生产段，结束计数: {}, 段产量: {}, 最终累计产量: {}",
                            productKey, endCount, segmentProduction, totalProduction);
                }
            }

            return totalProduction;

        } catch (Exception e) {
            log.error("从快照数据计算产品生产数量失败: 产品={}, 时间范围=[{}, {}]", productKey, startTime, endTime, e);
            return 0;
        } finally {
            if (dataSet != null) {
                try {
                    dataSet.close();
                } catch (Exception e) {
                    log.warn("关闭数据集失败", e);
                }
            }
        }
    }

    /**
     * 查询指定时间点快照数据中产品的最新计数
     */
    private int queryLatestCountFromSnapshot(String snapshotPath, String productKey, long timestamp)
            throws IoTDBConnectionException, StatementExecutionException {

        SessionDataSet dataSet = null;
        try {
            String sql = String.format("SELECT last_value(%s_count) FROM %s WHERE time <= %d",
                                     productKey, snapshotPath, timestamp);
            dataSet = iotSession.executeQueryStatement(sql);

            if (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                if (record.getFields().size() >= 1) {
                    String countStr = record.getFields().get(0).getStringValue();
                    if (countStr != null && !countStr.equals("null")) {
                        return (int) Double.parseDouble(countStr);
                    }
                }
            }
            return 0;
        } finally {
            if (dataSet != null) {
                try {
                    dataSet.close();
                } catch (Exception e) {
                    log.warn("关闭数据集失败", e);
                }
            }
        }
    }

    /**
     * 基于原始数据查询产品在指定时间范围内的精确生产数量
     * 通过分析设备原始计数器数据和产品切换记录来计算实际产量
     *
     * @param enterprise 企业编码
     * @param deviceCode 设备编码
     * @param productName 产品名称
     * @param startTime 开始时间 (格式: yyyy-MM-dd HH:mm:ss)
     * @param endTime 结束时间 (格式: yyyy-MM-dd HH:mm:ss)
     * @return 生产数量
     */
    public int queryProductionCountFromRawData(String enterprise, String deviceCode, String productName,
                                             String startTime, String endTime)
            throws IoTDBConnectionException, StatementExecutionException {

        if (StringUtils.isEmpty(enterprise) || StringUtils.isEmpty(deviceCode) || StringUtils.isEmpty(productName)) {
            return 0;
        }

        try {
            // 构建设备原始数据路径
            String rawPath = String.format("root.%s.%s.raw", enterprise, deviceCode);

            log.debug("开始基于原始数据查询产品生产数量: 设备={}, 产品={}, 时间范围=[{}, {}]",
                    deviceCode, productName, startTime, endTime);

            // 首先检查该时间序列是否存在数据
            if (!checkTimeSeriesExists(rawPath)) {
                log.warn("设备原始数据时间序列不存在: {}", rawPath);
                return 0;
            }

            // 查询指定时间范围内该产品的生产数量
            int productionCount = calculateProductionFromRawData(rawPath, productName, startTime, endTime);

            log.info("基于原始数据查询产品生产数量完成: 设备={}, 产品={}, 时间范围=[{}, {}], 产量={}",
                    deviceCode, productName, startTime, endTime, productionCount);

            return productionCount;

        } catch (Exception e) {
            log.error("基于原始数据查询产品生产数量失败: 设备={}, 产品={}, 时间范围=[{}, {}]",
                    deviceCode, productName, startTime, endTime, e);
            return 0;
        }
    }

    /**
     * 从原始数据计算指定产品的生产数量
     * 分析产品切换点，计算每个生产段的数量差值
     */
    private int calculateProductionFromRawData(String rawPath, String productName, String startTime, String endTime)
            throws IoTDBConnectionException, StatementExecutionException {

        SessionDataSet dataSet = null;
        int totalProduction = 0;

        try {
            // 将时间字符串转换为时间戳
            long startTimestamp = convertTimeStringToTimestamp(startTime);
            long endTimestamp = convertTimeStringToTimestamp(endTime);

            // 查询时间范围内的所有原始数据，按时间排序
            String sql = String.format(
                "SELECT counter, current_product, status FROM %s WHERE time >= %d AND time <= %d ORDER BY time ASC",
                rawPath, startTimestamp, endTimestamp
            );

            log.info("执行原始数据查询: {}", sql);
            dataSet = iotSession.executeQueryStatement(sql);

            // 分析数据，找出该产品的生产段
            String currentProduct = null;
            int segmentStartCount = 0;
            boolean inProductionSegment = false;

            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                if (record.getFields().size() >= 3) {

                    String counterStr = record.getFields().get(0).getStringValue();
                    String product = record.getFields().get(1).getStringValue();
                    String statusStr = record.getFields().get(2).getStringValue();

                    if (counterStr == null || counterStr.equals("null")) continue;

                    int counter = (int) Double.parseDouble(counterStr);
                    int status = statusStr != null && !statusStr.equals("null") ? (int) Double.parseDouble(statusStr) : 0;

                    // 检查是否是目标产品且正在生产
                    boolean isTargetProduct = productName.equals(product);
                    boolean isProducing = status == 1;

                    if (isTargetProduct && isProducing) {
                        if (!inProductionSegment) {
                            // 开始新的生产段
                            segmentStartCount = counter;
                            inProductionSegment = true;
                            log.debug("产品 {} 开始生产段，起始计数: {}", productName, segmentStartCount);
                        }
                        // 继续当前生产段，不需要特殊处理
                    } else {
                        if (inProductionSegment) {
                            // 结束当前生产段
                            int segmentProduction = counter - segmentStartCount;
                            totalProduction += Math.max(0, segmentProduction);
                            inProductionSegment = false;
                            log.debug("产品 {} 结束生产段，结束计数: {}, 段产量: {}, 累计产量: {}",
                                    productName, counter, segmentProduction, totalProduction);
                        }
                    }
                }
            }

            // 如果查询结束时仍在生产该产品，需要计算最后一段
            if (inProductionSegment) {
                // 查询结束时间点的计数器值
                int endCount = queryLatestCounterValue(rawPath, endTimestamp);
                if (endCount > segmentStartCount) {
                    int segmentProduction = endCount - segmentStartCount;
                    totalProduction += segmentProduction;
                    log.debug("产品 {} 最后生产段，结束计数: {}, 段产量: {}, 最终累计产量: {}",
                            productName, endCount, segmentProduction, totalProduction);
                }
            }

            return totalProduction;

        } catch (Exception e) {
            log.error("从原始数据计算产品生产数量失败: 产品={}, 时间范围=[{}, {}]", productName, startTime, endTime, e);
            return 0;
        } finally {
            if (dataSet != null) {
                try {
                    dataSet.close();
                } catch (Exception e) {
                    log.warn("关闭数据集失败", e);
                }
            }
        }
    }

    /**
     * 查询指定时间点的最新计数器值
     */
    private int queryLatestCounterValue(String rawPath, long timestamp)
            throws IoTDBConnectionException, StatementExecutionException {

        SessionDataSet dataSet = null;
        try {
            String sql = String.format("SELECT last_value(counter) FROM %s WHERE time <= %d", rawPath, timestamp);
            dataSet = iotSession.executeQueryStatement(sql);

            if (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                if (record.getFields().size() >= 1) {
                    String counterStr = record.getFields().get(0).getStringValue();
                    if (counterStr != null && !counterStr.equals("null")) {
                        return (int) Double.parseDouble(counterStr);
                    }
                }
            }
            return 0;
        } finally {
            if (dataSet != null) {
                try {
                    dataSet.close();
                } catch (Exception e) {
                    log.warn("关闭数据集失败", e);
                }
            }
        }
    }

    /**
     * 查询产品在指定时间范围内的精确生产数量
     * 支持跨批次、部分批次的复杂时间范围查询
     *
     * @param enterprise 企业编码
     * @param deviceCode 设备编码
     * @param productName 产品名称
     * @param startTime 开始时间 (格式: yyyy-MM-dd HH:mm:ss)
     * @param endTime 结束时间 (格式: yyyy-MM-dd HH:mm:ss)
     * @return 生产数量
     * @throws IoTDBConnectionException IoTDB连接异常
     * @throws StatementExecutionException SQL执行异常
     */
    public int queryProductionCount(String enterprise, String deviceCode, String productName,
                                  String startTime, String endTime)
            throws IoTDBConnectionException, StatementExecutionException {

        if (StringUtils.isEmpty(enterprise) || StringUtils.isEmpty(deviceCode) || StringUtils.isEmpty(productName)) {
            return 0;
        }

        try {
            // 构建时间序列路径
            String productPath = normalizeProductKey(productName);
            String path = String.format("root.%s.%s.production.%s", enterprise, deviceCode, productPath);

            log.debug("开始查询产品生产数量: 设备={}, 产品={}, 时间范围=[{}, {}], 路径={}",
                    deviceCode, productName, startTime, endTime, path);

            // 首先检查该时间序列是否存在数据
            if (!checkTimeSeriesExists(path)) {
                log.warn("时间序列不存在或无数据: {}, 回退到MySQL查询", path);
                return 0;
            }

            // 使用累计产量差值方法计算时间段内的精确产量
            // 这是唯一能正确处理任意时间段查询的方法
            int totalCount = queryProductionCountByDifference(path, startTime, endTime);

            log.info("查询产品生产数量完成: 设备={}, 产品={}, 时间范围=[{}, {}], 总产量={}",
                    deviceCode, productName, startTime, endTime, totalCount);

            return totalCount;

        } catch (Exception e) {
            log.error("查询产品生产数量失败: 设备={}, 产品={}, 时间范围=[{}, {}]",
                    deviceCode, productName, startTime, endTime, e);
            return 0;
        }
    }

    /**
     * 通过累计产量差值计算时间段内的生产数量
     * 这是支持任意时间段查询的正确方法
     *
     * @param path 时间序列路径
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 生产数量
     */
    private int queryProductionCountByDifference(String path, String startTime, String endTime)
            throws IoTDBConnectionException, StatementExecutionException {

        try {
            // 1. 查询开始时间点的累计产量
            int startCount = queryCountAtTime(path, startTime, true);

            // 2. 查询结束时间点的累计产量
            int endCount = queryCountAtTime(path, endTime, false);

            // 3. 计算差值得到时间段内的产量
            int periodCount = Math.max(0, endCount - startCount);

            log.info("累计产量差值计算: 开始时间={}, 开始累计={}, 结束时间={}, 结束累计={}, 时间段产量={}",
                    startTime, startCount, endTime, endCount, periodCount);

            return periodCount;

        } catch (Exception e) {
            log.error("累计产量差值计算失败: 时间范围=[{}, {}]", startTime, endTime, e);
            return 0;
        }
    }

    /**
     * 检查快照时间序列是否存在数据
     *
     * @param snapshotPath 快照路径
     * @param productKey 产品键
     * @return 是否存在数据
     */
    private boolean checkSnapshotTimeSeriesExists(String snapshotPath, String productKey) {
        SessionDataSet dataSet = null;
        try {
            // 检查产品状态时间序列是否存在
            String statusTimeSeriesPath = String.format("%s.%s_status", snapshotPath, productKey);
            String sql = String.format("SHOW TIMESERIES %s", statusTimeSeriesPath);
            log.info("检查快照时间序列存在性SQL: {}", sql);
            dataSet = iotSession.executeQueryStatement(sql);

            boolean exists = dataSet.hasNext();
            log.info("检查快照时间序列 {} 是否存在: {}", statusTimeSeriesPath, exists);

            // 如果不存在，尝试查询所有相关的快照时间序列
            if (!exists) {
                try {
                    String showAllSql = String.format("SHOW TIMESERIES %s.*", snapshotPath);
                    log.info("查询相关快照时间序列: {}", showAllSql);

                    SessionDataSet allDataSet = iotSession.executeQueryStatement(showAllSql);
                    int count = 0;
                    while (allDataSet.hasNext()) {
                        RowRecord record = allDataSet.next();
                        log.info("找到快照时间序列: {}", record.getFields().get(0).getStringValue());
                        count++;
                    }
                    log.info("共找到 {} 个相关快照时间序列", count);
                    allDataSet.close();
                } catch (Exception ex) {
                    log.warn("查询相关快照时间序列失败", ex);
                }
            }

            return exists;

        } catch (Exception e) {
            log.warn("检查快照时间序列存在性失败: {}, 错误: {}", snapshotPath, e.getMessage());
            return false;
        } finally {
            if (dataSet != null) {
                try {
                    dataSet.close();
                } catch (Exception e) {
                    log.warn("关闭数据集失败", e);
                }
            }
        }
    }

    /**
     * 查询指定时间范围内的批次产量
     *
     * @param path 时间序列路径
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 批次产量
     */
    private int queryBatchCountInTimeRange(String path, String startTime, String endTime)
            throws IoTDBConnectionException, StatementExecutionException {

        SessionDataSet dataSet = null;
        try {
            // 将时间字符串转换为时间戳
            long startTimestamp = convertTimeStringToTimestamp(startTime);
            long endTimestamp = convertTimeStringToTimestamp(endTime);

            // 查询指定时间范围内的最新批次产量
            String sql = String.format(
                "SELECT last_value(batch_count) FROM %s WHERE time >= %d AND time <= %d",
                path, startTimestamp, endTimestamp
            );

            log.info("执行IoTDB批次产量查询: {}", sql);
            dataSet = iotSession.executeQueryStatement(sql);

            if (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                if (record.getFields().size() >= 1) {
                    String batchCountStr = record.getFields().get(0).getStringValue();
                    if (batchCountStr != null && !batchCountStr.equals("null")) {
                        try {
                            // 处理浮点数格式的数值
                            double batchCountDouble = Double.parseDouble(batchCountStr);
                            int batchCount = (int) batchCountDouble;
                            log.info("查询到批次产量: {} (原始值: {}, 时间范围: [{}, {}])",
                                    batchCount, batchCountStr, startTime, endTime);
                            return batchCount;
                        } catch (NumberFormatException e) {
                            log.error("解析批次产量数值失败: {} (时间范围: [{}, {}])",
                                    batchCountStr, startTime, endTime, e);
                            return 0;
                        }
                    }
                }
            }

            log.warn("未查询到批次产量数据 (时间范围: [{}, {}])", startTime, endTime);
            return 0;

        } catch (Exception e) {
            log.error("查询批次产量失败: 时间范围=[{}, {}]", startTime, endTime, e);
            return 0;
        } finally {
            if (dataSet != null) {
                try {
                    dataSet.close();
                } catch (Exception e) {
                    log.warn("关闭数据集失败", e);
                }
            }
        }
    }

    /**
     * 查询指定时间点的累计产量
     *
     * @param path 时间序列路径
     * @param timePoint 时间点 (格式: yyyy-MM-dd HH:mm:ss)
     * @param isStartTime 是否为开始时间（影响查询策略）
     * @return 累计产量
     */
    private int queryCountAtTime(String path, String timePoint, boolean isStartTime)
            throws IoTDBConnectionException, StatementExecutionException {

        SessionDataSet dataSet = null;
        try {
            // 将时间字符串转换为时间戳
            long timestamp = convertTimeStringToTimestamp(timePoint);

            // 查询指定时间点之前的最后一个累计产量值
            String sql = String.format("SELECT last_value(count) FROM %s WHERE time <= %d", path, timestamp);

            log.info("执行IoTDB查询: {}", sql);
            dataSet = iotSession.executeQueryStatement(sql);

            if (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                if (record.getFields().size() >= 1) {
                    String countStr = record.getFields().get(0).getStringValue();
                    if (countStr != null && !countStr.equals("null")) {
                        try {
                            // 处理浮点数格式的数值，先转换为double再转为int
                            double countDouble = Double.parseDouble(countStr);
                            int count = (int) countDouble;
                            log.info("查询到累计产量: {} (原始值: {}, 时间点: {})", count, countStr, timePoint);
                            return count;
                        } catch (NumberFormatException e) {
                            log.error("解析累计产量数值失败: {} (时间点: {})", countStr, timePoint, e);
                            return 0;
                        }
                    }
                }
            }

            log.warn("未查询到累计产量数据 (时间点: {})", timePoint);
            return 0;

        } catch (Exception e) {
            log.error("查询时间点 {} 的累计产量失败: {}", timePoint, e.getMessage(), e);
            return 0;
        } finally {
            if (dataSet != null) {
                dataSet.close();
            }
        }
    }

    /**
     * 将时间字符串转换为时间戳
     *
     * @param timeString 时间字符串 (格式: yyyy-MM-dd HH:mm:ss)
     * @return 时间戳 (毫秒)
     */
    private long convertTimeStringToTimestamp(String timeString) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = sdf.parse(timeString);
            return date.getTime();
        } catch (Exception e) {
            log.error("时间字符串转换失败: {}", timeString, e);
            return System.currentTimeMillis();
        }
    }

    /**
     * 检查时间序列是否存在数据
     *
     * @param path 时间序列路径
     * @return 是否存在数据
     */
    private boolean checkTimeSeriesExists(String path) {
        SessionDataSet dataSet = null;
        try {
            // 查询该时间序列是否存在
            String sql = String.format("SHOW TIMESERIES %s.count", path);
            log.debug("检查时间序列存在性SQL: {}", sql);
            dataSet = iotSession.executeQueryStatement(sql);

            boolean exists = dataSet.hasNext();
            log.info("检查时间序列 {} 是否存在: {}", path, exists);

            // 如果不存在，尝试查询所有相关的时间序列
            if (!exists) {
                try {
                    String[] pathParts = path.split("\\.");
                    if (pathParts.length >= 4) {
                        String basePath = String.join(".", pathParts[0], pathParts[1], pathParts[2], pathParts[3]);
                        String showAllSql = String.format("SHOW TIMESERIES %s.*", basePath);
                        log.info("查询相关时间序列: {}", showAllSql);

                        SessionDataSet allDataSet = iotSession.executeQueryStatement(showAllSql);
                        int count = 0;
                        while (allDataSet.hasNext()) {
                            RowRecord record = allDataSet.next();
                            log.info("找到时间序列: {}", record.getFields().get(0).getStringValue());
                            count++;
                        }
                        log.info("共找到 {} 个相关时间序列", count);
                        allDataSet.close();
                    }
                } catch (Exception ex) {
                    log.warn("查询相关时间序列失败", ex);
                }
            }

            return exists;

        } catch (Exception e) {
            log.warn("检查时间序列存在性失败: {}, 错误: {}", path, e.getMessage());
            return false;
        } finally {
            if (dataSet != null) {
                try {
                    dataSet.close();
                } catch (Exception e) {
                    log.warn("关闭数据集失败", e);
                }
            }
        }
    }

    /**
     * 查询设备在指定时间范围内所有产品的生产汇总数据
     *
     * @param enterprise 企业编码
     * @param deviceCode 设备编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 产品生产汇总数据Map，key为产品名称，value为生产数量
     * @throws IoTDBConnectionException IoTDB连接异常
     * @throws StatementExecutionException SQL执行异常
     */
    public Map<String, Integer> queryDeviceProductionSummary(String enterprise, String deviceCode,
                                                           String startTime, String endTime)
            throws IoTDBConnectionException, StatementExecutionException {

        Map<String, Integer> result = new HashMap<>();

        if (StringUtils.isEmpty(enterprise) || StringUtils.isEmpty(deviceCode)) {
            return result;
        }

        SessionDataSet dataSet = null;
        try {
            // 查询设备下所有产品的生产数据
            String basePath = String.format("root.%s.%s.production", enterprise, deviceCode);
            String sql = String.format(
                "SHOW TIMESERIES %s.*.batch_count", basePath
            );

            dataSet = iotSession.executeQueryStatement(sql);

            // 获取所有产品的时间序列路径
            List<String> productPaths = new ArrayList<>();
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                if (record.getFields().size() > 0) {
                    String timeseriesPath = record.getFields().get(0).getStringValue();
                    productPaths.add(timeseriesPath);
                }
            }

            // 为每个产品查询生产数量
            for (String productPath : productPaths) {
                // 从路径中提取产品名称
                String[] pathParts = productPath.split("\\.");
                if (pathParts.length >= 5) {
                    String productKey = pathParts[4];
                    String productName = denormalizeProductKey(productKey);
                    int count = queryProductionCount(enterprise, deviceCode, productName, startTime, endTime);
                    if (count > 0) {
                        result.put(productName, count);
                    }
                }
            }

        } catch (Exception e) {
            log.error("查询设备产品生产汇总失败: 设备={}, 时间范围=[{}, {}]",
                    deviceCode, startTime, endTime, e);
        } finally {
            if (dataSet != null) {
                dataSet.close();
            }
        }

        return result;
    }

    /**
     * 查询所有产品在指定时间范围内的生产汇总数据
     *
     * @param enterprise 企业编码
     * @param productName 产品名称过滤（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 产品生产汇总数据列表
     * @throws IoTDBConnectionException IoTDB连接异常
     * @throws StatementExecutionException SQL执行异常
     */
    public List<ProductionSummaryData> queryAllProductionSummary(String enterprise, String productName,
                                                               String startTime, String endTime)
            throws IoTDBConnectionException, StatementExecutionException {

        List<ProductionSummaryData> result = new ArrayList<>();

        if (StringUtils.isEmpty(enterprise)) {
            return result;
        }

        SessionDataSet dataSet = null;
        try {
            // 构建查询路径
            String basePath = String.format("root.%s", enterprise);
            String timeseriesPattern = productName != null && !productName.trim().isEmpty()
                ? String.format("%s.*.production.%s.batch_count", basePath, normalizeProductKey(productName))
                : String.format("%s.*.production.*.batch_count", basePath);

            // 查询所有匹配的时间序列
            String sql = String.format("SHOW TIMESERIES %s", timeseriesPattern);
            dataSet = iotSession.executeQueryStatement(sql);

            // 收集所有设备和产品的组合
            Map<String, Set<String>> deviceProductMap = new HashMap<>();
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                if (record.getFields().size() > 0) {
                    String timeseriesPath = record.getFields().get(0).getStringValue();
                    String[] pathParts = timeseriesPath.split("\\.");
                    if (pathParts.length >= 5) {
                        String deviceCode = pathParts[2];
                        String productKey = pathParts[4];
                        String product = denormalizeProductKey(productKey);

                        deviceProductMap.computeIfAbsent(product, k -> new HashSet<>()).add(deviceCode);
                    }
                }
            }

            // 为每个产品汇总生产数据
            for (Map.Entry<String, Set<String>> entry : deviceProductMap.entrySet()) {
                String product = entry.getKey();
                Set<String> devices = entry.getValue();

                int totalCount = 0;
                int deviceCount = devices.size();

                // 汇总所有设备的生产数量
                for (String device : devices) {
                    int count = queryProductionCount(enterprise, device, product, startTime, endTime);
                    totalCount += count;
                }

                if (totalCount > 0 || deviceCount > 0) {
                    ProductionSummaryData summaryData = new ProductionSummaryData();
                    summaryData.setProductName(product);
                    summaryData.setDeviceCount(deviceCount);
                    summaryData.setProductionCount(totalCount);
                    summaryData.setDeviceCodes(String.join(",", devices));
                    result.add(summaryData);
                }
            }

        } catch (Exception e) {
            log.error("查询所有产品生产汇总失败: 产品名称={}, 时间范围=[{}, {}]",
                    productName, startTime, endTime, e);
        } finally {
            if (dataSet != null) {
                dataSet.close();
            }
        }

        return result;
    }

    /**
     * 产品生产汇总数据类
     */
    public static class ProductionSummaryData {
        private String productName;
        private Integer deviceCount;
        private Integer productionCount;
        private String deviceCodes;

        // Getters and Setters
        public String getProductName() { return productName; }
        public void setProductName(String productName) { this.productName = productName; }

        public Integer getDeviceCount() { return deviceCount; }
        public void setDeviceCount(Integer deviceCount) { this.deviceCount = deviceCount; }

        public Integer getProductionCount() { return productionCount; }
        public void setProductionCount(Integer productionCount) { this.productionCount = productionCount; }

        public String getDeviceCodes() { return deviceCodes; }
        public void setDeviceCodes(String deviceCodes) { this.deviceCodes = deviceCodes; }
    }

    /**
     * 查询指定产品的生产明细数据
     *
     * @param enterprise 企业编码
     * @param productName 产品名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 产品生产明细数据列表
     * @throws IoTDBConnectionException IoTDB连接异常
     * @throws StatementExecutionException SQL执行异常
     */
    public List<ProductionDetailData> queryProductionDetail(String enterprise, String productName,
                                                          String startTime, String endTime)
            throws IoTDBConnectionException, StatementExecutionException {

        List<ProductionDetailData> result = new ArrayList<>();

        if (StringUtils.isEmpty(enterprise) || StringUtils.isEmpty(productName)) {
            return result;
        }

        SessionDataSet dataSet = null;
        try {
            // 构建查询路径
            String basePath = String.format("root.%s", enterprise);
            String productPath = normalizeProductKey(productName);
            String timeseriesPattern = String.format("%s.*.production.%s.batch_count", basePath, productPath);

            // 查询所有匹配的时间序列
            String sql = String.format("SHOW TIMESERIES %s", timeseriesPattern);
            dataSet = iotSession.executeQueryStatement(sql);

            // 收集所有设备
            List<String> devices = new ArrayList<>();
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                if (record.getFields().size() > 0) {
                    String timeseriesPath = record.getFields().get(0).getStringValue();
                    String[] pathParts = timeseriesPath.split("\\.");
                    if (pathParts.length >= 3) {
                        String deviceCode = pathParts[2];
                        devices.add(deviceCode);
                    }
                }
            }

            // 为每个设备查询生产明细
            for (String deviceCode : devices) {
                int count = queryProductionCount(enterprise, deviceCode, productName, startTime, endTime);
                if (count > 0) {
                    ProductionDetailData detailData = new ProductionDetailData();
                    detailData.setDeviceCode(deviceCode);
                    detailData.setProductName(productName);
                    detailData.setProductionCount(count);
                    detailData.setStartTime(startTime);
                    detailData.setEndTime(endTime);
                    result.add(detailData);
                }
            }

        } catch (Exception e) {
            log.error("查询产品生产明细失败: 产品名称={}, 时间范围=[{}, {}]",
                    productName, startTime, endTime, e);
        } finally {
            if (dataSet != null) {
                dataSet.close();
            }
        }

        return result;
    }

    /**
     * 调试方法：查询IoTDB中所有的生产相关时间序列
     *
     * @param enterprise 企业编码
     * @return 时间序列列表
     */
    public List<String> debugQueryAllProductionTimeSeries(String enterprise) {
        List<String> timeSeriesList = new ArrayList<>();
        SessionDataSet dataSet = null;

        try {
            String sql = String.format("SHOW TIMESERIES root.%s.*.production.*", enterprise);
            log.info("调试查询所有生产时间序列: {}", sql);

            dataSet = iotSession.executeQueryStatement(sql);

            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                if (record.getFields().size() > 0) {
                    String timeSeries = record.getFields().get(0).getStringValue();
                    timeSeriesList.add(timeSeries);
                    log.info("找到生产时间序列: {}", timeSeries);
                }
            }

            log.info("共找到 {} 个生产相关时间序列", timeSeriesList.size());

        } catch (Exception e) {
            log.error("调试查询生产时间序列失败", e);
        } finally {
            if (dataSet != null) {
                try {
                    dataSet.close();
                } catch (Exception e) {
                    log.warn("关闭数据集失败", e);
                }
            }
        }

        return timeSeriesList;
    }

    /**
     * 产品生产明细数据类
     */
    public static class ProductionDetailData {
        private String deviceCode;
        private String productName;
        private Integer productionCount;
        private String startTime;
        private String endTime;

        // Getters and Setters
        public String getDeviceCode() { return deviceCode; }
        public void setDeviceCode(String deviceCode) { this.deviceCode = deviceCode; }

        public String getProductName() { return productName; }
        public void setProductName(String productName) { this.productName = productName; }

        public Integer getProductionCount() { return productionCount; }
        public void setProductionCount(Integer productionCount) { this.productionCount = productionCount; }

        public String getStartTime() { return startTime; }
        public void setStartTime(String startTime) { this.startTime = startTime; }

        public String getEndTime() { return endTime; }
        public void setEndTime(String endTime) { this.endTime = endTime; }
    }

    /**
     * @param enterprise 企业编码
     * @param device     设备编码
     * @param code       设备属性
     * @param start      开始时间
     * @param end        结束时间
     * @return java.util.List<com.ruoyi.zhengheiot.domain.PeriodMaintainData>
     * <AUTHOR>
     * @remark 查询 设备每小时的电量，然后查询属于哪个时段的数据并合并添加
     * @date 2025/3/19 10:30
     */
    public List<PeriodMaintainData> queryIotEquipmentPeriodMaintain(String enterprise, String device, String code, String start, String end) throws IoTDBConnectionException, StatementExecutionException {
        SessionDataSet dataSet = null;
        ArrayList<PeriodMaintainData> periodMaintainDataArrayList = new ArrayList<>();
        double operTime = 0;
        try {
            String aim = "root." + enterprise + "." + device;
            String sqlString = "select (MAX_VALUE(TotalPower) - MIN_VALUE(TotalPower)) AS hourPower from " + aim + " GROUP BY([" + start + ", " + end + "),1h) having (MAX_VALUE(TotalPower) - MIN_VALUE(TotalPower)) != 0 and (MAX_VALUE(TotalPower) - MIN_VALUE(TotalPower)) IS NOT NULL";
            dataSet = iotSession.executeQueryStatement(sqlString);
            List<String> measurements = dataSet.getColumnNames();
            //measurements.remove("Time");
            while (dataSet.hasNext()) {
                RowRecord record = dataSet.next();
                PeriodMaintainData periodMaintainData = new PeriodMaintainData();
                SimpleDateFormat dateFormat = new SimpleDateFormat("HH");
                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
                //获取小时数
                periodMaintainData.setPeriod(String.valueOf(DateUtil.hour(new Date(record.getTimestamp()), true) + 1));
                //获取电量值
                Double aDouble = Double.valueOf(record.getFields().get(0).getStringValue());
                periodMaintainData.setVal(Double.parseDouble(String.format("%.3f", aDouble)));
                periodMaintainData.setFiledName(NumericFieldCasts.NumericFieldString(String.valueOf(DateUtil.month(new Date(record.getTimestamp())) + 1)));
                //获取年
                periodMaintainData.setYearTime(String.valueOf(DateUtil.year(new Date(record.getTimestamp()))));
                periodMaintainDataArrayList.add(periodMaintainData);
            }
            return periodMaintainDataArrayList;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
//            iotSession.(dataSet);
        }
        return periodMaintainDataArrayList;
    }


    /**
     * 计算电量
     * 该方法通过解析给定的JSONObject中的数据来计算能量值它首先查找列表中的第一个非负数作为起始功率，
     * 然后查找最后一个大于起始功率的非负数作为结束功率能量值被计算为结束功率减去起始功率
     *
     * @param totalPower 包含功率数据的JSONObject对象
     * @return 计算得到的能量值，如果数据不满足条件，则可能返回0
     */
    public int getTotalPower(cn.hutool.json.JSONObject totalPower) {
        double beginPower = 0;
        double endPower = 0;
        if (totalPower.get("list") != null) {
            final cn.hutool.json.JSONArray data = totalPower.getJSONArray("list");
            if (data != null && data.size() > 0) {
                for (int j = 0; j < data.size(); j++) {
                    final cn.hutool.json.JSONObject jsonObject = data.getJSONObject(j);
                    if (jsonObject.get("val") != null && !"null".equals(jsonObject.get("val") + "") && !"/".equals(jsonObject.get("val") + "")) {
                        try {
                            double value = Double.parseDouble(jsonObject.get("val") + "");
                            if (value >= 0) {
                                beginPower = value;
                                break;
                            }
                        } catch (NumberFormatException e) {
//                            throw new RuntimeException(e);
                        }
                    }
                }
                for (int j = data.size() - 1; j >= 0; j--) {
                    final cn.hutool.json.JSONObject jsonObject = data.getJSONObject(j);
                    if (jsonObject.get("val") != null && !"null".equals(jsonObject.get("val") + "") && !"/".equals(jsonObject.get("val") + "")) {
                        try {
                            double value = Double.parseDouble(jsonObject.get("val") + "");
                            if (value > 0 && value >= beginPower) {
                                endPower = value;
                                break;
                            }
                        } catch (NumberFormatException e) {
//                                throw new RuntimeException(e);
                        }
                    }
                }
            }
        }
        return ((int) (endPower - beginPower));
    }

//    public List<HistoryData> listData(String enterprise, String device, String code, String start, String end) throws IoTDBConnectionException, StatementExecutionException {
//        List<HistoryData> result = new ArrayList<>();
//        SessionDataSet dataSet = null;
//        try {
//            String aim = "root." + enterprise + "." + device;
//            if (ObjectUtils.isEmpty(start) || ObjectUtils.isEmpty(end)) {
//                dataSet = iotSession.executeQueryStatement("select " + code + " from " + aim + " order by time desc limit 1");
//            } else {
//                dataSet = iotSession.executeQueryStatement("select last_value(" + code + ") from " + aim + " GROUP BY([" + start + ", " + end + "),1m) " + " fill(float[PREVIOUS])");
//            }
//            List<String> measurements = dataSet.getColumnNames();
//            measurements.remove("Time");
//            while (dataSet.hasNext()) {
//                RowRecord record = dataSet.next();
//                HistoryData dataVO = new HistoryData();
//                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
//                dataVO.setTime(dateFormat.format(new Date(record.getTimestamp())));
//                dataVO.setVal(record.getFields().get(0).getStringValue());
//                dataVO.setDevice(device);
//                result.add(dataVO);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
////            iotSession.(dataSet);
//        }
//        return result;
//    }
//
//    public JSONArray listDataHistory(String enterprise, String deviceStr, String code, String start, String end) throws IoTDBConnectionException, StatementExecutionException {
//        JSONArray result = new JSONArray();
//        SessionDataSet dataSet = null;
//        try {
//            String aim = "root." + enterprise + "." + deviceStr;
//            if (ObjectUtils.isEmpty(start) || ObjectUtils.isEmpty(end)) {
//                dataSet = iotSession.executeQueryStatement("select " + code + " from " + aim + " order by time desc limit 1");
//            } else {
//                //dataSet = iotSession.executeQueryStatement("select " + code + " from " + aim +  " where time >= " + start + " and time <" + end +" fill(PREVIOUS,2m)");
//                dataSet = iotSession.executeQueryStatement("select last_value(" + code + ") from " + aim + " GROUP BY([" + start + ", " + end + "),1m) fill(PREVIOUS)");
//
//            }
//            List<String> measurements = dataSet.getColumnNames();
//            measurements.remove("Time");
//            while (dataSet.hasNext()) {
//                RowRecord record = dataSet.next();
//                JSONObject object = new JSONObject();
//                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
//                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
//                for (int i = 0; i < measurements.size(); i++) {
//                    object.put(measurements.get(i).substring(measurements.get(i).lastIndexOf(".") + 1), record.getFields().get(i).getStringValue());
//                }
//                result.add(object);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
////            iotSession.(dataSet);
//        }
//        return result;
//    }
//

//
//    public List<HistoryData> getDataListWithoutFill(String enterprise, String device, String code, String start, String end) throws IoTDBConnectionException, StatementExecutionException {
//        List<HistoryData> result = new ArrayList<>();
//        SessionDataSet dataSet = null;
//        try {
//            String aim = "root." + enterprise + "." + device;
//            dataSet = iotSession.executeQueryStatement("select " + code + " from " + aim + " where time >= " + start + " and time <" + end);
//            List<String> measurements = dataSet.getColumnNames();
//            measurements.remove("Time");
//            while (dataSet.hasNext()) {
//                RowRecord record = dataSet.next();
//                HistoryData dataVO = new HistoryData();
//                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
//                dataVO.setTime(dateFormat.format(new Date(record.getTimestamp())));
//                dataVO.setVal(record.getFields().get(0).getStringValue());
//                result.add(dataVO);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
////            iotSession.closeResultSet(dataSet);
//        }
//        return result;
//    }
//


//    /**
//     * 获取某时间的OEE信息
//     */
//    public JSONObject getRealOEE(String enterprise, String device, String time) {
//        JSONObject result = new JSONObject();
//        SessionDataSet dataSet = null;
//        try {
//            if (StrUtil.isEmpty(device)) {
//                device = "*";
//            }
//            String aim = "root." + enterprise + ".*";
//            dataSet = iotSession.executeQueryStatement("select * from " + aim + " where time = " + time + "  fill(float[PREVIOUS])");
//            List<String> measurements = dataSet.getColumnNames();
//            measurements.remove("Time");
//            while (dataSet.hasNext()) {
//                RowRecord record = dataSet.next();
//                for (int i = 0; i < measurements.size(); i++) {
////                    System.out.println(measurements.get(i) + "==" + record.getFields().get(i).getStringValue());
//                    if (measurements.get(i).contains("OEE")) {
//                        result.put(measurements.get(i).replace("root.4bd8e784c72b47d8bc90faaa6b9364a8.", ""), record.getFields().get(i).getStringValue());
//                    }
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return result;
//    }
//
//    public JSONArray getMaxAndMinByHour(String enterprise, String device, String code, String start, String end) {
//        JSONArray result = new JSONArray();
//        SessionDataSet dataSet = null;
//        try {
//            String aim = "root." + enterprise + "." + device;
//            dataSet = iotSession.executeQueryStatement("select MAX_VALUE(" + code + ") as max,MIN_VALUE(" + code + ") as min from " + aim + " where " + code + " > 0 group by ([" + start + ", " + end + "),1h)");
//            List<String> measurements = dataSet.getColumnNames();
//            measurements.remove("Time");
//            while (dataSet.hasNext()) {
//                RowRecord record = dataSet.next();
//                JSONObject object = new JSONObject();
//                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH");
//                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
//                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
//                for (int i = 0; i < measurements.size(); i++) {
//                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
//                }
//                result.add(object);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
////            iotSession.closeResultSet(dataSet);
//        }
//        return result;
//    }
//
//    public JSONArray getAllMaxAndMinByHour(String enterprise, String deviceStr, String code, String start, String end) {
//        JSONArray result = new JSONArray();
//        SessionDataSet dataSet = null;
//        try {
//            String aim = "root." + enterprise + "." + deviceStr;
//            dataSet = iotSession.executeQueryStatement("select MAX_VALUE(" + code + ") as max,MIN_VALUE(" + code + ") as min from " + aim + " where " + code + " > 0  group by ([" + start + ", " + end + "),1h) align by device");
//            List<String> measurements = dataSet.getColumnNames();
//            measurements.remove("Time");
//            while (dataSet.hasNext()) {
//                RowRecord record = dataSet.next();
//                JSONObject object = new JSONObject();
//                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH");
//                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
//                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
//                for (int i = 0; i < measurements.size(); i++) {
//                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
//                }
//                result.add(object);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
////            iotSession.closeResultSet(dataSet);
//        }
//        return result;
//    }
//
//    public JSONArray getMaxAndMinByDay(String enterprise, String device, String code, String start, String end) {
//        JSONArray result = new JSONArray();
//        SessionDataSet dataSet = null;
//        try {
//            String aim = "root." + enterprise + "." + device;
//            dataSet = iotSession.executeQueryStatement("select MAX_VALUE(" + code + ") as max,MIN_VALUE(" + code + ") as min from " + aim + " where " + code + " > 0 group by ([" + start + ", " + end + "),1d)");
//            List<String> measurements = dataSet.getColumnNames();
//            measurements.remove("Time");
//            while (dataSet.hasNext()) {
//                RowRecord record = dataSet.next();
//                JSONObject object = new JSONObject();
//                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
//                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
//                for (int i = 0; i < measurements.size(); i++) {
//                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
//                }
//                result.add(object);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
////            iotSession.closeResultSet(dataSet);
//        }
//        return result;
//    }
//
//    public JSONArray getMaxAndMin(String enterprise, String device, String code, String start, String end) {
//        JSONArray result = new JSONArray();
//        SessionDataSet dataSet = null;
//        try {
//            String aim = "root." + enterprise + "." + device;
//            dataSet = iotSession.executeQueryStatement("select MAX_VALUE(" + code + ") as max,MIN_VALUE(" + code + ") as min from " + aim + " where " + code + " > 0 and time >= " + start + " and time <" + end + " align by device");
//            List<String> measurements = dataSet.getColumnNames();
//            measurements.remove("Time");
//            while (dataSet.hasNext()) {
//                RowRecord record = dataSet.next();
//                JSONObject object = new JSONObject();
//                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
//                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
//                for (int i = 0; i < measurements.size(); i++) {
//                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
//                }
//                result.add(object);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
////            iotSession.closeResultSet(dataSet);
//        }
//        return result;
//    }
//
//    public JSONArray getFirstAndLast(String enterprise, String device, String code, String start, String end) {
//        JSONArray result = new JSONArray();
//        SessionDataSet dataSet = null;
//        try {
//            String aim = "root." + enterprise + "." + device;
//            dataSet = iotSession.executeQueryStatement("select FIRST_VALUE(" + code + ") as max,LAST_VALUE(" + code + ") as min from " + aim + " group by ([" + start + ", " + end + "),1d) align by device");
//            List<String> measurements = dataSet.getColumnNames();
//            measurements.remove("Time");
//            while (dataSet.hasNext()) {
//                RowRecord record = dataSet.next();
//                JSONObject object = new JSONObject();
//                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh-mm-ss");
//                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
//                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
//                for (int i = 0; i < measurements.size(); i++) {
//                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
//                }
//                result.add(object);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
////            iotSession.closeResultSet(dataSet);
//        }
//        return result;
//    }
//
//    public JSONArray getAllMaxAndMinByDay(String enterprise, String device, String code, String start, String end) {
//        JSONArray result = new JSONArray();
//        SessionDataSet dataSet = null;
//        try {
//            String aim = "root." + enterprise + "." + device;
//            dataSet = iotSession.executeQueryStatement("select MAX_VALUE(" + code + ") as max,MIN_VALUE(" + code + ") as min from " + aim + " group by ([" + start + ", " + end + "),1d) align by device");
//            List<String> measurements = dataSet.getColumnNames();
//            measurements.remove("Time");
//            while (dataSet.hasNext()) {
//                RowRecord record = dataSet.next();
//                JSONObject object = new JSONObject();
//                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
//                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
//                for (int i = 0; i < measurements.size(); i++) {
//                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
//                }
//                result.add(object);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
////            iotSession.closeResultSet(dataSet);
//        }
//        return result;
//    }
//
//    public JSONArray getMaxAndMinByShift(String enterprise, String device, String code, String start, String end, String sharding) {
//        JSONArray result = new JSONArray();
//        SessionDataSet dataSet = null;
//        try {
//            String aim = "root." + enterprise + "." + device;
//            dataSet = iotSession.executeQueryStatement("select MAX_VALUE(" + code + ") as max,MIN_VALUE(" + code + ") as min from " + aim + " where " + code + " > 0 group by ([" + start + ", " + end + ")," + sharding + ",1d) align by device");
//            List<String> measurements = dataSet.getColumnNames();
//            measurements.remove("Time");
//            while (dataSet.hasNext()) {
//                RowRecord record = dataSet.next();
//                JSONObject object = new JSONObject();
//                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
//                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
//                for (int i = 0; i < measurements.size(); i++) {
//                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
//                }
//                result.add(object);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
////            iotSession.closeResultSet(dataSet);
//        }
//        return result;
//    }
//
//    public Double getTagCount(String enterprise, String device, String code, String start, String end) {
//        Double result = 0d;
//        Map<String, Double> run = new HashMap<>();
//        Integer count = 0;
//        SessionDataSet dataSet = null;
//        try {
//            String aim = "root." + enterprise + "." + device;
////            String aim = "root." + enterprise + ".*";
//            dataSet = iotSession.executeQueryStatement("select count(" + code + ") as c  from " + aim + " where " + code + "= '1' and time>= " + start + " and time <= " + end + " align by device");
//            List<String> measurements = dataSet.getColumnNames();
//            measurements.remove("Time");
//            while (dataSet.hasNext()) {
////                count ++;
//                RowRecord record = dataSet.next();
////                System.out.println(JSONObject.toJSONString(record));
////                run.put(record.getFields().get(0).getStringValue(),Convert.toDouble(record.getFields().get(1).getStringValue()));
//                for (int i = 0; i < measurements.size(); i++) {
//                    if (measurements.get(i).equalsIgnoreCase("c")) {
//                        count = count + Convert.toInt(record.getFields().get(i).getStringValue());
//                    }
//                }
//            }
//            dataSet = iotSession.executeQueryStatement("select count(" + code + ") as c  from " + aim + " where " + code + "= '0' and time>= " + start + " and time <= " + end + " align by device");
//            measurements.remove("Time");
//            while (dataSet.hasNext()) {
////                count ++;
//                RowRecord record = dataSet.next();
////                System.out.println(JSONObject.toJSONString(record));
////                run.put(record.getFields().get(0).getStringValue(),Convert.toDouble(record.getFields().get(1).getStringValue()));
//                for (int i = 0; i < measurements.size(); i++) {
//                    if (measurements.get(i).equalsIgnoreCase("c")) {
//                        if (count == 0) {
//                            result = 0d;
//                        } else {
//                            result = count / (Convert.toDouble(record.getFields().get(i).getStringValue()) + count);
//                        }
//                    }
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            return null;
//        } finally {
////            iotSession.closeResultSet(dataSet);
//        }
//        return result * DateUtil.between(DateUtil.parseDateTime(start), DateUtil.parseDateTime(end), DateUnit.HOUR);
//    }
//
//    public JSONArray getMaxAndMinByShiftV2(String enterprise, String device, String code, String start, String end, String startTime, String endTime, String sharding) {
//        JSONArray result = new JSONArray();
//        SessionDataSet dataSet = null;
//        try {
//            String aim = "root." + enterprise + "." + device;
//            dataSet = iotSession.executeQueryStatement("select MAX_TIME(" + code + ") as maxTime,MIN_TIME(" + code + ") as minTime,MAX_VALUE(" + code + ") as max,MIN_VALUE(" + code + ") as min from " + aim + " where time>=" + startTime + " and time <= " + endTime + " group by ([" + start + ", " + end + ")," + sharding + ",1d) align by device");
//            List<String> measurements = dataSet.getColumnNames();
//            measurements.remove("Time");
//            while (dataSet.hasNext()) {
//                RowRecord record = dataSet.next();
//                JSONObject object = new JSONObject();
//                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
//                object.put("time", dateFormat.format(new Date(record.getTimestamp())));
//                for (int i = 0; i < measurements.size(); i++) {
//                    object.put(measurements.get(i), record.getFields().get(i).getStringValue());
//                }
//                result.add(object);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
////            iotSession.closeResultSet(dataSet);
//        }
//        return result;
//    }
//
//    public List<HistoryData> listData1(String enterprise, String device, String code, String start, String end) throws IoTDBConnectionException, StatementExecutionException {
//        List<HistoryData> result = new ArrayList<>();
//        SessionDataSet dataSet = null;
//        try {
//            String aim = "root." + enterprise + "." + device;
//            if (ObjectUtils.isEmpty(start) || ObjectUtils.isEmpty(end)) {
//                dataSet = iotSession.executeQueryStatement("select " + code + " from " + aim + " order by time desc limit 1");
//            } else {
//                dataSet = iotSession.executeQueryStatement("select last_value(" + code + ") from " + aim + " GROUP BY([" + start + ", " + end + "),1m) " + " fill(float[PREVIOUS])");
//            }
//            List<String> measurements = dataSet.getColumnNames();
//            measurements.remove("Time");
//            while (dataSet.hasNext()) {
//                RowRecord record = dataSet.next();
//                HistoryData dataVO = new HistoryData();
//                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                dateFormat.setTimeZone(TimeZone.getTimeZone("Etc/GMT-8"));
//                dataVO.setTime(dateFormat.format(new Date(record.getTimestamp())));
//                dataVO.setVal(record.getFields().get(0).getStringValue());
//                dataVO.setDevice(device);
//                result.add(dataVO);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
////            iotSession.(dataSet);
//        }
//        return result;
//    }
}
