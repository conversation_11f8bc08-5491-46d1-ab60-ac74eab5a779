package com.ruoyi.zhenghe.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.zhenghe.domain.ZhPeriod;
import com.ruoyi.zhenghe.service.IZhPeriodService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 时段Controller
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
@Api(tags = "时段")
@RestController
@RequestMapping("/zhenghe/period")
public class ZhPeriodController extends BaseController
{
    @Autowired
    private IZhPeriodService zhPeriodService;

    /**
     * 查询时段列表
     */
    @ApiOperation(value = "查询时段列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum",value = "页数",required = true,paramType = "query",dataType = "integer"),
            @ApiImplicitParam(name = "pageSize",value = "条数",required = true,paramType = "query",dataType = "integer")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:period:list')")
    @GetMapping("/list")
    public TableDataInfo list(ZhPeriod zhPeriod)
    {
        startPage();
        List<ZhPeriod> list = zhPeriodService.selectZhPeriodList(zhPeriod);
        return getDataTable(list);
    }

    /**
     * 导出时段列表
     */
    //@PreAuthorize("@ss.hasPermi('zhenghe:period:export')")
    @Log(title = "时段", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhPeriod zhPeriod)
    {
        List<ZhPeriod> list = zhPeriodService.selectZhPeriodList(zhPeriod);
        ExcelUtil<ZhPeriod> util = new ExcelUtil<ZhPeriod>(ZhPeriod.class);
        util.exportExcel(response, list, "时段数据");
    }

    /**
     * 获取时段详细信息
     */
    @ApiOperation(value = "获取时段详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "时段id",required = true,paramType = "path",dataType = "long")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:period:query')")
    @GetMapping(value = "/{id}")
    public R<ZhPeriod> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(zhPeriodService.selectZhPeriodById(id));
    }

    /**
     * 新增时段
     */
    @ApiOperation(value = "新增时段")
    //@PreAuthorize("@ss.hasPermi('zhenghe:period:add')")
    @Log(title = "时段", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZhPeriod zhPeriod)
    {
        return toAjax(zhPeriodService.insertZhPeriod(zhPeriod));
    }

    /**
     * 修改时段
     */
    @ApiOperation(value = "修改时段")
    //@PreAuthorize("@ss.hasPermi('zhenghe:period:edit')")
    @Log(title = "时段", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhPeriod zhPeriod)
    {
        return toAjax(zhPeriodService.updateZhPeriod(zhPeriod));
    }

    /**
     * 删除时段
     */
    @ApiOperation(value = "删除时段")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids",value = "时段id",required = true,paramType = "path",dataType = "long")
    })
    //@PreAuthorize("@ss.hasPermi('zhenghe:period:remove')")
    @Log(title = "时段", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(zhPeriodService.deleteZhPeriodByIds(ids));
    }
}
