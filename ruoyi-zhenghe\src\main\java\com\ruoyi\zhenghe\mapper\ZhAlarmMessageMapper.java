package com.ruoyi.zhenghe.mapper;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhAlarmMessage;

/**
 * 告警信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
public interface ZhAlarmMessageMapper 
{
    /**
     * 查询告警信息
     * 
     * @param id 告警信息主键
     * @return 告警信息
     */
    public ZhAlarmMessage selectZhAlarmMessageById(Long id);

    /**
     * 查询告警信息列表
     * 
     * @param zhAlarmMessage 告警信息
     * @return 告警信息集合
     */
    public List<ZhAlarmMessage> selectZhAlarmMessageList(ZhAlarmMessage zhAlarmMessage);

    /**
     * 新增告警信息
     * 
     * @param zhAlarmMessage 告警信息
     * @return 结果
     */
    public int insertZhAlarmMessage(ZhAlarmMessage zhAlarmMessage);

    /**
     * 修改告警信息
     * 
     * @param zhAlarmMessage 告警信息
     * @return 结果
     */
    public int updateZhAlarmMessage(ZhAlarmMessage zhAlarmMessage);

    /**
     * 删除告警信息
     * 
     * @param id 告警信息主键
     * @return 结果
     */
    public int deleteZhAlarmMessageById(Long id);

    /**
     * 批量删除告警信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZhAlarmMessageByIds(Long[] ids);


    int selectZhAlarmMessageCountByAlarmConfigurationId(Long alarmConfigurationId);

    /**
     * 查询告警信息对象
     *
     * @param alarmConfigurationId 告警信息
     * @return 告警信息
     */
    public ZhAlarmMessage selectZhAlarmMessageByAlarmConfigurationId(Long alarmConfigurationId);
}
