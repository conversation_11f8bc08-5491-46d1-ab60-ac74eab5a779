package com.ruoyi.zhenghe.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.zhenghe.domain.ZhAlarmConfiguration;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.mapper.ZhAlarmConfigurationMapper;
import com.ruoyi.zhenghe.mapper.ZhIotEquipmentMapper;
import com.ruoyi.zhengheiot.domain.IotRealData;
import org.apache.commons.jexl3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.zhenghe.mapper.ZhAlarmMessageMapper;
import com.ruoyi.zhenghe.domain.ZhAlarmMessage;
import com.ruoyi.zhenghe.service.IZhAlarmMessageService;

/**
 * 告警信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@Service
public class ZhAlarmMessageServiceImpl implements IZhAlarmMessageService 
{
    @Autowired
    private ZhAlarmMessageMapper zhAlarmMessageMapper;
    @Autowired
    private ZhAlarmConfigurationMapper zhAlarmConfigurationMapper;
    @Autowired
    private ZhIotEquipmentMapper zhIotEquipmentMapper;

    /**
     * 查询告警信息
     * 
     * @param id 告警信息主键
     * @return 告警信息
     */
    @Override
    public ZhAlarmMessage selectZhAlarmMessageById(Long id)
    {
        return zhAlarmMessageMapper.selectZhAlarmMessageById(id);
    }

    /**
     * 查询告警信息列表
     * 
     * @param zhAlarmMessage 告警信息
     * @return 告警信息
     */
    @Override
    public List<ZhAlarmMessage> selectZhAlarmMessageList(ZhAlarmMessage zhAlarmMessage)
    {
        return zhAlarmMessageMapper.selectZhAlarmMessageList(zhAlarmMessage);
    }

    /**
     * 新增告警信息
     * 
     * @param zhAlarmMessage 告警信息
     * @return 结果
     */
    @Override
    public int insertZhAlarmMessage(ZhAlarmMessage zhAlarmMessage)
    {
        zhAlarmMessage.setCreateTime(DateUtils.getNowDate());
        return zhAlarmMessageMapper.insertZhAlarmMessage(zhAlarmMessage);
    }

    /**
     * 修改告警信息
     * 
     * @param zhAlarmMessage 告警信息
     * @return 结果
     */
    @Override
    public int updateZhAlarmMessage(ZhAlarmMessage zhAlarmMessage)
    {
        zhAlarmMessage.setUpdateTime(DateUtils.getNowDate());
        return zhAlarmMessageMapper.updateZhAlarmMessage(zhAlarmMessage);
    }

    /**
     * 批量删除告警信息
     * 
     * @param ids 需要删除的告警信息主键
     * @return 结果
     */
    @Override
    public int deleteZhAlarmMessageByIds(Long[] ids)
    {
        return zhAlarmMessageMapper.deleteZhAlarmMessageByIds(ids);
    }

    /**
     * 删除告警信息信息
     * 
     * @param id 告警信息主键
     * @return 结果
     */
    @Override
    public int deleteZhAlarmMessageById(Long id)
    {
        return zhAlarmMessageMapper.deleteZhAlarmMessageById(id);
    }

    @Override
    public void executeAlarmMessage(String deviceCode, List<IotRealData> tempList) {
        ZhIotEquipment zhIotEquipment = zhIotEquipmentMapper.selectZhIotEquipmentByEquipmentCode(deviceCode);
        if (ObjectUtil.isNotNull(zhIotEquipment)){
            //查出该设备的告警配置
            List<ZhAlarmConfiguration> zhAlarmConfigurationList = zhAlarmConfigurationMapper.selectZhAlarmConfigurationByEquipmentId(zhIotEquipment.getId());
            if (null != zhAlarmConfigurationList &&zhAlarmConfigurationList.size()>0){
                for (ZhAlarmConfiguration zhAlarmConfiguration : zhAlarmConfigurationList) {
                    for (IotRealData iotRealData : tempList) {
                        //比较属性和设备code是否相同
                        if (zhAlarmConfiguration.getAttrCode().equals(iotRealData.getTag()) && zhAlarmConfiguration.getEquipmentCode().equals(iotRealData.getDeviceCode())){
                            //首先转换值类型
                            if (zhAlarmConfiguration.getAttrType().equalsIgnoreCase("double") || zhAlarmConfiguration.getAttrType().equalsIgnoreCase("int")){
                                //相同就判断数值是否符合配置
                                if (evaluateExpression(Convert.toDouble(iotRealData.getVal()),zhAlarmConfiguration.getAlarmJudgeExpression(),iotRealData.getTag())){
                                    //为true符合配置，查询告警信息
                                    int count = zhAlarmMessageMapper.selectZhAlarmMessageCountByAlarmConfigurationId(zhAlarmConfiguration.getId());
                                    if (count == 0){
                                        //新故障发生
                                        ZhAlarmMessage zhAlarmMessage = new ZhAlarmMessage();
                                        zhAlarmMessage.setAlarmConfigurationId(zhAlarmConfiguration.getId());
                                        zhAlarmMessage.setStartTime(new Date());
                                        zhAlarmMessage.setDeptId(Long.valueOf(zhIotEquipment.getDeptId()));
                                        zhAlarmMessage.setCreateTime(new Date());
                                        zhAlarmMessageMapper.insertZhAlarmMessage(zhAlarmMessage);
                                    }
                                }else {
                                    //故障结束
                                    ZhAlarmMessage zhAlarmMessage = zhAlarmMessageMapper.selectZhAlarmMessageByAlarmConfigurationId(zhAlarmConfiguration.getId());
                                    if (ObjectUtil.isNotNull(zhAlarmMessage)){
                                        //修改结束时间
                                        zhAlarmMessage.setEndTime(new Date());
                                        zhAlarmMessage.setSustainTime(DateUtil.between(zhAlarmMessage.getStartTime(),zhAlarmMessage.getEndTime(), DateUnit.SECOND));
                                        zhAlarmMessageMapper.updateZhAlarmMessage(zhAlarmMessage);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private static final JexlEngine jexl = new JexlBuilder().cache(100).strict(true).silent(false).create();

    public boolean evaluateExpression(Double x, String expression,String attrCode) {
        if (expression == null || expression.isEmpty()) {
            throw new IllegalArgumentException("条件表达式不能为空");
        }

        // 替换表达式中的字段名为变量名（例如，将 "x" 替换为 "xValue"）
        //expression = expression.replaceAll("x", "xValue");

        // 创建 JEXL 引擎和上下文
        //JexlEngine jexl = new JexlBuilder().create();
        JexlExpression jexlExpression = jexl.createExpression(expression);
        JexlContext context = new MapContext();

        // 将变量值传入上下文
        context.set(attrCode, x);
        // 评估表达式
        boolean evaluate = (boolean) jexlExpression.evaluate(context);
        //清除缓存
        jexl.clearCache();


        return evaluate;
    }

}
