package com.ruoyi.zhenghe.service.impl;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.zhenghe.domain.ZhBaseProcess;
import com.ruoyi.zhenghe.mapper.ZhBaseProcessMapper;
import com.ruoyi.zhenghe.service.IZhBaseProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 基础数据-工序Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
@Service
public class ZhBaseProcessServiceImpl implements IZhBaseProcessService 
{
    @Autowired
    private ZhBaseProcessMapper zhBaseProcessMapper;

    /**
     * 查询基础数据-工序
     * 
     * @param id 基础数据-工序主键
     * @return 基础数据-工序
     */
    @Override
    public ZhBaseProcess selectZhBaseProcessById(Long id)
    {
        return zhBaseProcessMapper.selectZhBaseProcessById(id);
    }

    /**
     * 查询基础数据-工序列表
     * 
     * @param zhBaseProcess 基础数据-工序
     * @return 基础数据-工序
     */
    @Override
    @DataScope(deptAlias = "b")
    public List<ZhBaseProcess> selectZhBaseProcessList(ZhBaseProcess zhBaseProcess)
    {
        return zhBaseProcessMapper.selectZhBaseProcessList(zhBaseProcess);
    }

    /**
     * 新增基础数据-工序
     * 
     * @param zhBaseProcess 基础数据-工序
     * @return 结果
     */
    @Override
    public int insertZhBaseProcess(ZhBaseProcess zhBaseProcess) {
        zhBaseProcess.setUserId(SecurityUtils.getUserId());
        if (zhBaseProcess.getDeptId() == null) {
            zhBaseProcess.setDeptId(SecurityUtils.getDeptId());
        }
        zhBaseProcess.setCreateBy(SecurityUtils.getUsername());
        zhBaseProcess.setCreateTime(DateUtils.getNowDate());
        return zhBaseProcessMapper.insertZhBaseProcess(zhBaseProcess);
    }

    /**
     * 修改基础数据-工序
     * 
     * @param zhBaseProcess 基础数据-工序
     * @return 结果
     */
    @Override
    public int updateZhBaseProcess(ZhBaseProcess zhBaseProcess)
    {
        zhBaseProcess.setUpdateTime(DateUtils.getNowDate());
        return zhBaseProcessMapper.updateZhBaseProcess(zhBaseProcess);
    }

    /**
     * 批量删除基础数据-工序
     * 
     * @param ids 需要删除的基础数据-工序主键
     * @return 结果
     */
    @Override
    public int deleteZhBaseProcessByIds(Long[] ids)
    {
        return zhBaseProcessMapper.deleteZhBaseProcessByIds(ids);
    }

    /**
     * 删除基础数据-工序信息
     * 
     * @param id 基础数据-工序主键
     * @return 结果
     */
    @Override
    public int deleteZhBaseProcessById(Long id)
    {
        return zhBaseProcessMapper.deleteZhBaseProcessById(id);
    }
}
