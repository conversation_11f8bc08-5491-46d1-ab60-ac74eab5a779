package com.ruoyi.zhenghe.service;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhBaseProcess;

/**
 * 基础数据-工序Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface IZhBaseProcessService 
{
    /**
     * 查询基础数据-工序
     * 
     * @param id 基础数据-工序主键
     * @return 基础数据-工序
     */
    public ZhBaseProcess selectZhBaseProcessById(Long id);

    /**
     * 查询基础数据-工序列表
     * 
     * @param zhBaseProcess 基础数据-工序
     * @return 基础数据-工序集合
     */
    public List<ZhBaseProcess> selectZhBaseProcessList(ZhBaseProcess zhBaseProcess);

    /**
     * 新增基础数据-工序
     * 
     * @param zhBaseProcess 基础数据-工序
     * @return 结果
     */
    public int insertZhBaseProcess(ZhBaseProcess zhBaseProcess);

    /**
     * 修改基础数据-工序
     * 
     * @param zhBaseProcess 基础数据-工序
     * @return 结果
     */
    public int updateZhBaseProcess(ZhBaseProcess zhBaseProcess);

    /**
     * 批量删除基础数据-工序
     * 
     * @param ids 需要删除的基础数据-工序主键集合
     * @return 结果
     */
    public int deleteZhBaseProcessByIds(Long[] ids);

    /**
     * 删除基础数据-工序信息
     * 
     * @param id 基础数据-工序主键
     * @return 结果
     */
    public int deleteZhBaseProcessById(Long id);
}
