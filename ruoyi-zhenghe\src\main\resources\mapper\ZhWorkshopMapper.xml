<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhWorkshopMapper">

    <resultMap type="ZhWorkshop" id="ZhWorkshopResult">
        <result property="id"    column="id"    />
        <result property="workshopName"    column="workshop_name"    />
        <result property="workshopCode"    column="workshop_code"    />
        <result property="remark"    column="remark"    />
        <result property="parentId"    column="parent_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deptId"    column="dept_id"    />
    </resultMap>

    <resultMap type="com.ruoyi.zhenghe.domain.vo.ZhWorkshopVo" id="ZhWorkshopVoResult">
        <result property="id"    column="id"    />
        <result property="workshopName"    column="workshop_name"    />
        <result property="workshopCode"    column="workshop_code"    />
        <result property="remark"    column="remark"    />
        <result property="parentId"    column="parent_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deptId"    column="dept_id"    />
    </resultMap>

    <sql id="selectZhWorkshopVo">
        select id, workshop_name, workshop_code, remark, parent_id, create_by, create_time, update_by, update_time,dept_id from zh_workshop w
    </sql>

    <sql id="selectZhWorkshopVoLian">
        select w.id, w.workshop_name, w.workshop_code, w.remark, w.parent_id, w.create_by, w.create_time, w.update_by,
        w.update_time,w.dept_id,
        d.dept_name as deptName
        from zh_workshop w
        left join sys_dept d on w.dept_id = d.dept_id
    </sql>

    <select id="selectZhWorkshopList" parameterType="ZhWorkshop" resultMap="ZhWorkshopResult">
        <include refid="selectZhWorkshopVoLian"/>
        <where>
            1 = 1
            <if test="workshopName != null  and workshopName != ''"> and w.workshop_name like concat('%', #{workshopName}, '%')</if>
            <if test="workshopCode != null  and workshopCode != ''"> and w.workshop_code = #{workshopCode}</if>
            <if test="parentId != null "> and w.parent_id = #{parentId}</if>
            <if test="id != null "> and w.id = #{id}</if>
            <if test="deptId != null "> and w.dept_id = #{deptId}</if>
        </where>
        ${params.dataScope}
        order by
        w.create_time desc
    </select>

    <select id="selectZhWorkshopById" parameterType="Long" resultMap="ZhWorkshopResult">
        <include refid="selectZhWorkshopVoLian"/>
        where w.id = #{id}
    </select>

    <insert id="insertZhWorkshop" parameterType="ZhWorkshop" useGeneratedKeys="true" keyProperty="id">
        insert into zh_workshop
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workshopName != null and workshopName != ''">workshop_name,</if>
            <if test="workshopCode != null and workshopCode != ''">workshop_code,</if>
            <if test="remark != null">remark,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deptId != null">dept_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workshopName != null and workshopName != ''">#{workshopName},</if>
            <if test="workshopCode != null and workshopCode != ''">#{workshopCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deptId != null">#{deptId},</if>
        </trim>
    </insert>

    <update id="updateZhWorkshop" parameterType="ZhWorkshop">
        update zh_workshop
        <trim prefix="SET" suffixOverrides=",">
            <if test="workshopName != null and workshopName != ''">workshop_name = #{workshopName},</if>
            <if test="workshopCode != null and workshopCode != ''">workshop_code = #{workshopCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhWorkshopById" parameterType="Long">
        delete from zh_workshop where id = #{id}
    </delete>

    <delete id="deleteZhWorkshopByIds" parameterType="String">
        delete from zh_workshop where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectZhWorkshopByParentId" parameterType="Long" resultMap="ZhWorkshopResult">
        <include refid="selectZhWorkshopVo"/>
        where w.parent_id = #{parentId}
    </select>

    <select id="selectZhWorkshopVoList" parameterType="ZhWorkshop" resultMap="ZhWorkshopVoResult">
        <include refid="selectZhWorkshopVoLian"/>
        <where>
            1 = 1
            <if test="workshopName != null  and workshopName != ''"> and w.workshop_name like concat('%', #{workshopName}, '%')</if>
            <if test="workshopCode != null  and workshopCode != ''"> and w.workshop_code = #{workshopCode}</if>
            <if test="parentId != null "> and w.parent_id = #{parentId}</if>
            <if test="deptId != null "> and w.dept_id = #{deptId}</if>
        </where>
        ${params.dataScope}
        order by
        w.create_time desc
    </select>
</mapper>