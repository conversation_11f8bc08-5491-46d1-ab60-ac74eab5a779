package com.ruoyi.zhengheiot.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * IoT实时数据对象 iot_real_data
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
public class IotRealData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 设备编码 */
    private String deviceCode;

    /** 属性编码 */
    @Excel(name = "属性编码")
    private String tag;

    /** 值 */
    @Excel(name = "值")
    private String val;

    /** $column.columnComment */
    private String key;

    public void setDeviceCode(String deviceCode) 
    {
        this.deviceCode = deviceCode;
    }

    public String getDeviceCode() 
    {
        return deviceCode;
    }

    public void setTag(String tag) 
    {
        this.tag = tag;
    }

    public String getTag() 
    {
        return tag;
    }

    public void setVal(String val) 
    {
        this.val = val;
    }

    public String getVal() 
    {
        return val;
    }

    public void setKey(String key) 
    {
        this.key = key;
    }

    public String getKey() 
    {
        return key;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("deviceCode", getDeviceCode())
            .append("tag", getTag())
            .append("val", getVal())
            .append("updateTime", getUpdateTime())
            .append("key", getKey())
            .toString();
    }
}
