package com.ruoyi.zhenghe.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.zhenghe.domain.ZhPeriodMaintain;
import com.ruoyi.zhenghe.domain.ZhWorkshop;
import com.ruoyi.zhenghe.domain.vo.ZhPeriodMaintainVo;
import com.ruoyi.zhenghe.mapper.ZhPeriodMaintainMapper;
import com.ruoyi.zhenghe.service.IZhPeriodMaintainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 尖峰平谷时段维护Controller
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
@Api(tags = "尖峰平谷时段维护")
@RestController
@RequestMapping("/zhenghe/maintain")
public class ZhPeriodMaintainController extends BaseController
{
    @Autowired
    private IZhPeriodMaintainService zhPeriodMaintainService;
    @Resource
    private ZhPeriodMaintainMapper zhPeriodMaintainMapper;

    /**
     * 查询尖峰平谷时段维护列表
     */
    @ApiOperation(value = "查询尖峰平谷时段维护列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "yearTime",value = "年",required = false,paramType = "query",dataType = "String")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:maintain:list')")
    @GetMapping("/list")
    public R<List<ZhPeriodMaintain>> list(ZhPeriodMaintain zhPeriodMaintain)
    {
        List<ZhPeriodMaintain> list = zhPeriodMaintainService.selectZhPeriodMaintainList(zhPeriodMaintain);
        return R.ok(list);
    }

    /**
     * 导出尖峰平谷时段维护列表
     */
    @ApiOperation(value = "导出尖峰平谷时段维护列表")
    @PreAuthorize("@ss.hasPermi('zhenghe:maintain:export')")
    @Log(title = "尖峰平谷时段维护", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhPeriodMaintain zhPeriodMaintain)
    {
        List<ZhPeriodMaintain> list = zhPeriodMaintainService.selectZhPeriodMaintainList(zhPeriodMaintain);
        ExcelUtil<ZhPeriodMaintain> util = new ExcelUtil<ZhPeriodMaintain>(ZhPeriodMaintain.class);
        util.exportExcel(response, list, "尖峰平谷时段维护数据");
    }

    /**
     * 导入尖峰平谷时段维护列表
     */
    @ApiOperation(value = "导入尖峰平谷时段维护列表")
    //@PreAuthorize("@ss.hasPermi('zhenghe:workshop:import')")
    @Log(title = "尖峰平谷时段", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws IOException {
        ExcelUtil<ZhPeriodMaintain> util = new ExcelUtil<ZhPeriodMaintain>(ZhPeriodMaintain.class);
        List<ZhPeriodMaintain> zhPeriodMaintainList = util.importExcel(file.getInputStream());
        if (StringUtils.isNull(zhPeriodMaintainList) || zhPeriodMaintainList.size()==0){
            throw new ServiceException("导入尖峰平谷时段维护列表不能为空");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ZhPeriodMaintain zhPeriodMaintain : zhPeriodMaintainList) {
            try {
                ZhPeriodMaintain query=new ZhPeriodMaintain();
                query.setYearTime(zhPeriodMaintain.getYearTime());
                query.setPeriod(zhPeriodMaintain.getPeriod());
                final List<ZhPeriodMaintain> zhPeriodMaintains = zhPeriodMaintainService.selectZhPeriodMaintainList(query);
                if(zhPeriodMaintains!=null&&zhPeriodMaintains.size()>0){
                    zhPeriodMaintain.setId(zhPeriodMaintains.get(0).getId());
                    zhPeriodMaintain.setUpdateBy(SecurityUtils.getUsername());
                    zhPeriodMaintain.setUpdateTime(DateUtils.getNowDate());
                    zhPeriodMaintainMapper.updateZhPeriodMaintain(zhPeriodMaintain);
                }else {
                    zhPeriodMaintain.setCreateBy(SecurityUtils.getUsername());
                    zhPeriodMaintain.setCreateTime(DateUtils.getNowDate());
                    zhPeriodMaintainMapper.insertZhPeriodMaintain(zhPeriodMaintain);
                }
                successNum++;
                successMsg.append("<br/>" + successNum + "、时段 " + zhPeriodMaintain.getPeriod() + " 导入成功");
            }
            catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、时段 " + zhPeriodMaintain.getPeriod() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum>0){
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return AjaxResult.success(successMsg.toString());
    }

    /**
     * 导出尖峰平谷时段维护列表模板
     */
    @ApiOperation(value = "导出尖峰平谷时段维护列表模板")
    @Log(title = "尖峰平谷时段维护列表模板", businessType = BusinessType.EXPORT)
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response, ZhWorkshop zhWorkshop)
    {
        ExcelUtil<ZhPeriodMaintainVo> util = new ExcelUtil<ZhPeriodMaintainVo>(ZhPeriodMaintainVo.class);
        util.exportExcel(response, new ArrayList<>(), "尖峰平谷时段数据");
    }


    /**
     * 获取尖峰平谷时段维护详细信息
     */
    @ApiOperation(value = "获取尖峰平谷时段维护详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "工厂车间id",required = true,paramType = "path",dataType = "long")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:maintain:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(zhPeriodMaintainService.selectZhPeriodMaintainById(id));
    }

    /**
     * 新增尖峰平谷时段维护
     */
    @ApiOperation(value = "新增尖峰平谷时段维护")
    @PreAuthorize("@ss.hasPermi('zhenghe:maintain:add')")
    @Log(title = "尖峰平谷时段维护", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody List<ZhPeriodMaintain> zhPeriodMaintainList)
    {
        return toAjax(zhPeriodMaintainService.insertZhPeriodMaintain(zhPeriodMaintainList));
    }

    /**
     * 修改尖峰平谷时段维护
     */
    @PreAuthorize("@ss.hasPermi('zhenghe:maintain:edit')")
    @Log(title = "尖峰平谷时段维护", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhPeriodMaintain zhPeriodMaintain)
    {
        return toAjax(zhPeriodMaintainService.updateZhPeriodMaintain(zhPeriodMaintain));
    }

    /**
     * 删除尖峰平谷时段维护
     */
    @PreAuthorize("@ss.hasPermi('zhenghe:maintain:remove')")
    @Log(title = "尖峰平谷时段维护", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(zhPeriodMaintainService.deleteZhPeriodMaintainByIds(ids));
    }
}
