<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhengheiot.mapper.IotRealDataMapper">
    
    <resultMap type="IotRealData" id="IotRealDataResult">
        <result property="deviceCode"    column="device_code"    />
        <result property="tag"    column="tag"    />
        <result property="val"    column="val"    />
        <result property="updateTime"    column="update_time"    />
        <result property="key"    column="key"    />
    </resultMap>

    <sql id="selectIotRealDataVo">
        select device_code, tag, val, update_time from iot_real_data
    </sql>

    <select id="selectIotRealDataList" parameterType="IotRealData" resultMap="IotRealDataResult">
        <include refid="selectIotRealDataVo"/>
        <where>  
            <if test="deviceCode != null  and deviceCode != ''"> and device_code = #{deviceCode}</if>
            <if test="tag != null  and tag != ''"> and tag = #{tag}</if>
            <if test="val != null  and val != ''"> and val = #{val}</if>
        </where>
    </select>
    
    <select id="selectIotRealDataByKey" parameterType="String" resultMap="IotRealDataResult">
        <include refid="selectIotRealDataVo"/>
        where key = #{key}
    </select>

    <insert id="insertIotRealData" parameterType="IotRealData">
        insert into iot_real_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceCode != null and deviceCode != ''">device_code,</if>
            <if test="tag != null and tag != ''">tag,</if>
            <if test="val != null">val,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="key != null">key,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceCode != null and deviceCode != ''">#{deviceCode},</if>
            <if test="tag != null and tag != ''">#{tag},</if>
            <if test="val != null">#{val},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="key != null">#{key},</if>
         </trim>
    </insert>

    <update id="updateIotRealData" parameterType="IotRealData">
        update iot_real_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceCode != null and deviceCode != ''">device_code = #{deviceCode},</if>
            <if test="tag != null and tag != ''">tag = #{tag},</if>
            <if test="val != null">val = #{val},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where key = #{key}
    </update>

    <delete id="deleteIotRealDataByKey" parameterType="String">
        delete from iot_real_data where key = #{key}
    </delete>

    <delete id="deleteIotRealDataByKeys" parameterType="String">
        delete from iot_real_data where key in 
        <foreach item="key" collection="array" open="(" separator="," close=")">
            #{key}
        </foreach>
    </delete>
    <insert id="saveOrUpdate">
        insert into iot_real_data(`key`,device_code, tag, val,update_time)
        values
        <foreach collection="list" item="p" index="index" separator=",">
            (
            #{p.key},
            #{p.deviceCode},
            #{p.tag},
            #{p.val},
            NOW())
        </foreach>
        ON DUPLICATE KEY UPDATE
        device_code = values(device_code),
        tag = values(tag),
        val = values(val),
        update_time = values(update_time)
    </insert>
</mapper>