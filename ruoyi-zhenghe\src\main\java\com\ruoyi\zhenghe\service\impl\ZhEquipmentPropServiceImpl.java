package com.ruoyi.zhenghe.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.ruoyi.zhenghe.domain.ZhDeviceTypeAttr;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.mapper.ZhDeviceTypeAttrMapper;
import com.ruoyi.zhenghe.mapper.ZhIotEquipmentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.zhenghe.mapper.ZhEquipmentPropMapper;
import com.ruoyi.zhenghe.domain.ZhEquipmentProp;
import com.ruoyi.zhenghe.service.IZhEquipmentPropService;

import javax.annotation.Resource;

/**
 * 设备属性配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Service
public class ZhEquipmentPropServiceImpl implements IZhEquipmentPropService {
    @Resource
    private ZhEquipmentPropMapper zhEquipmentPropMapper;

    @Resource
    private ZhIotEquipmentMapper equipmentMapper;
    @Resource
    private ZhDeviceTypeAttrMapper deviceTypeAttrMapper;


    /**
     * 查询设备属性配置
     *
     * @param id 设备属性配置主键
     * @return 设备属性配置
     */
//    @Override
//    public ZhEquipmentProp selectZhEquipmentPropById(Long id)
//    {
//        return zhEquipmentPropMapper.selectZhEquipmentPropById(id);
//    }

    /**
     * 查询设备属性配置列表
     *
     * @param zhEquipmentProp 设备属性配置
     * @return 设备属性配置
     */
    @Override
    public List<ZhEquipmentProp> selectZhEquipmentPropList(ZhEquipmentProp zhEquipmentProp) {
        return zhEquipmentPropMapper.selectZhEquipmentPropList(zhEquipmentProp);
    }

    /**
     * 根据设备ID选择设备属性
     *
     * @param id 设备ID，用于查询设备属性
     * @return 返回一个包含设备属性的列表，如果查询不到则返回空列表
     *
     * 此方法首先根据设备ID查询设备信息，然后根据设备类型查询设备类型属性，
     * 最后根据设备类型属性查询设备属性，并将查询到的设备属性添加到列表中返回
     */
    @Override
    public List<ZhEquipmentProp> selectZhEquipmentPropByEquipmentId(Long id) {
        // 初始化设备属性列表
        List<ZhEquipmentProp> ans = new ArrayList<>();
        // 创建查询设备对象并设置设备ID
        ZhIotEquipment queryEquipment = new ZhIotEquipment();
        queryEquipment.setId(id);
        // 查询设备信息
        ZhIotEquipment zhIotEquipment2 = equipmentMapper.selectZhIotEquipmentList(queryEquipment).get(0);
        ZhIotEquipment zhIotEquipment = equipmentMapper.selectZhIotEquipmentById(id);
        // 检查设备信息是否为空
        if (zhIotEquipment != null) {
            // 创建设备类型属性对象并设置设备类型ID
            ZhDeviceTypeAttr zhDeviceTypeAttr = new ZhDeviceTypeAttr();
            zhDeviceTypeAttr.setTslId(zhIotEquipment.getDeviceTypeId());
            // 查询设备类型属性列表
            List<ZhDeviceTypeAttr> zhDeviceTypeAttrs = deviceTypeAttrMapper.selectZhDeviceTypeAttrList(zhDeviceTypeAttr);
            // 检查设备类型属性列表是否为空
            if (zhDeviceTypeAttrs != null && zhDeviceTypeAttrs.size() > 0) {
                // 遍历设备类型属性列表
                for (ZhDeviceTypeAttr attr : zhDeviceTypeAttrs) {
                    // 创建设备属性对象并设置属性ID和设备ID
                    ZhEquipmentProp temp = new ZhEquipmentProp();
                    temp.setAttrId(attr.getId());
                    temp.setEquipmentId(id);
                    // 查询设备属性
                    ZhEquipmentProp zhEquipmentProp1 = null;
                    try {
                        zhEquipmentProp1 = zhEquipmentPropMapper.selectZhEquipmentPropList(temp).get(0);
                    } catch (Exception e) {
//                        throw new RuntimeException(e);
                    }
                    // 检查设备属性是否为空，如果为空则创建新的设备属性对象
                    if (zhEquipmentProp1 == null) {
                        zhEquipmentProp1 = new ZhEquipmentProp();
                    }
                    // 设置设备属性的属性名称和属性类型
                    zhEquipmentProp1.setAttrName(attr.getAttrName());
                    zhEquipmentProp1.setArrType(attr.getAttrType());
                    zhEquipmentProp1.setAttrClass(attr.getAttrClass());
                    zhEquipmentProp1.setAttrId(attr.getId());
                    zhEquipmentProp1.setAttrCode(attr.getAttrCode());
                    // 将设备属性添加到列表中
                    ans.add(zhEquipmentProp1);
                }
            }
        }
        // 返回设备属性列表
        return ans;
    }

    /**
     * 新增设备属性配置
     *
     * @param zhEquipmentProp 设备属性配置
     * @return 结果
     */
    @Override
    public int insertZhEquipmentProp(ZhEquipmentProp zhEquipmentProp) {
        return zhEquipmentPropMapper.insertZhEquipmentProp(zhEquipmentProp);
    }

    /**
     * 修改设备属性配置
     *
     * @param zhEquipmentProp 设备属性配置
     * @return 结果
     */
    @Override
    public int updateZhEquipmentProp(ZhEquipmentProp zhEquipmentProp) {
        return zhEquipmentPropMapper.updateZhEquipmentProp(zhEquipmentProp);
    }

    /**
     * 批量删除设备属性配置
     *
     * @param ids 需要删除的设备属性配置主键
     * @return 结果
     */
    @Override
    public int deleteZhEquipmentPropByIds(Long[] ids) {
        return zhEquipmentPropMapper.deleteZhEquipmentPropByIds(ids);
    }

    /**
     * 删除设备属性配置信息
     *
     * @param id 设备属性配置主键
     * @return 结果
     */
    @Override
    public int deleteZhEquipmentPropById(Long id) {
        return zhEquipmentPropMapper.deleteZhEquipmentPropById(id);
    }

    @Override
    public List<ZhEquipmentProp> selectZhEquipmentPropByEquipmentIdAndAlarm(Long id) {
        List<ZhEquipmentProp> zhEquipmentPropList = zhEquipmentPropMapper.selectZhEquipmentPropByEquipmentIdAndAlarm(id);
        return zhEquipmentPropList;
    }
}
