package com.ruoyi.zhengheiot.mapper;

import java.util.List;
import com.ruoyi.zhengheiot.domain.IotRealData;

/**
 * IoT实时数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface IotRealDataMapper 
{
    /**
     * 查询IoT实时数据
     * 
     * @param key IoT实时数据主键
     * @return IoT实时数据
     */
    public IotRealData selectIotRealDataByKey(String key);

    /**
     * 查询IoT实时数据列表
     * 
     * @param iotRealData IoT实时数据
     * @return IoT实时数据集合
     */
    public List<IotRealData> selectIotRealDataList(IotRealData iotRealData);

    /**
     * 新增IoT实时数据
     * 
     * @param iotRealData IoT实时数据
     * @return 结果
     */
    public int insertIotRealData(IotRealData iotRealData);

    /**
     * 修改IoT实时数据
     * 
     * @param iotRealData IoT实时数据
     * @return 结果
     */
    public int updateIotRealData(IotRealData iotRealData);

    /**
     * 删除IoT实时数据
     * 
     * @param key IoT实时数据主键
     * @return 结果
     */
    public int deleteIotRealDataByKey(String key);

    /**
     * 批量删除IoT实时数据
     * 
     * @param keys 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIotRealDataByKeys(String[] keys);

    void saveOrUpdate(List<IotRealData> list);
}
