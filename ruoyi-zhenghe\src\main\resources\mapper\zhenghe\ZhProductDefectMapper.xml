<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhProductDefectMapper">

    <resultMap type="ZhProductDefect" id="ZhProductDefectResult">
        <result property="id" column="id"/>
        <result property="productId" column="product_id"/>
        <result property="defectName" column="defect_name"/>
        <result property="defectImage" column="defect_image"/>
        <result property="status" column="status"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="productModel" column="product_model"/>
        <result property="productName" column="product_name"/>
    </resultMap>

    <sql id="selectZhProductDefectVo">
        SELECT d.id,
               d.product_id,
               d.defect_name,
               d.defect_image,
               d.status,
               d.sort_order,
               d.create_by,
               d.create_time,
               d.update_by,
               d.update_time,
               d.remark,
               p.product_model,
               CONCAT(IFNULL(p.product_model, ''), ' - ', IFNULL(p.timing_mark, '')) as product_name
        FROM zh_product_defect d
                 LEFT JOIN zh_product p ON d.product_id = p.id
    </sql>

    <select id="selectZhProductDefectList" parameterType="ZhProductDefect" resultMap="ZhProductDefectResult">
        <include refid="selectZhProductDefectVo"/>
        WHERE 1 = 1
        <if test="productId != null">
            AND d.product_id = #{productId}
        </if>
        <if test="defectName != null and defectName != ''">
            AND d.defect_name LIKE CONCAT('%', #{defectName}, '%')
        </if>
        <if test="status != null">
            AND d.status = #{status}
        </if>
        <if test="productModel != null and productModel != ''">
            AND p.product_model LIKE CONCAT('%', #{productModel}, '%')
        </if>
        ORDER BY d.product_id, d.sort_order ASC, d.id ASC
    </select>

    <select id="selectZhProductDefectListByProductId" parameterType="Long" resultMap="ZhProductDefectResult">
        <include refid="selectZhProductDefectVo"/>
        WHERE  d.product_id = #{productId}
        ORDER BY d.sort_order ASC, d.id DESC
    </select>
    <select id="selectZhProductDefectListByProductIdOpen" parameterType="Long" resultMap="ZhProductDefectResult">
        <include refid="selectZhProductDefectVo"/>
        WHERE d.status = 1 AND d.product_id = #{productId}
        ORDER BY d.sort_order ASC, d.id ASC
    </select>

    <select id="selectZhProductDefectById" parameterType="Long" resultMap="ZhProductDefectResult">
        <include refid="selectZhProductDefectVo"/>
        WHERE d.id = #{id}
    </select>

    <select id="getNextSortOrder" parameterType="Long" resultType="Integer">
        SELECT IFNULL(MAX(sort_order), 0) + 1
        FROM zh_product_defect
        WHERE product_id = #{productId}
    </select>

    <insert id="insertZhProductDefect" parameterType="ZhProductDefect" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO zh_product_defect
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="defectName != null and defectName != ''">defect_name,</if>
            <if test="defectImage != null">defect_image,</if>
            <if test="status != null">status,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="defectName != null and defectName != ''">#{defectName},</if>
            <if test="defectImage != null">#{defectImage},</if>
            <if test="status != null">#{status},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateZhProductDefect" parameterType="ZhProductDefect">
        UPDATE zh_product_defect
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="defectName != null and defectName != ''">defect_name = #{defectName},</if>
            <if test="defectImage != null">defect_image = #{defectImage},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <update id="updateZhProductDefectStatus">
        UPDATE zh_product_defect SET status = #{status}
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteZhProductDefectById" parameterType="Long">
        DELETE FROM zh_product_defect WHERE id = #{id}
    </delete>

    <delete id="deleteZhProductDefectByIds" parameterType="String">
        DELETE FROM zh_product_defect WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteZhProductDefectByProductId" parameterType="Long">
        DELETE FROM zh_product_defect WHERE product_id = #{productId}
    </delete>

</mapper>
