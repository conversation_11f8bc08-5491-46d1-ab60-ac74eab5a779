package com.ruoyi.zhenghe.mapper;

import com.ruoyi.zhenghe.domain.ZhDeviceProductionRecord;
import com.ruoyi.zhenghe.domain.dto.ProductQuantityDto;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 设备产品生产记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ZhDeviceProductionRecordMapper 
{
    /**
     * 查询设备产品生产记录
     * 
     * @param id 设备产品生产记录主键
     * @return 设备产品生产记录
     */
    public ZhDeviceProductionRecord selectZhDeviceProductionRecordById(Long id);

    /**
     * 查询设备产品生产记录列表
     * 
     * @param zhDeviceProductionRecord 设备产品生产记录
     * @return 设备产品生产记录集合
     */
    public List<ZhDeviceProductionRecord> selectZhDeviceProductionRecordList(ZhDeviceProductionRecord zhDeviceProductionRecord);

    /**
     * 新增设备产品生产记录
     * 
     * @param zhDeviceProductionRecord 设备产品生产记录
     * @return 结果
     */
    public int insertZhDeviceProductionRecord(ZhDeviceProductionRecord zhDeviceProductionRecord);

    /**
     * 修改设备产品生产记录
     * 
     * @param zhDeviceProductionRecord 设备产品生产记录
     * @return 结果
     */
    public int updateZhDeviceProductionRecord(ZhDeviceProductionRecord zhDeviceProductionRecord);

    /**
     * 删除设备产品生产记录
     * 
     * @param id 设备产品生产记录主键
     * @return 结果
     */
    public int deleteZhDeviceProductionRecordById(Long id);

    /**
     * 批量删除设备产品生产记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZhDeviceProductionRecordByIds(Long[] ids);

    /**
     * 查询设备当前正在生产的产品记录
     *
     * @param deviceCode 设备编码
     * @param productName 产品名称
     * @return 生产记录
     */
    public ZhDeviceProductionRecord selectCurrentProductionRecord(@Param("deviceCode") String deviceCode,
                                                                 @Param("productName") String productName);

    /**
     * 查询设备最新的生产记录
     *
     * @param deviceCode 设备编码
     * @param productName 产品名称
     * @return 生产记录
     */
    public ZhDeviceProductionRecord selectLatestProductionRecord(@Param("deviceCode") String deviceCode,
                                                                @Param("productName") String productName);

    /**
     * 结束生产记录
     *
     * @param id 记录ID
     * @param endTime 结束时间
     * @param productionDuration 生产时长
     * @return 结果
     */
    public int finishProductionRecord(@Param("id") Long id,
                                     @Param("endTime") java.util.Date endTime,
                                     @Param("productionDuration") Integer productionDuration);

    /**
     * 查询设备所有正在进行中的生产记录
     *
     * @param deviceCode 设备编码
     * @return 生产记录列表
     */
    public List<ZhDeviceProductionRecord> selectActiveProductionRecords(@Param("deviceCode") String deviceCode);

    /**
     * 查询设备生产汇总数据
     *
     * @param deviceCode 设备编码
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 生产汇总列表
     */
    public List<ZhDeviceProductionRecord> selectProductionSummary(@Param("deviceCode") String deviceCode,
                                                                 @Param("beginTime") String beginTime,
                                                                 @Param("endTime") String endTime);

    /**
     * 查询产品数量展示界面数据
     *
     * @param productName 产品型号（可选）
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 产品数量列表
     */
    public List<ZhDeviceProductionRecord> selectProductQuantityList(@Param("productName") String productName,
                                                                   @Param("beginTime") String beginTime,
                                                                   @Param("endTime") String endTime);

    /**
     * 查询产品数量展示界面数据（用于导出）
     *
     * @param productName 产品型号（可选）
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 产品数量DTO列表
     */
    public List<ProductQuantityDto> selectProductQuantityForExport(@Param("productName") String productName,
                                                                  @Param("beginTime") String beginTime,
                                                                  @Param("endTime") String endTime);

    /**
     * 查询产品生产明细
     *
     * @param productName 产品名称
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 产品生产明细列表
     */
    public List<ZhDeviceProductionRecord> selectProductDetail(@Param("productName") String productName,
                                                             @Param("beginTime") String beginTime,
                                                             @Param("endTime") String endTime);

    /**
     * 查询产品配置与生产数据组合视图
     *
     * @param deviceCode 设备编码
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 产品配置与生产数据列表
     */
    public List<ZhDeviceProductionRecord> selectProductConfigWithProduction(String deviceCode, String beginTime, String endTime);
}
