package com.ruoyi.zhenghe.mapper;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhWorkshop;
import com.ruoyi.zhenghe.domain.vo.ZhWorkshopVo;

/**
 * 车间管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface ZhWorkshopMapper 
{
    /**
     * 查询车间管理
     * 
     * @param id 车间管理主键
     * @return 车间管理
     */
    public ZhWorkshop selectZhWorkshopById(Long id);

    /**
     * 查询车间管理列表
     * 
     * @param zhWorkshop 车间管理
     * @return 车间管理集合
     */

    public List<ZhWorkshop> selectZhWorkshopList(ZhWorkshop zhWorkshop);

    /**
     * 新增车间管理
     * 
     * @param zhWorkshop 车间管理
     * @return 结果
     */
    public int insertZhWorkshop(ZhWorkshop zhWorkshop);

    /**
     * 修改车间管理
     * 
     * @param zhWorkshop 车间管理
     * @return 结果
     */
    public int updateZhWorkshop(ZhWorkshop zhWorkshop);

    /**
     * 删除车间管理
     * 
     * @param id 车间管理主键
     * @return 结果
     */
    public int deleteZhWorkshopById(Long id);

    /**
     * 批量删除车间管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZhWorkshopByIds(Long[] ids);

    /**
     * 查询车间管理
     *
     * @param parentId 车间管理父级
     * @return 车间管理
     */
    public List<ZhWorkshop> selectZhWorkshopByParentId(Long parentId);

    /**
     * 查询车间管理列表导出
     *
     * @param zhWorkshop 车间管理
     * @return 车间管理集合
     */
    public List<ZhWorkshopVo> selectZhWorkshopVoList(ZhWorkshop zhWorkshop);
}
