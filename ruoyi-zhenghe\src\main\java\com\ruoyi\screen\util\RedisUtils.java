package com.ruoyi.screen.util;

import com.ruoyi.common.core.redis.RedisCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Set;

/**
 * Redis工具类
 * 提供一些RedisCache没有的便捷方法
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
@Slf4j
@Component
public class RedisUtils {

    @Autowired
    private RedisCache redisCache;

    /**
     * 根据模式删除缓存
     * 
     * @param pattern 匹配模式，如 "device:*:TEST001:*"
     * @return 删除的键数量
     */
    public int deleteByPattern(String pattern) {
        try {
            Collection<String> keys = redisCache.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                boolean success = redisCache.deleteObject(keys);
                int count = keys.size();
                log.info("根据模式 {} 删除缓存，共 {} 个键，删除结果: {}", pattern, count, success);
                return success ? count : 0;
            }
            return 0;
        } catch (Exception e) {
            log.error("根据模式 {} 删除缓存失败", pattern, e);
            return 0;
        }
    }

    /**
     * 获取匹配模式的所有键
     * 
     * @param pattern 匹配模式
     * @return 匹配的键集合
     */
    public Collection<String> getKeysByPattern(String pattern) {
        try {
            return redisCache.keys(pattern);
        } catch (Exception e) {
            log.error("获取模式 {} 的键失败", pattern, e);
            return null;
        }
    }

    /**
     * 检查是否存在匹配模式的键
     * 
     * @param pattern 匹配模式
     * @return 是否存在匹配的键
     */
    public boolean hasKeysByPattern(String pattern) {
        try {
            Collection<String> keys = redisCache.keys(pattern);
            return keys != null && !keys.isEmpty();
        } catch (Exception e) {
            log.error("检查模式 {} 的键是否存在失败", pattern, e);
            return false;
        }
    }

    /**
     * 统计匹配模式的键数量
     * 
     * @param pattern 匹配模式
     * @return 匹配的键数量
     */
    public int countKeysByPattern(String pattern) {
        try {
            Collection<String> keys = redisCache.keys(pattern);
            return keys != null ? keys.size() : 0;
        } catch (Exception e) {
            log.error("统计模式 {} 的键数量失败", pattern, e);
            return 0;
        }
    }

    /**
     * 批量检查键是否存在
     * 
     * @param keys 键集合
     * @return 存在的键集合
     */
    public Collection<String> filterExistingKeys(Collection<String> keys) {
        try {
            return keys.stream()
                .filter(key -> redisCache.hasKey(key))
                .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            log.error("批量检查键是否存在失败", e);
            return null;
        }
    }

    /**
     * 清空指定前缀的所有缓存
     * 
     * @param prefix 缓存键前缀
     * @return 删除的键数量
     */
    public int clearCacheByPrefix(String prefix) {
        String pattern = prefix + "*";
        return deleteByPattern(pattern);
    }

    /**
     * 获取缓存使用统计
     * 
     * @param prefix 缓存键前缀
     * @return 统计信息
     */
    public CacheStats getCacheStats(String prefix) {
        try {
            String pattern = prefix + "*";
            Collection<String> keys = redisCache.keys(pattern);
            
            CacheStats stats = new CacheStats();
            stats.setPrefix(prefix);
            stats.setTotalKeys(keys != null ? keys.size() : 0);
            
            if (keys != null && !keys.isEmpty()) {
                // 统计不同类型的键
                long deviceKeys = keys.stream().filter(key -> key.contains(":device:")).count();
                long segmentKeys = keys.stream().filter(key -> key.contains(":segment:")).count();
                long precomputedKeys = keys.stream().filter(key -> key.contains(":precomputed:")).count();
                
                stats.setDeviceKeys((int) deviceKeys);
                stats.setSegmentKeys((int) segmentKeys);
                stats.setPrecomputedKeys((int) precomputedKeys);
            }
            
            return stats;
        } catch (Exception e) {
            log.error("获取缓存统计失败", e);
            return new CacheStats();
        }
    }

    /**
     * 缓存统计信息类
     */
    public static class CacheStats {
        private String prefix;
        private int totalKeys;
        private int deviceKeys;
        private int segmentKeys;
        private int precomputedKeys;

        // Getters and Setters
        public String getPrefix() { return prefix; }
        public void setPrefix(String prefix) { this.prefix = prefix; }

        public int getTotalKeys() { return totalKeys; }
        public void setTotalKeys(int totalKeys) { this.totalKeys = totalKeys; }

        public int getDeviceKeys() { return deviceKeys; }
        public void setDeviceKeys(int deviceKeys) { this.deviceKeys = deviceKeys; }

        public int getSegmentKeys() { return segmentKeys; }
        public void setSegmentKeys(int segmentKeys) { this.segmentKeys = segmentKeys; }

        public int getPrecomputedKeys() { return precomputedKeys; }
        public void setPrecomputedKeys(int precomputedKeys) { this.precomputedKeys = precomputedKeys; }

        @Override
        public String toString() {
            return String.format("CacheStats{prefix='%s', totalKeys=%d, deviceKeys=%d, segmentKeys=%d, precomputedKeys=%d}",
                    prefix, totalKeys, deviceKeys, segmentKeys, precomputedKeys);
        }
    }
}
