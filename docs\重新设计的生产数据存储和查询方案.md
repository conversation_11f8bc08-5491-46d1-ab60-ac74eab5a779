# 重新设计的生产数据存储和查询方案

## 背景

原有的生产数据查询存在历史数据不一致的问题，每次查询同一历史时间段会返回不同的结果。经过分析，问题的根源在于：

1. **数据存储不完整**：IoTDB中缺乏完整的生产状态快照
2. **查询逻辑依赖实时计算**：使用MySQL的比例计算和IoTDB的实时查询
3. **数据源不统一**：不同接口使用不同的查询逻辑

## 新的设计方案

### 1. 整体架构

```
MQTT消息 → 数据解析 → 差值计算 → 存储到IoTDB → 查询接口
    ↓         ↓         ↓          ↓           ↓
  原始数据   产品状态   增量产量   累计产量     一致性数据
    ↓         ↓         ↓          ↓           ↓
  MySQL     Redis     实时计算   时序存储     快速查询
```

### 2. MQTT消息处理重新设计

#### 2.1 消息格式理解
```json
{
    "devName": "QC120",
    "time": 7882724,
    "diState": [0, 1, 0, 0],        // diState[1]=1 表示正在生产索引1的产品
    "counter": [241259, 50, 30, 0], // counter[0] 是主计数器，用于计算产量差值
    "frequency": [0, 0, 0, 0],
    "cycle": [0.616997004, 0, 0, 0],
    "actualData": [241259, 50, 30, 0],
    "speed": [0, 0, 0, 0]
}
```

#### 2.2 核心处理逻辑
1. **解析设备属性配置**：从 `zh_device_type_attr` 表获取产品配置
2. **计算产量增量**：通过Redis缓存上次计数器值，计算差值
3. **确定当前生产产品**：根据 `diState` 数组确定正在生产的产品
4. **更新累计产量**：为当前生产的产品增加产量，其他产品保持不变
5. **存储到IoTDB**：保存所有产品的累计产量和状态

#### 2.3 新的存储结构
```
IoTDB路径结构：
root.{enterprise}.{device}.production.{product_key}.count        # 累计产量
root.{enterprise}.{device}.production.{product_key}.status      # 生产状态 (0/1)
root.{enterprise}.{device}.production.{product_key}.increment   # 本次增量
```

#### 2.4 Redis缓存结构
```
device:{deviceCode}:last_counter     # 上次计数器值
device:{deviceCode}:product_counts   # 各产品累计产量 (Hash)
```

### 3. 数据查询重新设计

#### 3.1 查询优先级
1. **优先使用IoTDB**：所有生产数量数据来自IoTDB
2. **MySQL仅用于配置**：只用于获取设备产品配置信息
3. **Redis用于缓存**：缓存频繁查询的数据

#### 3.2 查询逻辑
```java
// 1. 从IoTDB获取时间序列
SHOW TIMESERIES root.{enterprise}.*.production.*.count

// 2. 计算时间范围内的生产数量
SELECT last_value(count) FROM {timeseries} WHERE time <= {beginTime}
SELECT last_value(count) FROM {timeseries} WHERE time <= {endTime}
// 生产数量 = endCount - beginCount

// 3. 聚合所有设备的数据
// 按产品名称汇总各设备的生产数量
```

### 4. 核心实现

#### 4.1 MQTT消息处理 (`executeMdIOQNew`)
```java
private void executeMdIOQNew(String topic, MqttMessage message) {
    // 1. 解析MQTT消息
    // 2. 获取设备属性配置
    // 3. 计算产量增量
    // 4. 确定当前生产产品
    // 5. 更新产品累计产量
    // 6. 保存到IoTDB
}
```

#### 4.2 生产数据查询服务 (`ProductionDataQueryService`)
```java
public List<ZhDeviceProductionRecord> queryProductQuantityList(
    String productName, String beginTime, String endTime) {
    // 1. 从IoTDB获取所有设备的生产数据
    // 2. 计算时间范围内的生产数量
    // 3. 转换为返回格式
    // 4. 按产品名称聚合数据
}
```

### 5. 产品键规范化

#### 5.1 问题
IoTDB时间序列路径不能以数字开头，而产品名称如 `04BT-1`、`06CF1-2` 会导致 `ILLEGAL_PATH` 错误。

#### 5.2 解决方案
```java
private String normalizeProductKey(String productName) {
    String productKey = productName.replace("-", "_");
    productKey = productKey.replaceAll("[^a-zA-Z0-9_]", "_");
    
    // 如果以数字开头，添加 "prod_" 前缀
    if (Character.isDigit(productKey.charAt(0))) {
        productKey = "prod_" + productKey;
    }
    
    return productKey;
}
```

#### 5.3 转换示例
| 原始产品名 | 规范化产品键 | IoTDB路径示例 |
|-----------|-------------|--------------|
| `04BT-1` | `prod_04BT_1` | `root.enterprise.device.production.prod_04BT_1.count` |
| `06CF1-2` | `prod_06CF1_2` | `root.enterprise.device.production.prod_06CF1_2.count` |
| `CL04CF7-9` | `CL04CF7_9` | `root.enterprise.device.production.CL04CF7_9.count` |

### 6. 数据一致性保证

#### 6.1 历史数据一致性
- **固定时间点查询**：历史时间段的查询结果完全固定
- **累计产量存储**：每次MQTT消息都更新累计产量
- **差值计算**：通过时间范围开始和结束的累计产量差值计算生产数量

#### 6.2 接口数据一致性
- **统一数据源**：`/productQuantity` 和 `/productDetail` 使用相同的IoTDB查询逻辑
- **相同计算方式**：所有接口使用相同的差值计算方法

### 7. 性能优化

#### 7.1 缓存策略
- **Redis缓存**：缓存上次计数器值和产品累计产量
- **时间序列缓存**：缓存频繁查询的时间序列路径

#### 7.2 查询优化
- **批量查询**：一次性获取所有相关时间序列
- **并行处理**：并行计算各时间序列的生产数量
- **结果聚合**：在内存中聚合数据，减少数据库查询

### 8. 部署和验证

#### 8.1 部署步骤
1. **部署新代码**：包含重新设计的MQTT处理和查询逻辑
2. **验证MQTT处理**：确认新的数据存储逻辑正常工作
3. **测试查询接口**：验证历史数据一致性
4. **性能测试**：确认查询性能满足要求

#### 8.2 验证方法
```bash
# 1. 测试产品键规范化
mvn test -Dtest=ProductKeyNormalizationTest

# 2. 测试数据一致性
# 多次查询同一历史时间段，验证结果一致
curl "http://localhost:8080/zhenghe/deviceProductionRecord/productQuantity?beginTime=2025-07-22%2012%3A00%3A00&endTime=2025-07-22%2013%3A00%3A00"

# 3. 验证接口一致性
# 对比 /productQuantity 和 /productDetail 的数据
```

### 9. 监控和维护

#### 9.1 关键指标
- **MQTT消息处理成功率**：监控消息处理异常
- **IoTDB存储成功率**：监控数据存储失败
- **查询响应时间**：监控接口性能
- **数据一致性**：定期验证历史数据一致性

#### 9.2 日志监控
```
# 关键日志
- "处理设备 {} 的生产数据"
- "设备 {} 产品 {} 产量更新"
- "成功保存设备 {} 生产数据到IoTDB"
- "查询产品数量（基于IoTDB）"
```

### 10. 回滚方案

如果新方案出现问题，可以快速回滚：
1. **修改控制器**：将 `productionDataQueryService` 改回 `zhDeviceProductionRecordService`
2. **修改MQTT处理**：将 `executeMdIOQNew` 改回 `executeMdIOQOld`
3. **保持数据完整性**：新存储的IoTDB数据不会影响原有功能

## 总结

新的设计方案通过以下方式解决了历史数据不一致的问题：

1. **完整的数据存储**：每次MQTT消息都保存完整的产品状态和累计产量
2. **统一的查询逻辑**：所有接口使用相同的IoTDB查询逻辑
3. **固定的历史数据**：历史时间段的查询结果完全固定
4. **高效的查询性能**：通过缓存和优化提升查询效率

这个方案确保了数据的一致性、准确性和高性能，为生产数据分析提供了可靠的基础。
