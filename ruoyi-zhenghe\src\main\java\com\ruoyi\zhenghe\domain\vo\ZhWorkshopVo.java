package com.ruoyi.zhenghe.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.zhenghe.domain.ZhWorkshop;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 车间管理对象 zh_workshop
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
public class ZhWorkshopVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    //@Excel(name = "工厂车间id",sort = 1)
    private Long id;

    /** 工厂车间名称 */
    @ApiModelProperty(value = "工厂车间名称")
    @Excel(name = "工厂车间名称",sort = 2)
    private String workshopName;

    /** 车间编码 */
    @ApiModelProperty(value = "车间编码")
    //@Excel(name = "车间编码",sort = 3)
    private String workshopCode;

    /** 父级id 父级为0 */
    @ApiModelProperty(value = "父级id")
    //@Excel(name = "父级id 父级为0",sort = 4)
    private Long parentId;

    /** 部门id */
    private Long deptId;

    /** 子级 */
    private List<ZhWorkshop> children;

    /** 部门名称 */
    @Excel(name = "事业部名称",sort = 1)
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /** 车间总计数 */
    @ApiModelProperty(value = "车间总计数")
    private Integer totalCount;

    /** 设备类型id */
    private Long deviceTypeId;

    @Excel(name = "开始时间",sort = 3)
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @Excel(name = "结束时间",sort = 4)
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @Excel(name = "用电量",sort = 5)
    @ApiModelProperty(value = "车间电量")
    private Double workshopPower;

    @Excel(name = "平段电量",sort = 6)
    @ApiModelProperty(value = "平段电量")
    private Double flatPower;

    @Excel(name = "谷段电量",sort = 8)
    @ApiModelProperty(value = "谷段电量")
    private Double valleyPower;

    @Excel(name = "峰段电量",sort = 7)
    @ApiModelProperty(value = "峰段电量")
    private Double crestPower;

    @Excel(name = "深谷电量",sort = 10)
    @ApiModelProperty(value = "深谷电量")
    private Double barrancaPower;

    @Excel(name = "尖峰电量",sort = 9)
    @ApiModelProperty(value = "尖峰电量")
    private Double spikePower;

}
