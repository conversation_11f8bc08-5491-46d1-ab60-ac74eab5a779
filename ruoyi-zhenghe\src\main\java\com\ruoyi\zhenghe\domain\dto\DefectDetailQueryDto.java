package com.ruoyi.zhenghe.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 瑕疵细分查询请求数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
public class DefectDetailQueryDto {

    /** 产品ID */
    @NotNull(message = "产品ID不能为空")
    @ApiModelProperty(value = "产品ID", required = true)
    private Long productId;

    /** 检测日期 */
    @NotNull(message = "检测日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "检测日期，格式：yyyy-MM-dd", required = true)
    private Date detectionDate;

    /** 页码 */
    @ApiModelProperty(value = "页码，默认1")
    private Integer pageNum = 1;

    /** 每页大小 */
    @ApiModelProperty(value = "每页大小，默认10")
    private Integer pageSize = 10;
}
