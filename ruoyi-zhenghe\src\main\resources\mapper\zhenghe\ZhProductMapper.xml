<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhProductMapper">

    <resultMap type="ZhProduct" id="ZhProductResult">
        <result property="id" column="id"/>
        <result property="productModel" column="product_model"/>
        <result property="pieceCount" column="piece_count"/>
        <result property="sectionCount" column="section_count"/>
        <result property="timingMark" column="timing_mark"/>
        <result property="productImage" column="product_image"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="customerName" column="customer_name"/>
        <result property="machineType" column="machine_type"/>
        <result property="printMark" column="print_mark"/>
        <result property="oe" column="oe"/>
        <result property="chainLength" column="chain_length"/>
        <result property="chainLengthTolerance" column="chain_length_tolerance"/>
        <result property="pinyin" column="pinyin"/>
    </resultMap>

    <sql id="selectZhProductVo">
        SELECT id,
               product_model,
               piece_count,
               section_count,
               timing_mark,
               product_image,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               customer_name,
               machine_type,
               print_mark,
               oe,
               chain_length_tolerance,
               chain_length,
               pinyin
        FROM zh_product
    </sql>

    <select id="selectZhProductList" parameterType="ZhProduct" resultMap="ZhProductResult">
        <include refid="selectZhProductVo"/>
        WHERE 1 = 1
        <if test="productModel != null and productModel != ''">
            AND product_model LIKE CONCAT('%', #{productModel}, '%')
        </if>
        <if test="customerName != null and customerName != ''">
            AND (customer_name LIKE CONCAT('%', #{customerName}, '%')
                 OR pinyin LIKE CONCAT('%', #{customerName}, '%'))
        </if>
        <if test="machineType != null and machineType != ''">
            AND machine_type LIKE CONCAT('%', #{machineType}, '%')
        </if>
        <if test="printMark != null and printMark != ''">
            AND print_mark LIKE CONCAT('%', #{printMark}, '%')
        </if>

        <if test="pieceCount != null">
            AND piece_count = #{pieceCount}
        </if>
        <if test="sectionCount != null">
            AND section_count = #{sectionCount}
        </if>
        <if test="timingMark != null and timingMark != ''">
            AND timing_mark LIKE CONCAT('%', #{timingMark}, '%')
        </if>

        ORDER BY id DESC
    </select>

    <select id="selectZhProductById" parameterType="Long" resultMap="ZhProductResult">
        <include refid="selectZhProductVo"/>
        WHERE id = #{id}
    </select>



    <insert id="insertZhProduct" parameterType="ZhProduct" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO zh_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productModel != null and productModel != ''">product_model,</if>
            <if test="pieceCount != null">piece_count,</if>
            <if test="sectionCount != null">section_count,</if>
            <if test="timingMark != null">timing_mark,</if>
            <if test="productImage != null">product_image,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="machineType != null">machine_type,</if>
            <if test="printMark != null">print_mark,</if>
            <if test="oe != null">oe,</if>
            <if test="chainLength != null">chain_length,</if>
            <if test="chainLengthTolerance != null">chain_length_tolerance,</if>
            <if test="pinyin != null">pinyin,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productModel != null and productModel != ''">#{productModel},</if>
            <if test="pieceCount != null">#{pieceCount},</if>
            <if test="sectionCount != null">#{sectionCount},</if>
            <if test="timingMark != null">#{timingMark},</if>
            <if test="productImage != null">#{productImage},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="machineType != null">#{machineType},</if>
            <if test="printMark != null">#{printMark},</if>
            <if test="oe != null">#{oe},</if>
            <if test="chainLength != null">#{chainLength},</if>
            <if test="chainLengthTolerance != null">#{chainLengthTolerance},</if>
            <if test="pinyin != null">#{pinyin},</if>
        </trim>
    </insert>

    <update id="updateZhProduct" parameterType="ZhProduct">
        UPDATE zh_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="productModel != null and productModel != ''">product_model = #{productModel},</if>
            <if test="pieceCount != null">piece_count = #{pieceCount},</if>
            <if test="sectionCount != null">section_count = #{sectionCount},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="machineType != null">machine_type = #{machineType},</if>
            <if test="printMark != null">print_mark = #{printMark},</if>
            <if test="timingMark != null">timing_mark = #{timingMark},</if>
            <if test="productImage != null">product_image = #{productImage},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="oe != null">oe = #{oe},</if>
            <if test="chainLengthTolerance != null">chain_length_tolerance = #{chainLengthTolerance},</if>
            <if test="chainLength != null">chain_length = #{chainLength},</if>
            <if test="pinyin != null">pinyin = #{pinyin},</if>
        </trim>
        WHERE id = #{id}
    </update>

    <delete id="deleteZhProductById" parameterType="Long">
        DELETE FROM zh_product WHERE id = #{id}
    </delete>

    <delete id="deleteZhProductByIds" parameterType="String">
        DELETE FROM zh_product WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>



    <!-- 获取所有产品型号 -->
    <select id="selectAllProductModels" resultType="String">
        SELECT DISTINCT product_model
        FROM zh_product
        WHERE customer_name = #{productModel}
        ORDER BY product_model ASC
    </select>

    <!-- 获取所有片数 -->
    <select id="selectAllPieceCounts" resultType="Integer">
        SELECT DISTINCT piece_count
        FROM zh_product
        WHERE piece_count IS NOT NULL
        ORDER BY piece_count ASC
    </select>

    <!-- 获取所有节数 -->
    <select id="selectAllSectionCounts" resultType="Integer">
        SELECT DISTINCT section_count
        FROM zh_product
        WHERE section_count IS NOT NULL
        ORDER BY section_count ASC
    </select>

    <!-- 获取所有正时标记 -->
    <select id="selectAllTimingMarks" resultType="String">
        SELECT DISTINCT timing_mark
        FROM zh_product
        WHERE timing_mark IS NOT NULL AND timing_mark != ''
        ORDER BY timing_mark ASC
    </select>

<select id="selectPieceCustomer" resultType="String">
    SELECT DISTINCT customer_name
    FROM zh_product
    WHERE customer_name IS NOT NULL AND customer_name != ''
    <if test="customerName != null and customerName != ''">
        AND (customer_name LIKE CONCAT('%', #{customerName}, '%')
             OR pinyin LIKE CONCAT('%', #{customerName}, '%'))
    </if>
    ORDER BY
        CASE
            WHEN customer_name REGEXP '^[0-9]' THEN 0
            ELSE 1
            END,
        CASE
            WHEN customer_name REGEXP '^[0-9]+' THEN CAST(SUBSTRING(customer_name, 1, REGEXP_INSTR(customer_name, '[^0-9]') - 1) AS UNSIGNED)
            ELSE NULL
            END,
        customer_name COLLATE utf8mb4_zh_0900_as_cs  -- MySQL 8.0+ 中文拼音排序
    -- 或者：CONVERT(customer_name USING gbk)  -- 旧版本 MySQL 兼容方案
</select>

    <!-- 根据产品型号获取片数下拉选项 -->
    <select id="selectPieceCountsByProductModel" parameterType="String" resultType="Integer">
        SELECT DISTINCT piece_count
        FROM zh_product
        WHERE product_model = #{productModel}
          AND piece_count IS NOT NULL
        ORDER BY piece_count ASC
    </select>

    <!-- 根据产品型号和片数获取节数下拉选项 -->
    <select id="selectSectionCountsByProductModelAndPieceCount" resultType="Integer">
        SELECT DISTINCT section_count
        FROM zh_product
        WHERE product_model = #{productModel}
          AND piece_count = #{pieceCount}
          AND section_count IS NOT NULL
        ORDER BY section_count ASC
    </select>

    <!-- 根据产品型号、片数和节数获取正时标记下拉选项 -->
    <select id="selectTimingMarksByProductModelAndPieceCountAndSectionCount" resultType="String">
        SELECT DISTINCT timing_mark
        FROM zh_product
        WHERE product_model = #{productModel}
          AND piece_count = #{pieceCount}
          AND section_count = #{sectionCount}
          AND timing_mark IS NOT NULL AND timing_mark != ''
        ORDER BY timing_mark ASC
    </select>

    <!-- 根据组合条件查询产品 -->
    <select id="selectProductByCombination" resultMap="ZhProductResult">
        <include refid="selectZhProductVo"/>
        WHERE 1 = 1
        <if test="productModel != null and productModel != ''">
            AND product_model = #{productModel}
        </if>
        <if test="pieceCount != null">
            AND piece_count = #{pieceCount}
        </if>
        <if test="sectionCount != null">
            AND section_count = #{sectionCount}
        </if>
        <if test="timingMark != null and timingMark != ''">
            AND timing_mark = #{timingMark}
        </if>
        LIMIT 1
    </select>

    <select id="selectAllProductsForTree" resultMap="ZhProductResult">
        <include refid="selectZhProductVo"/>
        ORDER BY product_model ASC, piece_count ASC, section_count ASC, timing_mark ASC
    </select>

</mapper>
