package com.ruoyi.screen.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.util.iotdb.IoTDBUtil;
import com.ruoyi.zhenghe.domain.ZhDeviceTypeAttr;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.mapper.ZhDeviceTypeAttrMapper;
import com.ruoyi.zhenghe.service.IZhIotEquipmentService;
import com.ruoyi.zhengheiot.service.IotHistoryDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 数据预计算服务
 * 定时预计算热点数据并缓存，减少实时查询压力
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
@Slf4j
@Service
public class DataPrecomputeService {

    @Autowired
    private MultiLevelCacheService cacheService;

    @Autowired
    private IZhIotEquipmentService zhIotEquipmentService;

    @Autowired
    private IoTDBUtil ioTDBUtil;

    @Resource
    private ZhDeviceTypeAttrMapper zhDeviceTypeAttrMapper;

    @Autowired
    private IotHistoryDataService historyDataService;

    // 预计算专用线程池
    private final ExecutorService precomputeExecutor = Executors.newFixedThreadPool(5);

    /**
     * 每5分钟预计算当前时间段的数据
     */
//    @Scheduled(fixedRate = 5 * 60 * 1000) // 5分钟
    public void precomputeCurrentData() {
        log.info("开始预计算当前时间段数据");
        try {
            // 预计算今日数据
            precomputeTodayData();
            // 预计算当前小时数据
            precomputeCurrentHourData();
        } catch (Exception e) {
            log.error("预计算当前数据失败", e);
        }
    }

    /**
     * 每小时预计算历史数据
     */
//    @Scheduled(fixedRate = 60 * 60 * 1000) // 1小时
    public void precomputeHistoricalData() {
        log.info("开始预计算历史数据");
        try {
            // 预计算昨天的数据
            precomputeYesterdayData();
            // 预计算过去7天的数据
            precomputeWeeklyData();
        } catch (Exception e) {
            log.error("预计算历史数据失败", e);
        }
    }

    /**
     * 预计算今日数据
     */
    @Async
    public void precomputeTodayData() {
        DateTime today = DateUtil.beginOfDay(new Date());
        DateTime now = DateUtil.date();
        String startTime = DateUtil.format(today, "yyyy-MM-dd HH:mm:ss");
        String endTime = DateUtil.format(now, "yyyy-MM-dd HH:mm:ss");

        // 获取所有部门的设备
        List<String> deptIds = Arrays.asList("103", "104", "105"); // 主要部门ID
        
        for (String deptId : deptIds) {
            CompletableFuture.runAsync(() -> {
                try {
                    precomputeDeptData(deptId, null, startTime, endTime, "today");
                } catch (Exception e) {
                    log.error("预计算部门 {} 今日数据失败", deptId, e);
                }
            }, precomputeExecutor);
        }
    }

    /**
     * 预计算当前小时数据
     */
    @Async
    public void precomputeCurrentHourData() {
        DateTime hourStart = DateUtil.beginOfHour(new Date());
        DateTime now = DateUtil.date();
        String startTime = DateUtil.format(hourStart, "yyyy-MM-dd HH:mm:ss");
        String endTime = DateUtil.format(now, "yyyy-MM-dd HH:mm:ss");

        List<String> deptIds = Arrays.asList("103", "104", "105");
        
        for (String deptId : deptIds) {
            CompletableFuture.runAsync(() -> {
                try {
                    precomputeDeptData(deptId, null, startTime, endTime, "currentHour");
                } catch (Exception e) {
                    log.error("预计算部门 {} 当前小时数据失败", deptId, e);
                }
            }, precomputeExecutor);
        }
    }

    /**
     * 预计算昨天数据
     */
    @Async
    public void precomputeYesterdayData() {
        DateTime yesterday = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -1));
        DateTime yesterdayEnd = DateUtil.endOfDay(yesterday);
        String startTime = DateUtil.format(yesterday, "yyyy-MM-dd HH:mm:ss");
        String endTime = DateUtil.format(yesterdayEnd, "yyyy-MM-dd HH:mm:ss");

        List<String> deptIds = Arrays.asList("103", "104", "105");
        
        for (String deptId : deptIds) {
            CompletableFuture.runAsync(() -> {
                try {
                    precomputeDeptData(deptId, null, startTime, endTime, "yesterday");
                } catch (Exception e) {
                    log.error("预计算部门 {} 昨日数据失败", deptId, e);
                }
            }, precomputeExecutor);
        }
    }

    /**
     * 预计算过去7天数据
     */
    @Async
    public void precomputeWeeklyData() {
        List<String> deptIds = Arrays.asList("103", "104", "105");
        
        for (String deptId : deptIds) {
            CompletableFuture.runAsync(() -> {
                try {
                    for (int i = 1; i <= 7; i++) {
                        DateTime dayStart = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -i));
                        DateTime dayEnd = DateUtil.endOfDay(dayStart);
                        String startTime = DateUtil.format(dayStart, "yyyy-MM-dd HH:mm:ss");
                        String endTime = DateUtil.format(dayEnd, "yyyy-MM-dd HH:mm:ss");
                        
                        precomputeDeptData(deptId, null, startTime, endTime, "day-" + i);
                        
                        // 避免过于频繁的查询
                        Thread.sleep(1000);
                    }
                } catch (Exception e) {
                    log.error("预计算部门 {} 周数据失败", deptId, e);
                }
            }, precomputeExecutor);
        }
    }

    /**
     * 预计算部门数据
     */
    private void precomputeDeptData(String deptId, String workshopId, String startTime, String endTime, String timeType) {
        try {
            // 获取设备列表
            ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
            zhIotEquipment.setDeptId(Long.valueOf(deptId));
            if (workshopId != null) {
                zhIotEquipment.setWorkshopId(Long.valueOf(workshopId));
            }

            List<ZhIotEquipment> equipmentList = zhIotEquipmentService.selectZhIotEquipmentListNoScope(zhIotEquipment);
            if (equipmentList == null || equipmentList.isEmpty()) {
                return;
            }

            // 预计算设备级数据
            precomputeDeviceData(equipmentList, startTime, endTime);

            // 预计算聚合数据
            precomputeAggregatedData(deptId, workshopId, equipmentList, startTime, endTime, timeType);

        } catch (Exception e) {
            log.error("预计算部门数据失败: deptId={}, timeType={}", deptId, timeType, e);
        }
    }

    /**
     * 预计算设备级数据
     */
    private void precomputeDeviceData(List<ZhIotEquipment> equipmentList, String startTime, String endTime) {
        // 过滤非电表设备进行生产数据预计算
        List<ZhIotEquipment> productionEquipment = equipmentList.stream()
            .filter(equipment -> !equipment.getEquipmentName().contains("电表"))
            .collect(Collectors.toList());

        for (ZhIotEquipment equipment : productionEquipment) {
            CompletableFuture.runAsync(() -> {
                try {
                    // 检查缓存是否已存在
                    Integer cachedCount = cacheService.getDeviceProductionCount(equipment.getEquipmentCode(), startTime, endTime);
                    if (cachedCount == null) {
                        // 查询并缓存生产计数
                        int count = ioTDBUtil.queryIotEquipmentProductionCount(Constants.TENANT, equipment.getEquipmentCode(), null, startTime, endTime);
                        cacheService.setDeviceProductionCount(equipment.getEquipmentCode(), startTime, endTime, count);
                    }

                    // 查询并缓存生产时长
                    String code = getDeviceStatusCode(equipment);
                    Integer cachedDuration = cacheService.getDeviceProductionDuration(equipment.getEquipmentCode(), code, startTime, endTime);
                    if (cachedDuration == null) {
                        int duration = ioTDBUtil.queryIotEquipmentProductionDuration(Constants.TENANT, equipment.getEquipmentCode(), code, startTime, endTime);
                        cacheService.setDeviceProductionDuration(equipment.getEquipmentCode(), code, startTime, endTime, duration);
                    }

                } catch (Exception e) {
                    log.error("预计算设备 {} 数据失败", equipment.getEquipmentName(), e);
                }
            }, precomputeExecutor);
        }

        // 过滤电表设备进行能耗数据预计算
        List<ZhIotEquipment> energyEquipment = equipmentList.stream()
            .filter(equipment -> equipment.getEquipmentName() != null && equipment.getEquipmentName().contains("电表"))
            .collect(Collectors.toList());

        for (ZhIotEquipment equipment : energyEquipment) {
            CompletableFuture.runAsync(() -> {
                try {
                    Integer cachedEnergy = cacheService.getDeviceEnergyData(equipment.getId(), startTime, endTime);
                    if (cachedEnergy == null) {
                        // 查询并缓存能耗数据
                        final cn.hutool.json.JSONObject totalPower = historyDataService.getHistoryDataTagList(
                            equipment.getId(), "TotalPower", startTime, endTime, null, null);
                        final int energy = ioTDBUtil.getTotalPower(totalPower);
                        cacheService.setDeviceEnergyData(equipment.getId(), startTime, endTime, energy);
                    }
                } catch (Exception e) {
                    log.error("预计算设备 {} 能耗数据失败", equipment.getEquipmentName(), e);
                }
            }, precomputeExecutor);
        }
    }

    /**
     * 预计算聚合数据
     */
    private void precomputeAggregatedData(String deptId, String workshopId, List<ZhIotEquipment> equipmentList, 
                                        String startTime, String endTime, String timeType) {
        try {
            // 预计算产量聚合数据
            JSONObject yieldData = new JSONObject();
            int totalYield = 0;
            
            for (ZhIotEquipment equipment : equipmentList) {
                if (!equipment.getEquipmentName().contains("电表")) {
                    Integer count = cacheService.getDeviceProductionCount(equipment.getEquipmentCode(), startTime, endTime);
                    if (count != null) {
                        totalYield += count;
                    }
                }
            }
            
            yieldData.put("totalYield", totalYield);
            yieldData.put("timeRange", timeType);
            yieldData.put("startTime", startTime);
            yieldData.put("endTime", endTime);
            
            // 缓存聚合结果
            cacheService.setTimeSegmentData(deptId, workshopId, "yield", timeType, yieldData);

            log.debug("预计算聚合数据完成: deptId={}, timeType={}, totalYield={}", deptId, timeType, totalYield);

        } catch (Exception e) {
            log.error("预计算聚合数据失败", e);
        }
    }

    /**
     * 获取设备状态码
     */
    private String getDeviceStatusCode(ZhIotEquipment equipment) {
        try {
            ZhDeviceTypeAttr zhDeviceTypeAttr = new ZhDeviceTypeAttr();
            zhDeviceTypeAttr.setTslId(equipment.getDeviceTypeId());
            zhDeviceTypeAttr.setAttrCode("Status");
            List<ZhDeviceTypeAttr> zhDeviceTypeAttrList = zhDeviceTypeAttrMapper.selectZhDeviceTypeAttrList(zhDeviceTypeAttr);
            if (ObjectUtil.isNotNull(zhDeviceTypeAttrList) && zhDeviceTypeAttrList.size() > 0) {
                return zhDeviceTypeAttrList.get(0).getAttrCode();
            }
        } catch (Exception e) {
            log.error("获取设备状态码失败", e);
        }
        return "Power_ON";
    }

    /**
     * 手动触发预计算
     */
    public void triggerPrecompute(String deptId, String timeType) {
        log.info("手动触发预计算: deptId={}, timeType={}", deptId, timeType);
        
        CompletableFuture.runAsync(() -> {
            try {
                DateTime start, end;
                switch (timeType) {
                    case "today":
                        start = DateUtil.beginOfDay(new Date());
                        end = DateUtil.date();
                        break;
                    case "yesterday":
                        start = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -1));
                        end = DateUtil.endOfDay(start);
                        break;
                    default:
                        return;
                }
                
                String startTime = DateUtil.format(start, "yyyy-MM-dd HH:mm:ss");
                String endTime = DateUtil.format(end, "yyyy-MM-dd HH:mm:ss");
                
                precomputeDeptData(deptId, null, startTime, endTime, timeType);
                
            } catch (Exception e) {
                log.error("手动预计算失败", e);
            }
        }, precomputeExecutor);
    }
}
