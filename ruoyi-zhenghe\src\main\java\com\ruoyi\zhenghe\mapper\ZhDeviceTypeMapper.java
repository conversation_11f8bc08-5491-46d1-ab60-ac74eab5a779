package com.ruoyi.zhenghe.mapper;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhDeviceType;

/**
 * 设备类型/Iot物模型Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface ZhDeviceTypeMapper 
{
    /**
     * 查询设备类型/Iot物模型
     * 
     * @param id 设备类型/Iot物模型主键
     * @return 设备类型/Iot物模型
     */
    public ZhDeviceType selectZhDeviceTypeById(Long id);

    /**
     * 查询设备类型/Iot物模型列表
     * 
     * @param zhDeviceType 设备类型/Iot物模型
     * @return 设备类型/Iot物模型集合
     */
    public List<ZhDeviceType> selectZhDeviceTypeList(ZhDeviceType zhDeviceType);

//    public List<ZhDeviceType> selectZhDeviceTypeListByEquipmentId(Long deviceId);

    /**
     * 新增设备类型/Iot物模型
     * 
     * @param zhDeviceType 设备类型/Iot物模型
     * @return 结果
     */
    public int insertZhDeviceType(ZhDeviceType zhDeviceType);

    /**
     * 修改设备类型/Iot物模型
     * 
     * @param zhDeviceType 设备类型/Iot物模型
     * @return 结果
     */
    public int updateZhDeviceType(ZhDeviceType zhDeviceType);

    /**
     * 删除设备类型/Iot物模型
     * 
     * @param id 设备类型/Iot物模型主键
     * @return 结果
     */
    public int deleteZhDeviceTypeById(Long id);

    /**
     * 批量删除设备类型/Iot物模型
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZhDeviceTypeByIds(Long[] ids);


}
