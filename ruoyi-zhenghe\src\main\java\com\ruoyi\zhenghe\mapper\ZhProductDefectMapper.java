package com.ruoyi.zhenghe.mapper;

import com.ruoyi.zhenghe.domain.ZhProductDefect;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品瑕疵Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
public interface ZhProductDefectMapper 
{
    /**
     * 查询产品瑕疵
     * 
     * @param id 产品瑕疵主键
     * @return 产品瑕疵
     */
    public ZhProductDefect selectZhProductDefectById(Long id);

    /**
     * 查询产品瑕疵列表
     * 
     * @param zhProductDefect 产品瑕疵
     * @return 产品瑕疵集合
     */
    public List<ZhProductDefect> selectZhProductDefectList(ZhProductDefect zhProductDefect);

    /**
     * 根据产品ID查询瑕疵列表
     * 
     * @param productId 产品ID
     * @return 产品瑕疵集合
     */
    public List<ZhProductDefect> selectZhProductDefectListByProductId(Long productId);

    /**
     * 根据产品ID查询瑕疵列表
     * 只查启用的
     *
     * @param productId 产品ID
     * @return 产品瑕疵集合
     */
    public List<ZhProductDefect> selectZhProductDefectListByProductIdOpen(Long productId);

    /**
     * 新增产品瑕疵
     * 
     * @param zhProductDefect 产品瑕疵
     * @return 结果
     */
    public int insertZhProductDefect(ZhProductDefect zhProductDefect);

    /**
     * 修改产品瑕疵
     * 
     * @param zhProductDefect 产品瑕疵
     * @return 结果
     */
    public int updateZhProductDefect(ZhProductDefect zhProductDefect);

    /**
     * 删除产品瑕疵
     * 
     * @param id 产品瑕疵主键
     * @return 结果
     */
    public int deleteZhProductDefectById(Long id);

    /**
     * 批量删除产品瑕疵
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZhProductDefectByIds(Long[] ids);

    /**
     * 根据产品ID删除瑕疵
     * 
     * @param productId 产品ID
     * @return 结果
     */
    public int deleteZhProductDefectByProductId(Long productId);

    /**
     * 批量更新瑕疵状态
     * 
     * @param ids 瑕疵ID数组
     * @param status 状态
     * @return 结果
     */
int updateZhProductDefectStatus(@Param("ids") Long[] ids, @Param("status") Integer status);

    /**
     * 获取产品下一个排序号
     * 
     * @param productId 产品ID
     * @return 下一个排序号
     */
    public Integer getNextSortOrder(Long productId);
}
