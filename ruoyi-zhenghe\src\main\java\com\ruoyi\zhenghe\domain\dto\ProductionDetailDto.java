package com.ruoyi.zhenghe.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 生产明细数据DTO
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@ApiModel(value = "ProductionDetailDto", description = "生产明细数据DTO")
public class ProductionDetailDto {

    /** 设备编码 */
    @ApiModelProperty(value = "设备编码")
    private String deviceCode;

    /** 设备名称 */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    /** 产品名称 */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /** 所属事业部 */
    @ApiModelProperty(value = "所属事业部")
    private String deptName;

    /** 所属车间 */
    @ApiModelProperty(value = "所属车间")
    private String workshopName;

    /** 生产开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生产开始时间")
    private Date startTime;

    /** 生产结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生产结束时间")
    private Date endTime;

    /** 生产时长(分钟) */
    @ApiModelProperty(value = "生产时长(分钟)")
    private Integer productionDuration;

    /** 生产时长(格式化显示) */
    @ApiModelProperty(value = "生产时长(格式化显示)")
    private String productionDurationStr;

    /** 本次生产数量 */
    @ApiModelProperty(value = "本次生产数量")
    private Integer productionCount;

    /** 状态（0-结束，1-进行中） */
    @ApiModelProperty(value = "状态（0-结束，1-进行中）")
    private Integer status;

    /** 状态描述 */
    @ApiModelProperty(value = "状态描述")
    private String statusDesc;

}
