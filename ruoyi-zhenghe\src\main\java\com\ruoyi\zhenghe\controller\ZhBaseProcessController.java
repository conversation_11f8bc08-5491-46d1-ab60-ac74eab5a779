package com.ruoyi.zhenghe.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import io.swagger.annotations.*;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.zhenghe.domain.ZhBaseProcess;
import com.ruoyi.zhenghe.service.IZhBaseProcessService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 基础数据-工序Controller
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
@Api(tags = "基础数据-工序")
@RestController
@RequestMapping("/zhenghe/process")
public class ZhBaseProcessController extends BaseController
{
    @Autowired
    private IZhBaseProcessService zhBaseProcessService;

    /**
     * 查询基础数据-工序列表
     */
    @ApiOperation(value = "查询基础数据-工序列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processName",value = "工序名称",required = false,paramType = "query",dataType = "String"),
            @ApiImplicitParam(name = "processCode",value = "工序编码",required = false,paramType = "query",dataType = "String"),
            @ApiImplicitParam(name = "pageNum",value = "页数",required = true,paramType = "query",dataType = "integer"),
            @ApiImplicitParam(name = "pageSize",value = "条数",required = true,paramType = "query",dataType = "integer")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:process:list')")
    //@DataScope(deptAlias = "b")
    @GetMapping("/list")
    public TableDataInfo list(ZhBaseProcess zhBaseProcess)
    {
        startPage();
        List<ZhBaseProcess> list = zhBaseProcessService.selectZhBaseProcessList(zhBaseProcess);
        return getDataTable(list);
    }

    /**
     * 导出基础数据-工序列表
     */
    @ApiOperation(value = "导出基础数据-工序列表")
    @PreAuthorize("@ss.hasPermi('zhenghe:process:export')")
    @Log(title = "基础数据-工序", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhBaseProcess zhBaseProcess)
    {
        List<ZhBaseProcess> list = zhBaseProcessService.selectZhBaseProcessList(zhBaseProcess);
        ExcelUtil<ZhBaseProcess> util = new ExcelUtil<ZhBaseProcess>(ZhBaseProcess.class);
        util.exportExcel(response, list, "基础数据-工序数据");
    }

    /**
     * 导入基础数据-工序列表
     */
    @ApiOperation(value = "导入基础数据-工序列表")
    //@PreAuthorize("@ss.hasPermi('zhenghe:process:import')")
    @Log(title = "基础数据-工序", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws IOException {
        ExcelUtil<ZhBaseProcess> util = new ExcelUtil<ZhBaseProcess>(ZhBaseProcess.class);
        List<ZhBaseProcess> zhBaseProcessList = util.importExcel(file.getInputStream());
        if (StringUtils.isNull(zhBaseProcessList) || zhBaseProcessList.size()==0){
            throw new ServiceException("导入工序数据不能为空");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ZhBaseProcess zhBaseProcess : zhBaseProcessList) {
            try {
                zhBaseProcess.setDeptId(SecurityUtils.getDeptId());
                zhBaseProcessService.insertZhBaseProcess(zhBaseProcess);
                successNum++;
                successMsg.append("<br/>" + successNum + "、工序 " + zhBaseProcess.getProcessName() + " 导入成功");
            }
            catch (Exception e){
                failureNum++;
                String msg = "<br/>" + failureNum + "、工序 " + zhBaseProcess.getProcessName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum>0){
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return AjaxResult.success(successMsg.toString());
    }

    /**
     * 导出基础数据-工序列表模板
     */
    @ApiOperation(value = "导出基础数据-工序列表模板")
    @Log(title = "基础数据-工序", businessType = BusinessType.EXPORT)
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response, ZhBaseProcess zhBaseProcess)
    {
        ExcelUtil<ZhBaseProcess> util = new ExcelUtil<ZhBaseProcess>(ZhBaseProcess.class);
        util.exportExcel(response, new ArrayList<>(), "基础数据-工序数据");
    }

    /**
     * 获取基础数据-工序详细信息
     */
    @PreAuthorize("@ss.hasPermi('zhenghe:process:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "查询基础数据-工序详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "工序id",required = true,paramType = "path",dataType = "long")
    })
    public R<ZhBaseProcess> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(zhBaseProcessService.selectZhBaseProcessById(id));
    }

    /**
     * 新增基础数据-工序
     */
    @ApiOperation(value = "新增基础数据-工序")
    @PreAuthorize("@ss.hasPermi('zhenghe:process:add')")
    @Log(title = "基础数据-工序", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ZhBaseProcess zhBaseProcess)
    {
        return toAjax(zhBaseProcessService.insertZhBaseProcess(zhBaseProcess));
    }

    /**
     * 修改基础数据-工序
     */
    @ApiOperation(value = "修改基础数据-工序")
    @PreAuthorize("@ss.hasPermi('zhenghe:process:edit')")
    @Log(title = "基础数据-工序", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ZhBaseProcess zhBaseProcess)
    {
        return toAjax(zhBaseProcessService.updateZhBaseProcess(zhBaseProcess));
    }

    /**
     * 删除基础数据-工序
     */
    @ApiOperation(value = "删除基础数据-工序")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids",value = "工序id",required = true,paramType = "path",dataType = "long")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:process:remove')")
    @Log(title = "基础数据-工序", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(zhBaseProcessService.deleteZhBaseProcessByIds(ids));
    }
}
