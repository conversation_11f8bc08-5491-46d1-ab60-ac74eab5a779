package com.ruoyi.zhenghe.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 基础数据-工序对象 zh_base_process
 * 
 * <AUTHOR>
 * @date 2025-03-07
 */
@ApiModel(value = "ZhBaseProcess",description = "工序对象")
public class ZhBaseProcess extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 工序名称 */
    @ApiModelProperty(value = "工序名称")
    @Excel(name = "工序名称",sort = 1)
    private String processName;

    /** 排序 */
    //@Excel(name = "排序",sort = 3)
    private Long processSort;

    /** 工序编码 */
    @ApiModelProperty(value = "工序编码")
    @Excel(name = "工序编码",sort = 2)
    private String processCode;

    /** 部门id */
    private Long deptId;

    /** 用户id */
    private Long userId;

    /** 工序关联设备总生产数 */
    @ApiModelProperty(value = "工序关联设备总生产数")
    private Integer totalCount;

    /** 车间id */
    private Long workshopId;

    /** 设备类型id */
    private Long deviceTypeId;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @Excel(name = "部门名称",sort = 3)
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Long getWorkshopId() {
        return workshopId;
    }

    public void setWorkshopId(Long workshopId) {
        this.workshopId = workshopId;
    }

    public Long getDeviceTypeId() {
        return deviceTypeId;
    }

    public void setDeviceTypeId(Long deviceTypeId) {
        this.deviceTypeId = deviceTypeId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setProcessName(String processName) 
    {
        this.processName = processName;
    }

    public String getProcessName() 
    {
        return processName;
    }

    public void setProcessSort(Long processSort) 
    {
        this.processSort = processSort;
    }

    public Long getProcessSort() 
    {
        return processSort;
    }

    public void setProcessCode(String processCode) 
    {
        this.processCode = processCode;
    }

    public String getProcessCode() 
    {
        return processCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("processName", getProcessName())
            .append("processSort", getProcessSort())
            .append("processCode", getProcessCode())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
