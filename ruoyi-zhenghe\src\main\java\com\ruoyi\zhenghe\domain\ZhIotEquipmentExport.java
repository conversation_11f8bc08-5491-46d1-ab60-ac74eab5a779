package com.ruoyi.zhenghe.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 物联网设备明细对象 zh_iot_equipment
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
public class ZhIotEquipmentExport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private Long id;

    /** 设备名称 */
    @Excel(name = "设备名称",sort = 1)
    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    /** 设备编号 */
    @Excel(name = "设备编号",sort = 2)
    @ApiModelProperty(value = "设备编号")
    private String equipmentCode;

    /** 设备类型id */
    @ApiModelProperty(value = "设备类型id")
    private Long deviceTypeId;

    /** 设备类型 */
    @Excel(name = "设备类型",sort = 3)
    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    /** 设备图片 */
    @ApiModelProperty(value = "设备图片地址")
    private String equipmentImg;

    /** 序号 */
    @ApiModelProperty(value = "排序号")
    private Long equipmentSort;

    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    /** $column.columnComment */
    @ApiModelProperty(value = "部门id，事业部id")
    private Long deptId;

    @Excel(name = "部门",sort = 4)
    @ApiModelProperty(value = "部门")
    private String deptName;

    /** $column.columnComment */
    @ApiModelProperty(value = "生产产品型号id")
    private String openId;

    /** 车间id */
    @ApiModelProperty(value = "车间id")
    private Long workshopId;

    @Excel(name = "车间",sort = 5)
    @ApiModelProperty(value = "车间")
    private String workshopName;

    /** 工序id */
    @ApiModelProperty(value = "工序id")
    private Long processId;


    @Excel(name = "工序",sort = 6)
    @ApiModelProperty(value = "工序")
    private String process;

    private String ex1;

    private String ex2;
    @Excel(name = "网关",sort = 7)
    @ApiModelProperty(value = "网关")
    private String gatewayCode;

    @Excel(name = "EAM",sort = 8)
    @ApiModelProperty(value = "EAM")
    private String eamCode;

    @Excel(name = "理论产量",sort = 9)
    @ApiModelProperty(value = "理论产量")
    private double theoreticalYield;


//    @Excel(name = "开始时间")
    @ApiModelProperty(value = "开始时间")
    private String startTime;

//    @Excel(name = "结束时间")
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /** 生产计数 */
//    @Excel(name = "生产计数")
    @ApiModelProperty(value = "生产计数")
    private Object count;
    /** 运行时长 */
//    @Excel(name = "运行时长")
    @ApiModelProperty(value = "运行时长")
    private Object operTime;
    /** 每件运行时长 */
//    @Excel(name = "每件运行时长")
    @ApiModelProperty(value = "每件运行时长")
    private Object proTime;

}
