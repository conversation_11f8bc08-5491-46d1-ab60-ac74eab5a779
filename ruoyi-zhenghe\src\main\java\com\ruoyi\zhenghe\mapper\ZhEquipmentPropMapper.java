package com.ruoyi.zhenghe.mapper;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhEquipmentProp;

/**
 * 设备属性配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface ZhEquipmentPropMapper 
{
    /**
     * 查询设备属性配置
     * 
     * @param id 设备属性配置主键
     * @return 设备属性配置
     */
    public ZhEquipmentProp selectZhEquipmentPropById(Long id);

    public ZhEquipmentProp selectZhEquipmentPropByAttrId(Long id);

    /**
     * 查询设备属性配置列表
     * 
     * @param zhEquipmentProp 设备属性配置
     * @return 设备属性配置集合
     */
    public List<ZhEquipmentProp> selectZhEquipmentPropList(ZhEquipmentProp zhEquipmentProp);

    /**
     * 新增设备属性配置
     * 
     * @param zhEquipmentProp 设备属性配置
     * @return 结果
     */
    public int insertZhEquipmentProp(ZhEquipmentProp zhEquipmentProp);

    /**
     * 修改设备属性配置
     * 
     * @param zhEquipmentProp 设备属性配置
     * @return 结果
     */
    public int updateZhEquipmentProp(ZhEquipmentProp zhEquipmentProp);

    /**
     * 删除设备属性配置
     * 
     * @param id 设备属性配置主键
     * @return 结果
     */
    public int deleteZhEquipmentPropById(Long id);

    /**
     * 批量删除设备属性配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZhEquipmentPropByIds(Long[] ids);

    /**
     * 根据设备id查询设备已配置属性中勾选了报警配置的属性集合
     *
     * @param id 设备id
     * @return 设备属性配置集合
     */
    public List<ZhEquipmentProp> selectZhEquipmentPropByEquipmentIdAndAlarm(Long id);

}
