package com.ruoyi.zhengheiot.controller;


import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.util.iotdb.IoTDBUtil;
import com.ruoyi.zhenghe.domain.*;
import com.ruoyi.zhenghe.service.*;
import com.ruoyi.zhengheiot.domain.AlarmVo;
import com.ruoyi.zhengheiot.domain.HistoryData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Slf4j
@Api(tags = "设备数据管理")
@RestController
@RequestMapping("/zhengheiot/data/management")
public class DataManagementController extends BaseController {


    @Resource
    IoTDBUtil iotDBUtil;

    @Autowired
    private IZhIotEquipmentService zhIotEquipmentService;

    @Autowired
    private IZhDeviceTypeAttrService zhDeviceTypeAttrService;

    @Autowired
    IZhEquipmentPropService zhEquipmentPropService;

    @Autowired
    IZhWorkshopService zhWorkshopService;

    @Autowired
    private IZhBaseProcessService zhBaseProcessService;

    @Autowired
    private RedisCache redisCache;

    @ApiOperation("设备运行图接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "设备的id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "deviceTypeId", value = "设备类型id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "deptId", value = "事业部id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "workshopId", value = "车间id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, paramType = "query", dataType = "string")
    })
    @GetMapping("/device/status")
    public AjaxResult deviceList(@RequestParam(required = false) Long id,
                                 @RequestParam(required = false) Long deviceTypeId,
                                 @RequestParam(required = false) Long deptId,
                                 @RequestParam(required = false) Long workshopId,
                                 @RequestParam(required = false) String startTime,
                                 @RequestParam(required = false) String endTime) throws Exception {
        JSONObject result = new JSONObject();
        // 获取设备清单
        ZhIotEquipment query = new ZhIotEquipment();
        if (id != null) {
            query.setId(id);
        }
        if (deviceTypeId != null) {
            query.setDeviceTypeId(deviceTypeId);
        }
        if (deptId != null) {
            query.setDeptId(deptId);
        }
        if (workshopId != null) {
            query.setWorkshopId(workshopId);
        }

        List<ZhIotEquipment> list = zhIotEquipmentService.selectZhIotEquipmentList(query);
        if (list != null && list.size() > 0) {
        } else {
            throw new Exception("暂无可用设备");
        }
        if (startTime == null || endTime == null) {
            startTime = DateUtil.format(DateUtil.offsetHour(new Date(), -3), "yyyy-MM-dd HH:mm:ss");
            endTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        } else {

        }
        for (ZhIotEquipment temp : list) {
            JSONObject object = new JSONObject();
            object.put("data", getStatusTime(Constants.TENANT, temp.getEquipmentCode(), "Product_Status", startTime, endTime));
            object.put("sort", temp.getEquipmentSort());
            result.put(temp.getEquipmentName(), object);
        }
        return AjaxResult.success(result);
    }

    @ApiOperation("设备运行状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "设备的id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "deviceTypeId", value = "设备类型id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "deptId", value = "事业部id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "workshopId", value = "车间id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "pageSize", value = "每页显示记录数", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageNum", value = "分页页数", required = false, paramType = "query", dataType = "int")
    })

    /**
     * 获取设备运行状态列表
     *
     * <p>该接口查询指定时间范围内设备的运行状态统计信息，包括：</p>
     * <ul>
     *   <li>开机时间（分钟）：Power_ON=1的分钟数</li>
     *   <li>待机时间（分钟）：Power_ON=0的分钟数</li>
     *   <li>关机时间（分钟）：总时间减去开机和待机时间</li>
     *   <li>OEE（设备综合效率）：开机时间占总时间的百分比</li>
     *   <li>利用率：开机时间占总时间的百分比</li>
     * </ul>
     *
     * <p><strong>优化说明：</strong></p>
     * <p>修复了原有SQL查询中一分钟内多条数据重复计算的问题。现在按分钟分组统计，
     * 确保每分钟只计算一次，避免了状态数据在同一分钟内多次记录导致的时间重复计算。</p>
     *
     * @param query 查询条件，包含设备筛选条件和时间范围
     * @return 设备运行状态统计数据
     */
    @GetMapping("/device/status/list")
    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:list')")
    public TableDataInfo getRunningState(ZhIotEquipment query) {
        // 初始化返回对象
        TableDataInfo dataInfo = new TableDataInfo();
        // 初始化结果数组
        JSONArray ansArray = new JSONArray();


        // 设置默认时间范围
        String startTime = query.getStartTime();
        String endTime = query.getEndTime();
        if (startTime == null || endTime == null) {
            startTime = DateUtil.format(DateUtil.beginOfDay(new Date()), "yyyy-MM-dd HH:mm:ss");
            endTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        }
        List<ZhIotEquipment> equipmentList = zhIotEquipmentService.selectZhIotEquipmentList(query);

        // 设置总记录数
        dataInfo.setTotal(equipmentList.size());

        // 处理分页逻辑
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        try {
            equipmentList = equipmentList.subList((pageNum - 1) * pageSize, (Math.min(equipmentList.size(), pageNum * pageSize)));
        } catch (Exception e) {
        }

        // 遍历设备列表，计算每个设备的运行状态
        for (ZhIotEquipment equipment : equipmentList) {
            JSONObject object = new JSONObject();
            object.put("equipmentName", equipment.getEquipmentName());
            object.put("equipmentCode", equipment.getEquipmentCode());
            object.put("deptName", equipment.getDeptName());
            object.put("workshopName", equipment.getWorkshopName());
            object.put("deviceType", equipment.getDeviceType());
            object.put("startTime", startTime);
            object.put("endTime", endTime);

            // 初始化状态计数器（单位：分钟）
            long powerOnMinutes = 0;    // 开机分钟数 (Power_ON=1)
            long standbyMinutes = 0;    // 待机分钟数 (Power_ON=0)
            long powerOffMinutes = 0;   // 关机分钟数（总时间-开机-待机）

            // 计算总时间范围（分钟）
            final long totalMinutes = DateUtil.between(DateUtil.parse(startTime), DateUtil.parse(endTime), DateUnit.MINUTE);

            try {
                // 查询设备开机状态的分钟数（Power_ON=1）
                // 优化后的查询避免了一分钟内多条数据重复计算的问题
                powerOnMinutes = iotDBUtil.getCountTag(Constants.TENANT, equipment.getEquipmentCode(), "Power_ON", "1.0", startTime, endTime);

                // 查询设备待机状态的分钟数（Power_ON=0）
                standbyMinutes = iotDBUtil.getCountTag(Constants.TENANT, equipment.getEquipmentCode(), "Power_ON", "0.0", startTime, endTime);

                // 数据合理性验证和修正
                long[] correctedTimes = validateAndCorrectStateTime(equipment.getEquipmentCode(), powerOnMinutes, standbyMinutes, totalMinutes);
                powerOnMinutes = correctedTimes[0];
                standbyMinutes = correctedTimes[1];

            } catch (IoTDBConnectionException e) {
                log.error("IoTDB连接异常 - 设备: {}", equipment.getEquipmentCode(), e);
            } catch (StatementExecutionException e) {
                log.error("IoTDB查询执行异常 - 设备: {}", equipment.getEquipmentCode(), e);
            }

            // 设置运行状态数据
            object.put("powerOn", powerOnMinutes);
            object.put("Standby", standbyMinutes);

            // 计算设备关闭时间（总时间减去开机和待机时间）
            powerOffMinutes = Math.max(0, totalMinutes - powerOnMinutes - standbyMinutes);
            object.put("powerOff", powerOffMinutes);

            // 计算OEE（设备综合效率）= 开机时间 / 总时间 * 100%
            double OEE = totalMinutes == 0 ? 0 : (1.0*powerOnMinutes / totalMinutes) * 100;
            object.put("OEE", String.format("%.3f", OEE));

            // 计算利用率（设备利用率）= 开机时间 / 总时间 * 100%
            double utilizationRate = totalMinutes == 0 ? 0 : (1.0*powerOnMinutes / totalMinutes) * 100;
            object.put("utilizationRate", String.format("%.3f", utilizationRate));

            // 将结果添加到结果数组
            ansArray.add(object);
        }
        // 设置返回值
        dataInfo.setRows(ansArray);
        dataInfo.setCode(200);
        dataInfo.setMsg("");
        return dataInfo;
    }

    /**
     * 验证和修正设备状态时间数据
     * 确保开机时间+待机时间不超过总查询时间
     *
     * @param deviceCode 设备编码
     * @param powerOnMinutes 开机分钟数
     * @param standbyMinutes 待机分钟数
     * @param totalMinutes 总查询时间分钟数
     * @return 修正后的时间数组 [开机分钟数, 待机分钟数]
     */
    private long[] validateAndCorrectStateTime(String deviceCode, long powerOnMinutes, long standbyMinutes, long totalMinutes) {
        long totalStateMinutes = powerOnMinutes + standbyMinutes;

        // 如果状态时间总和超过查询区间，进行修正
        if (totalStateMinutes > totalMinutes) {
            log.warn("设备状态时间异常需要修正 - 设备: {}, 开机: {}分钟, 待机: {}分钟, 总计: {}分钟, 查询区间: {}分钟",
                    deviceCode, powerOnMinutes, standbyMinutes, totalStateMinutes, totalMinutes);

            if (totalStateMinutes == 0) {
                // 避免除零错误
                return new long[]{0, 0};
            }

            // 按比例缩放修正数据，保持原有比例关系
            double scaleFactor = (double) totalMinutes / totalStateMinutes;
            long correctedPowerOn = Math.round(powerOnMinutes * scaleFactor);
            long correctedStandby = Math.round(standbyMinutes * scaleFactor);

            // 确保修正后的总和不超过总时间（处理四舍五入误差）
            long correctedTotal = correctedPowerOn + correctedStandby;
            if (correctedTotal > totalMinutes) {
                // 从较大的值中减去超出部分
                if (correctedPowerOn >= correctedStandby) {
                    correctedPowerOn -= (correctedTotal - totalMinutes);
                } else {
                    correctedStandby -= (correctedTotal - totalMinutes);
                }
            }

            // 确保没有负值
            correctedPowerOn = Math.max(0, correctedPowerOn);
            correctedStandby = Math.max(0, correctedStandby);

            log.info("设备状态时间修正完成 - 设备: {}, 修正后开机: {}分钟, 待机: {}分钟, 缩放比例: {:.3f}",
                    deviceCode, correctedPowerOn, correctedStandby, scaleFactor);

            return new long[]{correctedPowerOn, correctedStandby};
        }

        // 数据正常，无需修正
        return new long[]{powerOnMinutes, standbyMinutes};
    }

    @ApiOperation("故障列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "设备的id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "deviceTypeId", value = "设备类型id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "deptId", value = "事业部id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "workshopId", value = "车间id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "pageSize", value = "每页显示记录数", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageNum", value = "分页页数", required = false, paramType = "query", dataType = "int")
    })
    @GetMapping("/device/fault/list")
    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:list')")
    public TableDataInfo getRunnigetFaultListngState(ZhIotEquipment query) throws IoTDBConnectionException, StatementExecutionException {

        // 初始化返回对象
        TableDataInfo dataInfo = new TableDataInfo();
        // 初始化结果数组
        List<AlarmVo> ansArray = new ArrayList<>();

        // 设置默认时间范围
        String startTime = query.getStartTime();
        String endTime = query.getEndTime();
        if (startTime == null || endTime == null) {
            startTime = DateUtil.format(DateUtil.beginOfDay(new Date()), "yyyy-MM-dd HH:mm:ss");
            endTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        }
        List<ZhIotEquipment> equipmentList = zhIotEquipmentService.selectZhIotEquipmentList(query);

        // 设置总记录数
        dataInfo.setTotal(equipmentList.size());

        // 处理分页逻辑
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        try {
            equipmentList = equipmentList.subList((pageNum - 1) * pageSize, (Math.min(equipmentList.size(), pageNum * pageSize)));
        } catch (Exception e) {
        }
        for (ZhIotEquipment equipment : equipmentList) {
            AlarmVo alarmVo = new AlarmVo();

            final JSONArray faultData = getFaultData(equipment, startTime, endTime);

            alarmVo.setId(equipment.getId());
            alarmVo.setEquipmentName(equipment.getEquipmentName());
            alarmVo.setEquipmentCode(equipment.getEquipmentCode());
            alarmVo.setDeptName(equipment.getDeptName() == null ? "" : equipment.getDeptName());
            alarmVo.setWorkshopName(equipment.getWorkshopName() == null ? "" : equipment.getWorkshopName());
            alarmVo.setDeviceType(equipment.getDeviceType() == null ? "" : equipment.getDeviceType());
            alarmVo.setStartTime(startTime);
            alarmVo.setEndTime(endTime);
            if (faultData == null || faultData.size() == 0) {
                alarmVo.setAlarmCount(0);
                alarmVo.setAlarmTime(0L);
                alarmVo.setErrorRate("0.00");

            } else {
                alarmVo.setAlarmCount(faultData.size());
                long alarmTime = 0L;
                final long between = DateUtil.between(DateUtil.parse(startTime), DateUtil.parse(endTime), DateUnit.MINUTE);
                int[] timeBucket = new int[(int) between];
                for (int i = 0; i < faultData.size(); i++) {
                    JSONObject jsonObject = faultData.getJSONObject(i);
//                    alarmTime += jsonObject.getLong("time");
                    final String begin = jsonObject.getStr("begin");
                    final String end = jsonObject.getStr("end");
                    final String time = jsonObject.getStr("stopTime");
                    final long beginIndex = DateUtil.between(DateUtil.parse(startTime), DateUtil.parse(begin), DateUnit.MINUTE);
                    for (int j = 0; j < Integer.parseInt(time); j++) {
                        timeBucket[(int) beginIndex + j]++;
                    }
                }
                for (int i = 0; i < timeBucket.length; i++) {
                    if (timeBucket[i] > 0) {
                        alarmTime++;
                    }
                }
                alarmVo.setAlarmTime(alarmTime);
                double errorRate = 100.0 * alarmTime / (DateUtil.between(DateUtil.parse(startTime), DateUtil.parse(endTime), DateUnit.MINUTE));
                alarmVo.setErrorRate(String.format("%.3f", errorRate % 100));
            }
            ansArray.add(alarmVo);
        }
        dataInfo.setCode(200);
        dataInfo.setMsg("");
        dataInfo.setRows(ansArray);
        return dataInfo;

    }


    //图形报表
    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:list')")
    @GetMapping("/device/fault/graphic")
    public AjaxResult getFaultGraphic(
            ZhIotEquipment query) throws IoTDBConnectionException, StatementExecutionException {

        JSONObject ans = new JSONObject();

        List<String> equipmentNameList = new ArrayList<>();
        List<String> timeFaultList = new ArrayList<>();
        List<String> countFaultList = new ArrayList<>();

        // 设置默认时间范围
        String startTime = query.getStartTime();
        String endTime = query.getEndTime();
        if (startTime == null || endTime == null) {
            startTime = DateUtil.format(DateUtil.beginOfDay(new Date()), "yyyy-MM-dd HH:mm:ss");
            endTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        }
        List<ZhIotEquipment> equipmentList = zhIotEquipmentService.selectZhIotEquipmentList(query);


        for (ZhIotEquipment equipment : equipmentList) {
            // 查询设备类型属性
            ZhDeviceTypeAttr queryTemp = new ZhDeviceTypeAttr();
            queryTemp.setTslId(equipment.getDeviceTypeId());
            final List<ZhDeviceTypeAttr> attrs = zhDeviceTypeAttrService.selectZhDeviceTypeAttrList(queryTemp);
            Long alarmTime = 0L;
            List<HistoryData> alarm = new ArrayList<>();
            for (ZhDeviceTypeAttr attr : attrs) {
                if (attr.getAttrCode() != null && attr.getAttrCode().startsWith("Alarm")) {
                    alarmTime += iotDBUtil.getCountTag(Constants.TENANT, equipment.getEquipmentCode(), attr.getAttrCode(), "1.0", startTime, endTime);
                    List<HistoryData> temp = getStatusTime(Constants.TENANT, equipment.getEquipmentCode(), attr.getAttrCode(), startTime, endTime);
                    alarm.addAll(temp);
                }
            }

            if (alarmTime <= 0) {
                continue;
            }
            equipmentNameList.add(equipment.getEquipmentName() + " " + equipment.getEquipmentCode());
            timeFaultList.add(alarmTime + "");
            countFaultList.add(alarm.size() / 2 + "");

        }

        ans.put("equipmentNameList", equipmentNameList);
        ans.put("timeFaultList", timeFaultList);
        ans.put("countFaultList", countFaultList);
        return AjaxResult.success(ans);

    }

    //车间分析
    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:list')")
    @GetMapping("/device/fault/workshop")
    public TableDataInfo getFaultWorkshop(
            ZhWorkshop query) throws IoTDBConnectionException, StatementExecutionException {

        JSONArray ansArray = new JSONArray();

        // 设置默认时间范围
        String startTime = query.getStartTime();
        String endTime = query.getEndTime();
        if (startTime == null || endTime == null) {
            startTime = DateUtil.format(DateUtil.beginOfDay(new Date()), "yyyy-MM-dd HH:mm:ss");
            endTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        }
        startPage();
        final List<ZhWorkshop> zhWorkshops = zhWorkshopService.selectZhWorkshopList(query);

        if (zhWorkshops == null) {
            return getDataTable(ansArray);
        }
        for (ZhWorkshop zhWorkshop : zhWorkshops) {

            ZhIotEquipment queryTemp = new ZhIotEquipment();
            queryTemp.setWorkshopId(zhWorkshop.getId());
            List<ZhIotEquipment> equipmentList = (zhIotEquipmentService.selectZhIotEquipmentList(queryTemp));
            if (equipmentList == null || equipmentList.size() == 0) {
                continue;
            }
            JSONObject ans = new JSONObject();
            ans.put("workshopName", zhWorkshop.getWorkshopName());

            Long alarmTime = 0L;
            int count = 0;
            for (ZhIotEquipment equipment : equipmentList) {

                if (equipment.getDeptName() == null) {
                    continue;
                }
                ans.put("deptName", equipment.getDeptName());

                // 查询设备类型属性
                ZhDeviceTypeAttr queryAttr = new ZhDeviceTypeAttr();
                queryAttr.setTslId(equipment.getDeviceTypeId());
                final List<ZhDeviceTypeAttr> attrs = zhDeviceTypeAttrService.selectZhDeviceTypeAttrList(queryAttr);


                for (ZhDeviceTypeAttr attr : attrs) {
                    if (attr.getAttrCode() != null && attr.getAttrCode().startsWith("Alarm")) {
                        count++;
                        alarmTime += iotDBUtil.getCountTag(Constants.TENANT, equipment.getEquipmentCode(), "Alarm", "1.0", startTime, endTime);
                    }
                }
            }
            //TODO: 2023/3/29 错误率计算错误
            double errorRate;
            long minutes = DateUtil.between(DateUtil.parse(startTime), DateUtil.parse(endTime), DateUnit.MINUTE);
            if (count == 0 || minutes == 0 || equipmentList.size() == 0) {
                errorRate = 0.0;
            } else {
                errorRate = 100.0 * alarmTime / count * equipmentList.size() * minutes;
            }
            ans.put("faultRate", String.format("%.3f", errorRate % 100));
            ansArray.add(ans);

        }

        return getDataTable(ansArray);
    }


    //工序分析
    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:list')")
    @GetMapping("/device/fault/process")
    public TableDataInfo getFaultProcess(
            ZhBaseProcess query) throws IoTDBConnectionException, StatementExecutionException {

        JSONArray ansArray = new JSONArray();

        // 设置默认时间范围
        String startTime = query.getStartTime();
        String endTime = query.getEndTime();
        if (startTime == null || endTime == null) {
            startTime = DateUtil.format(DateUtil.beginOfDay(new Date()), "yyyy-MM-dd HH:mm:ss");
            endTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        }
        startPage();
        final List<ZhBaseProcess> zhBaseProcesses = zhBaseProcessService.selectZhBaseProcessList(query);

        if (zhBaseProcesses == null) {
            return getDataTable(ansArray);
        }
        for (ZhBaseProcess process : zhBaseProcesses) {

            ZhIotEquipment queryTemp = new ZhIotEquipment();
            queryTemp.setProcessId(process.getId());
            List<ZhIotEquipment> equipmentList = (zhIotEquipmentService.selectZhIotEquipmentList(queryTemp));
            if (equipmentList == null || equipmentList.size() == 0) {
                continue;
            }
            JSONObject ans = new JSONObject();
            ans.put("processName", process.getProcessName());

            Long alarmTime = 0L;
            int count = 0;
            for (ZhIotEquipment equipment : equipmentList) {

                if (equipment.getDeptName() == null) {
                    continue;
                }
                ans.put("deptName", equipment.getDeptName());

                // 查询设备类型属性
                ZhDeviceTypeAttr queryAttr = new ZhDeviceTypeAttr();
                queryAttr.setTslId(equipment.getDeviceTypeId());
                final List<ZhDeviceTypeAttr> attrs = zhDeviceTypeAttrService.selectZhDeviceTypeAttrList(queryAttr);


                for (ZhDeviceTypeAttr attr : attrs) {
                    if (attr.getAttrCode() != null && attr.getAttrCode().startsWith("Alarm")) {
                        count++;
                        alarmTime += iotDBUtil.getCountTag(Constants.TENANT, equipment.getEquipmentCode(), "Alarm", "1.0", startTime, endTime);
                    }
                }
            }
            //TODO: 2023/3/29 错误率计算错误
            double errorRate;
            long minutes = DateUtil.between(DateUtil.parse(startTime), DateUtil.parse(endTime), DateUnit.MINUTE);
            if (count == 0 || minutes == 0 || equipmentList.size() == 0) {
                errorRate = 0.0;
            } else {
                errorRate = 100.0 * alarmTime / count * equipmentList.size() * minutes;
            }

            ans.put("faultRate", String.format("%.3f", errorRate % 100));
            ansArray.add(ans);

        }

        return getDataTable(ansArray);
    }

    @ApiOperation("故障详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "设备的id", required = true, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "pageSize", value = "每页显示记录数", required = false, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "pageNum", value = "分页页数", required = false, paramType = "query", dataType = "int")
    })
    @GetMapping("/device/fault/detail")
    public TableDataInfo getRunnigetFaultListngState(@RequestParam(required = true) Long id,
                                                     @RequestParam(required = false) String startTime,
                                                     @RequestParam(required = false) String endTime) {
        // 初始化返回对象
        TableDataInfo dataInfo = new TableDataInfo();
        // 初始化结果数组
        JSONArray ansArray = new JSONArray();

        // 设置默认时间范围
        if (startTime == null || endTime == null) {
            startTime = DateUtil.format(DateUtil.beginOfDay(new Date()), "yyyy-MM-dd HH:mm:ss");
            endTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        }

        String cacheKey = "alarm:" + id + ":" + startTime + ":" + endTime;

//        if(redisCache.hasKey(cacheKey)){
//            ansArray = redisCache.getCacheObject(cacheKey);
//        }else {

        // 获取设备清单
        ZhIotEquipment equipment = zhIotEquipmentService.selectZhIotEquipmentById(id);
        if (equipment == null || equipment.getDeviceTypeId() == null) {
            return dataInfo;
        }

        ansArray = getFaultData(equipment, startTime, endTime);

//        redisCache.setCacheObject(cacheKey, ansArray, ConstantZH.CASH_DEVICE_FOR_HISTORY, TimeUnit.MINUTES);
        // 设置总记录数
        dataInfo.setTotal(ansArray.size());

        // 处理分页逻辑
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        List<Object> objects = Collections.emptyList();
        try {
            objects = ansArray.subList((pageNum - 1) * pageSize, (Math.min(ansArray.size(), pageNum * pageSize)));
        } catch (Exception e) {
            log.error("分页失败", e);
        }
        dataInfo.setRows(objects);
        dataInfo.setCode(200);
        return dataInfo;
    }

    private List<HistoryData> getStatusTime(String tenant, String deviceStr, String tag, String start, String end) {
        try {
            JSONObject object = new JSONObject();
            object.put("times", "0");
            List<HistoryData> onlyList = new ArrayList<>();
            List<HistoryData> list = iotDBUtil.getDataListFillWithZero(tenant, deviceStr, tag, start, end);
            if (list != null && list.size() > 0) {
                HistoryData temp = list.get(0);
                onlyList.add(temp);
                for (HistoryData obj : list) {
                    if (Convert.toStr(obj.getVal()).equals(temp.getVal())) {
                    } else {
                        try {
                            if (Convert.toStr(obj.getVal()).equalsIgnoreCase((String) temp.getVal())) {
                                object.put("times", object.getInt("times") + 1);
                            }
                        } catch (Exception e) {
                            log.error("转换异常", e);
//                            throw new RuntimeException(e);
                        }
                        temp = obj;
                        onlyList.add(temp);
                    }
                }
                onlyList.add(list.get(list.size() - 1));
            }
            return onlyList;
        } catch (IoTDBConnectionException ex) {
            ex.printStackTrace();
        } catch (StatementExecutionException ex) {
            ex.printStackTrace();
        }
        return null;
    }

    @PreAuthorize("@ss.hasPermi('zhenghe:equipment:list')")
    @GetMapping("/device/run/diagram")
    public AjaxResult runDiagram(ZhIotEquipment query) {
        JSONObject result = new JSONObject();

        List<ZhIotEquipment> list = zhIotEquipmentService.selectZhIotEquipmentList(query);
        if (list != null && list.size() > 0) {
        } else {
            throw new RuntimeException("未查询到设备");
        }
        // 设置默认时间范围
        String startTime = query.getStartTime();
        String endTime = query.getEndTime();
        if (startTime == null || endTime == null) {
            startTime = DateUtil.format(DateUtil.beginOfDay(new Date()), "yyyy-MM-dd HH:mm:ss");
            endTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        }

        String tag = "Power_ON";
        for (ZhIotEquipment temp : list) {
            JSONObject object = new JSONObject();
            final List<HistoryData> statusTime = getStatusTime(Constants.TENANT, temp.getEquipmentCode(), tag, startTime, endTime);
            object.put("data", statusTime);
            object.put("sort", temp.getEquipmentSort());
            result.put(temp.getEquipmentName() + " - " + temp.getEquipmentCode(), object);
        }
        return AjaxResult.success(result);
    }


    /*
     * @param equipment 设备信息
     * @return JSONArray
     * @date 2021/3/26 14:08
     * @description 获取故障数据
     */
    private JSONArray getFaultData(ZhIotEquipment equipment, String startTime, String endTime) {

        // 初始化结果数组
        JSONArray ansArray = new JSONArray();
        // 查询设备类型属性
        ZhDeviceTypeAttr query = new ZhDeviceTypeAttr();
        query.setTslId(equipment.getDeviceTypeId());
        final List<ZhDeviceTypeAttr> attrs = zhDeviceTypeAttrService.selectZhDeviceTypeAttrList(query);

        for (ZhDeviceTypeAttr attr : attrs) {
            // 处理报警属性
            if (attr.getAttrCode() != null && attr.getAttrCode().startsWith("Alarm")) {
                ZhEquipmentProp equipmentProp = new ZhEquipmentProp();
                equipmentProp.setAttrId(attr.getId());
                equipmentProp.setEquipmentId(equipment.getId());
                List<ZhEquipmentProp> zhEquipmentProps = zhEquipmentPropService.selectZhEquipmentPropList(equipmentProp);
                String alarmFlag = "1";
                String nomalFlag = "0";
                if (zhEquipmentProps != null && zhEquipmentProps.size() > 0) {
                    ZhEquipmentProp zhEquipmentProp = zhEquipmentProps.get(0);
                    if (zhEquipmentProp != null && zhEquipmentProp.getFaultVal() != null) {
                        alarmFlag = zhEquipmentProp.getFaultVal();
                    }
                }
                if ("1".equals(alarmFlag)) {
                    alarmFlag = "1.0";
                    nomalFlag = "0.0";
                } else {
                    alarmFlag = "0.0";
                    nomalFlag = "1.0";
                }

                // 获取状态时间
                List<HistoryData> alarm = getStatusTime(Constants.TENANT, equipment.getEquipmentCode(), attr.getAttrCode(), startTime, endTime);
                boolean flag = false;
                JSONObject object = new JSONObject();
                for (HistoryData historyData : alarm) {
                    // 处理报警和正常状态转换
                    if (alarmFlag.equals(historyData.getVal())) {
                        object.put("begin", historyData.getTime());
                        flag = true;
                    }
                    if (nomalFlag.equals(historyData.getVal()) && flag) {
                        object.put("end", historyData.getTime());
                        flag = false;
                        final String begin = String.valueOf(object.get("begin"));
                        final String end = String.valueOf(object.get("end"));

                        object.put("stopTime", DateUtil.between(DateUtil.parse(begin), DateUtil.parse(end), DateUnit.MINUTE));
                        object.put("reason", attr.getAttrName());
                        ansArray.add(object);
                        object = new JSONObject();
                    }
                }
            }
        }
        return ansArray;
    }
}
