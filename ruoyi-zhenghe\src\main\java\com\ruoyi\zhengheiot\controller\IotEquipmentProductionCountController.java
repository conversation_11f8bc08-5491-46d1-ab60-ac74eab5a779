package com.ruoyi.zhengheiot.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.util.DataTimeUtil;
import com.ruoyi.util.iotdb.IoTDBUtil;
import com.ruoyi.zhenghe.domain.ZhBaseProcess;
import com.ruoyi.zhenghe.domain.ZhDeviceTypeAttr;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.domain.ZhWorkshop;
import com.ruoyi.zhenghe.mapper.ZhDeviceTypeAttrMapper;
import com.ruoyi.zhenghe.mapper.ZhIotEquipmentMapper;
import com.ruoyi.zhenghe.service.IZhBaseProcessService;
import com.ruoyi.zhenghe.service.IZhIotEquipmentService;
import com.ruoyi.zhenghe.service.IZhWorkshopService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;


/**
 * 数据分析计数Controller
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Api(tags = "数据分析计数Controller")
@RestController
@RequestMapping("/zhenghe/productionCount")
public class IotEquipmentProductionCountController extends BaseController {

    @Autowired
    private ZhIotEquipmentMapper zhIotEquipmentMapper;
    private static final String TENANT = Constants.TENANT;
    @Autowired
    private IoTDBUtil ioTDBUtil;
    @Autowired
    private IZhWorkshopService zhWorkshopService;
    @Autowired
    private IZhBaseProcessService zhBaseProcessService;
    @Autowired
    private ZhDeviceTypeAttrMapper zhDeviceTypeAttrMapper;
    @Autowired
    private IZhIotEquipmentService zhIotEquipmentService;


    /**
     * 查询数据分析计数列表
     */
    @ApiOperation(value = "查询数据分析计数列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "事业部id", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "workshopId", value = "车间id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "deviceTypeId", value = "设备类型id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, paramType = "query", dataType = "integer"),
            @ApiImplicitParam(name = "pageSize", value = "条数", required = true, paramType = "query", dataType = "integer")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:productionCount:list')")
    @GetMapping("/list")
    public TableDataInfo list(ZhIotEquipment zhIotEquipment) throws StatementExecutionException, IoTDBConnectionException {
        // 如果未提供开始时间或结束时间，则默认查询过去24小时的数据
        if (ObjectUtil.isNull(zhIotEquipment.getStartTime()) || ObjectUtil.isNull(zhIotEquipment.getEndTime())) {
            zhIotEquipment.setStartTime(DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyy-MM-dd HH:mm:ss"));
            zhIotEquipment.setEndTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }
        // 校验时间格式
        if (!DataTimeUtil.isValidTimeFormat(zhIotEquipment.getStartTime()) || !DataTimeUtil.isValidTimeFormat(zhIotEquipment.getEndTime())) {
            throw new IllegalArgumentException("时间格式不正确，应为 'yyyy-MM-dd HH:mm:ss'");
        }

        if (zhIotEquipment.getStartTime().compareTo(zhIotEquipment.getEndTime()) > 0) {
            throw new IllegalArgumentException("开始时间不能大于结束时间");
        }
        startPage();
        List<ZhIotEquipment> zhIotEquipmentList = zhIotEquipmentService.selectZhIotEquipmentList(zhIotEquipment);

        if (ObjectUtil.isNotNull(zhIotEquipmentList) && zhIotEquipmentList.size() > 0) {
            String code = "Power_ON";
            ZhDeviceTypeAttr zhDeviceTypeAttr = new ZhDeviceTypeAttr();
            for (int i = 0; i < zhIotEquipmentList.size(); i++) {
                ZhIotEquipment iotEquipment = zhIotEquipmentList.get(i);
                zhDeviceTypeAttr.setTslId(iotEquipment.getDeviceTypeId());
                zhDeviceTypeAttr.setAttrCode("Status");
                List<ZhDeviceTypeAttr> zhDeviceTypeAttrList = zhDeviceTypeAttrMapper.selectZhDeviceTypeAttrList(zhDeviceTypeAttr);
                if (ObjectUtil.isNotNull(zhDeviceTypeAttrList) && zhDeviceTypeAttrList.size() > 0) {
                    code = zhDeviceTypeAttrList.get(0).getAttrCode();
                }
                //读取设备生产计数
                Integer count = ioTDBUtil.queryIotEquipmentProductionCount(TENANT, iotEquipment.getEquipmentCode(), null, zhIotEquipment.getStartTime(), zhIotEquipment.getEndTime());
                //读取设备生产计加时长
                Integer operTime = ioTDBUtil.queryIotEquipmentProductionDuration(TENANT, iotEquipment.getEquipmentCode(), code, zhIotEquipment.getStartTime(), zhIotEquipment.getEndTime());
                iotEquipment.setCount(count);
                if ((int) count == 0) {
                    iotEquipment.setCount("/");
                }
                iotEquipment.setOperTime(operTime);
                if (count == 0 || operTime == 0) {
                    iotEquipment.setProTime(0);
                } else {
                    BigDecimal countBig = new BigDecimal(count);
                    BigDecimal operTimeBig = new BigDecimal(operTime);
                    iotEquipment.setProTime(operTimeBig.divide(countBig, 3, RoundingMode.HALF_UP));
                }
                iotEquipment.setStartTime(zhIotEquipment.getStartTime());
                iotEquipment.setEndTime(zhIotEquipment.getEndTime());

//                if(zhIotEquipment.getCount()==null){
//                    zhIotEquipmentList.remove(iotEquipment);
//                }else {
//                    if("0".equals(zhIotEquipment.getCount().toString())){
//                        zhIotEquipmentList.remove(iotEquipment);
//                    }
//                }
            }
        }
        return getDataTable(zhIotEquipmentList);
    }


    /**
     * 查询车间总计数
     */
    @ApiOperation(value = "查询车间总计数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "事业部id", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "id", value = "车间id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "deviceTypeId", value = "设备类型id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, paramType = "query", dataType = "integer"),
            @ApiImplicitParam(name = "pageSize", value = "条数", required = true, paramType = "query", dataType = "integer")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:productionCount:list')")
    @GetMapping("/workshopCountlist")
    public TableDataInfo workshopCountlist(ZhWorkshop zhWorkshop) throws StatementExecutionException, IoTDBConnectionException {
        // 如果未提供开始时间或结束时间，则默认查询过去24小时的数据
        if (ObjectUtil.isNull(zhWorkshop.getStartTime()) || ObjectUtil.isNull(zhWorkshop.getEndTime())) {
            zhWorkshop.setStartTime(DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyy-MM-dd HH:mm:ss"));
            zhWorkshop.setEndTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }
        // 校验时间格式
        if (!DataTimeUtil.isValidTimeFormat(zhWorkshop.getStartTime()) || !DataTimeUtil.isValidTimeFormat(zhWorkshop.getEndTime())) {
            throw new IllegalArgumentException("时间格式不正确，应为 'yyyy-MM-dd HH:mm:ss'");
        }

        if (zhWorkshop.getStartTime().compareTo(zhWorkshop.getEndTime()) > 0) {
            throw new IllegalArgumentException("开始时间不能大于结束时间");
        }
        //查询车间列表
        startPage();
        List<ZhWorkshop> zhWorkshopList = zhWorkshopService.selectOnlyZhWorkshopList(zhWorkshop);
        if (ObjectUtil.isNull(zhWorkshopList)) {
            throw new ServiceException("车间为空");
        }
        for (ZhWorkshop workshop : zhWorkshopList) {
            Integer totalCount = 0;
            ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
            zhIotEquipment.setStartTime(zhWorkshop.getStartTime());
            zhIotEquipment.setEndTime(zhWorkshop.getEndTime());
            zhIotEquipment.setWorkshopId(workshop.getId());
            if (ObjectUtil.isNotNull(zhWorkshop.getDeviceTypeId())) {
                zhIotEquipment.setDeviceTypeId(zhWorkshop.getDeviceTypeId());
            }
            //查询该车间绑定的设备
            List<ZhIotEquipment> zhIotEquipmentList = zhIotEquipmentMapper.selectZhIotEquipmentList(zhIotEquipment);
            if (ObjectUtil.isNotNull(zhIotEquipmentList) && zhIotEquipmentList.size() > 0) {
                for (ZhIotEquipment iotEquipment : zhIotEquipmentList) {
                    //读取设备生产计数
                    Integer count = ioTDBUtil.queryIotEquipmentProductionCount(TENANT, iotEquipment.getEquipmentCode(), null, zhIotEquipment.getStartTime(), zhIotEquipment.getEndTime());
                    totalCount = totalCount + count;
                }
            }
            workshop.setTotalCount(totalCount);
        }
        return getDataTable(zhWorkshopList);
    }


    /**
     * 查询工序总计数
     */
    @ApiOperation(value = "查询工序总计数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "事业部id", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "workshopId", value = "车间id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "deviceTypeId", value = "设备类型id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pageNum", value = "页数", required = true, paramType = "query", dataType = "integer"),
            @ApiImplicitParam(name = "pageSize", value = "条数", required = true, paramType = "query", dataType = "integer")
    })
    @PreAuthorize("@ss.hasPermi('zhenghe:productionCount:list')")
    @GetMapping("/processCountlist")
    public TableDataInfo processCountlist(ZhBaseProcess zhBaseProcess) throws StatementExecutionException, IoTDBConnectionException {
        // 如果未提供开始时间或结束时间，则默认查询过去24小时的数据
        if (ObjectUtil.isNull(zhBaseProcess.getStartTime()) || ObjectUtil.isNull(zhBaseProcess.getEndTime())) {
            zhBaseProcess.setStartTime(DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyy-MM-dd HH:mm:ss"));
            zhBaseProcess.setEndTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }
        // 校验时间格式
        if (!DataTimeUtil.isValidTimeFormat(zhBaseProcess.getStartTime()) || !DataTimeUtil.isValidTimeFormat(zhBaseProcess.getEndTime())) {
            throw new IllegalArgumentException("时间格式不正确，应为 'yyyy-MM-dd HH:mm:ss'");
        }

        if (zhBaseProcess.getStartTime().compareTo(zhBaseProcess.getEndTime()) > 0) {
            throw new IllegalArgumentException("开始时间不能大于结束时间");
        }
        //查询工序列表
        startPage();
        List<ZhBaseProcess> zhBaseProcessList = zhBaseProcessService.selectZhBaseProcessList(zhBaseProcess);
        if (ObjectUtil.isNull(zhBaseProcessList)) {
            throw new ServiceException("工序为空");
        }
        for (ZhBaseProcess baseProcess : zhBaseProcessList) {
            Integer totalCount = 0;
            ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
            zhIotEquipment.setProcessId(baseProcess.getId());
            zhIotEquipment.setStartTime(zhBaseProcess.getStartTime());
            zhIotEquipment.setEndTime(zhBaseProcess.getEndTime());
            if (ObjectUtil.isNotNull(zhBaseProcess.getWorkshopId())) {
                zhIotEquipment.setWorkshopId(zhBaseProcess.getWorkshopId());
            }
            if (ObjectUtil.isNotNull(zhBaseProcess.getDeviceTypeId())) {
                zhIotEquipment.setDeviceTypeId(zhBaseProcess.getDeviceTypeId());
            }
            //查询该工序绑定的设备
            List<ZhIotEquipment> zhIotEquipmentList = zhIotEquipmentMapper.selectZhIotEquipmentList(zhIotEquipment);
            if (ObjectUtil.isNotNull(zhIotEquipmentList) && zhIotEquipmentList.size() > 0) {
                for (ZhIotEquipment iotEquipment : zhIotEquipmentList) {
                    //读取设备生产计数
                    Integer count = ioTDBUtil.queryIotEquipmentProductionCount(TENANT, iotEquipment.getEquipmentCode(), null, zhIotEquipment.getStartTime(), zhIotEquipment.getEndTime());
                    totalCount = totalCount + count;
                }
            }
            baseProcess.setTotalCount(totalCount);
        }
        return getDataTable(zhBaseProcessList);
    }


    /**
     * 导出数据分析计数列表
     */
    @ApiOperation(value = "导出数据分析计数列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "事业部id", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "workshopId", value = "车间id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "deviceTypeId", value = "设备类型id", required = false, paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = false, paramType = "query", dataType = "String")
    })
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhIotEquipment zhIotEquipment) throws StatementExecutionException, IoTDBConnectionException {
        // 如果未提供开始时间或结束时间，则默认查询过去24小时的数据
        if (ObjectUtil.isNull(zhIotEquipment.getStartTime()) || ObjectUtil.isNull(zhIotEquipment.getEndTime())) {
            zhIotEquipment.setStartTime(DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyy-MM-dd HH:mm:ss"));
            zhIotEquipment.setEndTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }
        // 校验时间格式
        if (!DataTimeUtil.isValidTimeFormat(zhIotEquipment.getStartTime()) || !DataTimeUtil.isValidTimeFormat(zhIotEquipment.getEndTime())) {
            throw new IllegalArgumentException("时间格式不正确，应为 'yyyy-MM-dd HH:mm:ss'");
        }

        if (zhIotEquipment.getStartTime().compareTo(zhIotEquipment.getEndTime()) > 0) {
            throw new IllegalArgumentException("开始时间不能大于结束时间");
        }
        List<ZhIotEquipment> zhIotEquipmentList = zhIotEquipmentMapper.selectZhIotEquipmentList(zhIotEquipment);
        if (ObjectUtil.isNotNull(zhIotEquipmentList) && zhIotEquipmentList.size() > 0) {
            String code = "Power_ON";
            ZhDeviceTypeAttr zhDeviceTypeAttr = new ZhDeviceTypeAttr();
            for (ZhIotEquipment iotEquipment : zhIotEquipmentList) {
                zhDeviceTypeAttr.setTslId(iotEquipment.getDeviceTypeId());
                zhDeviceTypeAttr.setAttrCode("Status");
                List<ZhDeviceTypeAttr> zhDeviceTypeAttrList = zhDeviceTypeAttrMapper.selectZhDeviceTypeAttrList(zhDeviceTypeAttr);
                if (ObjectUtil.isNotNull(zhDeviceTypeAttrList) && zhDeviceTypeAttrList.size() > 0) {
                    code = zhDeviceTypeAttrList.get(0).getAttrCode();
                }
                //读取设备生产计数
                Integer count = ioTDBUtil.queryIotEquipmentProductionCount(TENANT, iotEquipment.getEquipmentCode(), null, zhIotEquipment.getStartTime(), zhIotEquipment.getEndTime());
                //读取设备生产计加时长
                Integer operTime = ioTDBUtil.queryIotEquipmentProductionDuration(TENANT, iotEquipment.getEquipmentCode(), code, zhIotEquipment.getStartTime(), zhIotEquipment.getEndTime());
                iotEquipment.setCount(count);
                iotEquipment.setOperTime(operTime);
                if (count == 0 || operTime == 0) {
                    iotEquipment.setProTime(0);
                } else {
                    BigDecimal countBig = new BigDecimal(count);
                    BigDecimal operTimeBig = new BigDecimal(operTime);
                    iotEquipment.setProTime(operTimeBig.divide(countBig, 2, RoundingMode.HALF_UP));
                }
                iotEquipment.setStartTime(zhIotEquipment.getStartTime());
                iotEquipment.setEndTime(zhIotEquipment.getEndTime());
            }
        }
        ExcelUtil<ZhIotEquipment> util = new ExcelUtil<ZhIotEquipment>(ZhIotEquipment.class);
        util.exportExcel(response, zhIotEquipmentList, "数据分析计数数据");
    }


}
