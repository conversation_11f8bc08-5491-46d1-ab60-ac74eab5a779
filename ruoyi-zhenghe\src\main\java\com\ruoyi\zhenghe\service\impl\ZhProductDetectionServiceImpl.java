package com.ruoyi.zhenghe.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.zhenghe.domain.ZhProduct;
import com.ruoyi.zhenghe.domain.ZhProductDefect;
import com.ruoyi.zhenghe.domain.dto.DetectionAnalysisDto;
import com.ruoyi.zhenghe.domain.dto.DetectionSummaryDto;
import com.ruoyi.zhenghe.domain.dto.ProductDetectionChartDto;
import com.ruoyi.zhenghe.service.IZhProductDefectService;
import com.ruoyi.zhenghe.service.IZhProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.zhenghe.mapper.ZhProductDetectionMapper;
import com.ruoyi.zhenghe.domain.ZhProductDetection;
import com.ruoyi.zhenghe.service.IZhProductDetectionService;

/**
 * 产品检测记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
public class ZhProductDetectionServiceImpl implements IZhProductDetectionService 
{
    @Autowired
    private ZhProductDetectionMapper zhProductDetectionMapper;

    @Autowired
    private IZhProductService zhProductService;

    @Autowired
    private IZhProductDefectService zhProductDefectService;

    /**
     * 查询产品检测记录
     * 
     * @param id 产品检测记录主键
     * @return 产品检测记录
     */
    @Override
    public ZhProductDetection selectZhProductDetectionById(Long id)
    {
        return zhProductDetectionMapper.selectZhProductDetectionById(id);
    }

    /**
     * 查询产品检测记录列表
     * 
     * @param zhProductDetection 产品检测记录
     * @return 产品检测记录
     */
    @Override
    public List<ZhProductDetection> selectZhProductDetectionList(ZhProductDetection zhProductDetection)
    {
        return zhProductDetectionMapper.selectZhProductDetectionList(zhProductDetection);
    }

    /**
     * 查询产品检测记录分组列表（按日期分组）
     * 
     * @param zhProductDetection 产品检测记录
     * @return 产品检测记录集合
     */
    @Override
    public List<DetectionSummaryDto> selectZhProductDetectionGroupList(ZhProductDetection zhProductDetection)
    {
        return zhProductDetectionMapper.selectZhProductDetectionGroupList(zhProductDetection);
    }

    /**
     * 新增产品检测记录
     * 
     * @param zhProductDetection 产品检测记录
     * @return 结果
     */
    @Override
    public int insertZhProductDetection(ZhProductDetection zhProductDetection)
    {
        // 自动计算合格数量和瑕疵数量
        zhProductDetection.autoCalculateCounts();
        
        // 设置默认值
        if (zhProductDetection.getStatus() == null) {
            zhProductDetection.setStatus(1);
        }
        if (zhProductDetection.getDetectionTime() == null) {
            zhProductDetection.setDetectionTime(DateUtils.getNowDate());
        }
        
        zhProductDetection.setCreateTime(DateUtils.getNowDate());
        zhProductDetection.setCreateBy(SecurityUtils.getUsername());
        
        return zhProductDetectionMapper.insertZhProductDetection(zhProductDetection);
    }

    /**
     * 修改产品检测记录
     * 
     * @param zhProductDetection 产品检测记录
     * @return 结果
     */
    @Override
    public int updateZhProductDetection(ZhProductDetection zhProductDetection)
    {
        // 自动计算合格数量和瑕疵数量
        zhProductDetection.autoCalculateCounts();
        
        zhProductDetection.setUpdateTime(DateUtils.getNowDate());
        zhProductDetection.setUpdateBy(SecurityUtils.getUsername());
        
        return zhProductDetectionMapper.updateZhProductDetection(zhProductDetection);
    }

    /**
     * 批量删除产品检测记录
     * 
     * @param ids 需要删除的产品检测记录主键
     * @return 结果
     */
    @Override
    public int deleteZhProductDetectionByIds(Long[] ids)
    {
        return zhProductDetectionMapper.deleteZhProductDetectionByIds(ids);
    }

    /**
     * 删除产品检测记录信息
     * 
     * @param id 产品检测记录主键
     * @return 结果
     */
    @Override
    public int deleteZhProductDetectionById(Long id)
    {
        return zhProductDetectionMapper.deleteZhProductDetectionById(id);
    }

    /**
     * 保存检测结果（专用接口）
     * 
     * @param zhProductDetection 产品检测记录
     * @return 结果
     */
    @Override
    public int saveDetectionResult(ZhProductDetection zhProductDetection)
    {
        // 验证产品是否存在
        if (zhProductDetection.getProductId() == null) {
            throw new RuntimeException("产品ID不能为空");
        }
        
        ZhProduct product = zhProductService.selectZhProductById(zhProductDetection.getProductId());
        if (product == null) {
            throw new RuntimeException("产品不存在");
        }
        
        // 设置操作员为当前用户
        if (zhProductDetection.getOperator() == null || zhProductDetection.getOperator().isEmpty()) {
            zhProductDetection.setOperator(SecurityUtils.getUsername());
        }
        
        return insertZhProductDetection(zhProductDetection);
    }

    /**
     * 根据产品ID查询检测记录列表
     * 
     * @param productId 产品ID
     * @return 检测记录集合
     */
    @Override
    public List<ZhProductDetection> selectZhProductDetectionByProductId(Long productId)
    {
        return zhProductDetectionMapper.selectZhProductDetectionByProductId(productId);
    }

    /**
     * 根据产品ID和时间范围查询检测统计数据
     * 
     * @param productId 产品ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 检测统计数据
     */
    @Override
    public List<DetectionSummaryDto> selectDetectionStatisticsByProductId(Long productId, Date startTime, Date endTime)
    {
        return zhProductDetectionMapper.selectDetectionStatisticsByProductId(productId, startTime, endTime);
    }

    /**
     * 根据产品ID和检测日期查询瑕疵细分数据
     * 
     * @param productId 产品ID
     * @param detectionDate 检测日期
     * @return 瑕疵细分数据
     */
    @Override
    public List<ZhProductDetection> selectDefectDetailsByProductIdAndDate(Long productId, Date detectionDate)
    {
        return zhProductDetectionMapper.selectDefectDetailsByProductIdAndDate(productId, detectionDate);
    }

    /**
     * 获取产品检测分析数据
     *
     * @param productId 产品ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 检测分析数据
     */
    @Override
    public DetectionAnalysisDto getDetectionAnalysisData(Long productId, Date startTime, Date endTime)
    {
        // 查询统计数据
        List<DetectionSummaryDto> summaryList = zhProductDetectionMapper.selectDetectionAnalysisData(productId, startTime, endTime);

        if (summaryList.isEmpty()) {
            return null;
        }

        // 合并统计数据（如果有多条记录）
        DetectionSummaryDto summary = summaryList.get(0);
        if (summaryList.size() > 1) {
            summary = mergeSummaryData(summaryList);
        }

        // 构建分析数据
        DetectionAnalysisDto analysisDto = new DetectionAnalysisDto();
        analysisDto.setProductModel(summary.getProductModel());
        analysisDto.setPieceCount(summary.getPieceCount());
        analysisDto.setSectionCount(summary.getSectionCount());
        analysisDto.setTimingMark(summary.getTimingMark());
        analysisDto.setTotalCount(summary.getTotalCount());
        analysisDto.setQualifiedCount(summary.getQualifiedCount());
        analysisDto.setDefectCount(summary.getDefectCount());

        // 计算合格率和瑕疵率
        if (summary.getTotalCount() > 0) {
            BigDecimal qualifiedRate = BigDecimal.valueOf(summary.getQualifiedCount())
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(summary.getTotalCount()), 2, RoundingMode.HALF_UP);
            analysisDto.setQualifiedRate(qualifiedRate);

            BigDecimal defectRate = BigDecimal.valueOf(summary.getDefectCount())
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(summary.getTotalCount()), 2, RoundingMode.HALF_UP);
            analysisDto.setDefectRate(defectRate);
        }

        // 获取瑕疵详情统计
        List<DetectionAnalysisDto.DefectStatistics> defectStatisticsList = getDefectStatistics(productId, startTime, endTime);
        analysisDto.setDefectStatisticsList(defectStatisticsList);

        return analysisDto;
    }

    /**
     * 合并多条统计数据
     *
     * @param summaryList 统计数据列表
     * @return 合并后的统计数据
     */
    private DetectionSummaryDto mergeSummaryData(List<DetectionSummaryDto> summaryList) {
        DetectionSummaryDto merged = new DetectionSummaryDto();

        // 使用第一条记录的产品信息
        DetectionSummaryDto first = summaryList.get(0);
        merged.setProductModel(first.getProductModel());
        merged.setPieceCount(first.getPieceCount());
        merged.setSectionCount(first.getSectionCount());
        merged.setTimingMark(first.getTimingMark());

        // 累加统计数据
        int totalCount = summaryList.stream().mapToInt(DetectionSummaryDto::getTotalCount).sum();
        int qualifiedCount = summaryList.stream().mapToInt(DetectionSummaryDto::getQualifiedCount).sum();
        int defectCount = summaryList.stream().mapToInt(DetectionSummaryDto::getDefectCount).sum();

        merged.setTotalCount(totalCount);
        merged.setQualifiedCount(qualifiedCount);
        merged.setDefectCount(defectCount);

        return merged;
    }

    /**
     * 获取瑕疵详情统计
     *
     * @param productId 产品ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 瑕疵详情统计列表
     */
    private List<DetectionAnalysisDto.DefectStatistics> getDefectStatistics(Long productId, Date startTime, Date endTime) {
        // 查询检测记录
        ZhProductDetection queryParam = new ZhProductDetection();
        queryParam.setProductId(productId);

        // 设置时间范围参数
        Map<String, Object> params = new HashMap<>();
        if (startTime != null) {
            params.put("beginTime", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, startTime));
        }
        if (endTime != null) {
            params.put("endTime", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, endTime));
        }
        queryParam.setParams(params);

        List<ZhProductDetection> detectionList = zhProductDetectionMapper.selectZhProductDetectionList(queryParam);

        // 合并所有瑕疵数据
        Map<String, Integer> mergedDefectData = new HashMap<>();
        for (ZhProductDetection detection : detectionList) {
            Map<String, Integer> defectMap = detection.getDefectDataMap();
            if (defectMap != null) {
                for (Map.Entry<String, Integer> entry : defectMap.entrySet()) {
                    mergedDefectData.merge(entry.getKey(), entry.getValue(), Integer::sum);
                }
            }
        }

        // 计算总瑕疵数
        int totalDefects = mergedDefectData.values().stream().mapToInt(Integer::intValue).sum();

        // 获取产品的瑕疵定义（用于获取瑕疵图片）
        Map<String, String> defectImageMap = new HashMap<>();
        if (productId != null) {
            List<ZhProductDefect> defectList = zhProductDefectService.selectZhProductDefectListByProductId(productId);
            defectImageMap = defectList.stream()
                    .collect(Collectors.toMap(ZhProductDefect::getDefectName,
                            defect -> defect.getDefectImage() != null ? defect.getDefectImage() : "",
                            (existing, replacement) -> existing));
        }

        // 构建瑕疵统计列表
        List<DetectionAnalysisDto.DefectStatistics> statisticsList = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : mergedDefectData.entrySet()) {
            DetectionAnalysisDto.DefectStatistics statistics = new DetectionAnalysisDto.DefectStatistics();
            statistics.setDefectName(entry.getKey());
            statistics.setDefectCount(entry.getValue());
            statistics.setDefectImage(defectImageMap.get(entry.getKey()));

            // 计算瑕疵占比
            if (totalDefects > 0) {
                BigDecimal ratio = BigDecimal.valueOf(entry.getValue())
                        .multiply(BigDecimal.valueOf(100))
                        .divide(BigDecimal.valueOf(totalDefects), 2, RoundingMode.HALF_UP);
                statistics.setDefectRatio(ratio);
            } else {
                statistics.setDefectRatio(BigDecimal.ZERO);
            }

            statisticsList.add(statistics);
        }

        // 按瑕疵数量降序排序
        statisticsList.sort((a, b) -> b.getDefectCount().compareTo(a.getDefectCount()));

        return statisticsList;
    }

    /**
     * 根据时间范围查询所有产品的检测汇总数据（按日期和产品分组）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 检测汇总数据
     */
    @Override
    public List<DetectionSummaryDto> selectDetectionSummaryByDateRange(Date startTime, Date endTime)
    {
        return zhProductDetectionMapper.selectDetectionSummaryByDateRange(startTime, endTime);
    }

    /**
     * 获取柱状图数据（按产品组合分组统计）
     *
     * @param productModel 产品型号（可选）
     * @param pieceCount 片数（可选）
     * @param sectionCount 节数（可选）
     * @param timingMark 正时标记（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 柱状图数据
     */
    @Override
    public List<ProductDetectionChartDto> getChartData(String customerName,String productModel, String machineType,
                                                       String printMark, String timingMark,
                                                       Date startTime, Date endTime)
    {
        List<ProductDetectionChartDto> chartData = zhProductDetectionMapper.selectChartData(customerName,
                productModel, machineType, printMark, timingMark, startTime, endTime);

        // 为每个数据项构建产品组合标识
        for (ProductDetectionChartDto item : chartData) {
            item.buildProductCombination();
            // 如果数据库查询没有计算比率，这里重新计算
            if (item.getQualifiedRate() == null || item.getDefectRate() == null) {
                item.calculateRates();
            }
        }

        return chartData;
    }
}
