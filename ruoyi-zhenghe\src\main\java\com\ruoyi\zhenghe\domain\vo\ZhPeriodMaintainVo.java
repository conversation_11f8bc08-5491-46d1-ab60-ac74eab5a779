package com.ruoyi.zhenghe.domain.vo;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 尖峰平谷时段维护对象 zh_period_maintain
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Data
public class ZhPeriodMaintainVo {

    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 年 */
    @ApiModelProperty(value = "年")
    @Excel(name = "年")
    private String yearTime;

    /** 时段 从0开始 0为00:00 - 01:00 */
    @ApiModelProperty(value = "时段")
    @Excel(name = "时段 从1开始 1为00:00 - 01:00")
    private Long period;

    /** 一月时段名称 */
    @ApiModelProperty(value = "一月时段名称")
    @Excel(name = "一月时段名称")
    private String onePeriodName;

    /** 一月时段颜色 */
    @ApiModelProperty(value = "一月时段颜色")
    //@Excel(name = "一月时段颜色")
    private String onePeriodColor;

    /** 二月时段名称 */
    @ApiModelProperty(value = "二月时段名称")
    @Excel(name = "二月时段名称")
    private String twoPeriodName;

    /** 二月时段颜色 */
    @ApiModelProperty(value = "二月时段颜色")
    //@Excel(name = "二月时段颜色")
    private String twoPeriodColor;

    /** 三月时段名称 */
    @ApiModelProperty(value = "三月时段名称")
    @Excel(name = "三月时段名称")
    private String threePeriodName;

    /** 三月时段颜色 */
    @ApiModelProperty(value = "三月时段颜色")
    //@Excel(name = "三月时段颜色")
    private String threePeriodColor;

    /** 四月时段名称 */
    @ApiModelProperty(value = "四月时段名称")
    @Excel(name = "四月时段名称")
    private String fourPeriodName;

    /** 四月时段颜色 */
    @ApiModelProperty(value = "四月时段颜色")
    //@Excel(name = "四月时段颜色")
    private String fourPeriodColor;

    /** 五月时段名称 */
    @ApiModelProperty(value = "五月时段名称")
    @Excel(name = "五月时段名称")
    private String fivePeriodName;

    /** 五月时段颜色 */
    @ApiModelProperty(value = "五月时段颜色")
    //@Excel(name = "五月时段颜色")
    private String fivePeriodColor;

    /** 六月时段名称 */
    @ApiModelProperty(value = "六月时段名称")
    @Excel(name = "六月时段名称")
    private String sixPeriodName;

    /** 六月时段颜色 */
    @ApiModelProperty(value = "六月时段颜色")
    //@Excel(name = "六月时段颜色")
    private String sixPeriodColor;

    /** 七月时段名称 */
    @ApiModelProperty(value = "七月时段名称")
    @Excel(name = "七月时段名称")
    private String sevenPeriodName;

    /** 七月时段颜色 */
    @ApiModelProperty(value = "七月时段颜色")
    //@Excel(name = "七月时段颜色")
    private String sevenPeriodColor;

    /** 八月时段名称 */
    @ApiModelProperty(value = "八月时段名称")
    @Excel(name = "八月时段名称")
    private String eightPeriodName;

    /** 八月时段颜色 */
    @ApiModelProperty(value = "八月时段颜色")
    //@Excel(name = "八月时段颜色")
    private String eightPeriodColor;

    /** 九月时段名称 */
    @ApiModelProperty(value = "九月时段名称")
    @Excel(name = "九月时段名称")
    private String ninePeriodName;

    /** 九月时段颜色 */
    @ApiModelProperty(value = "九月时段颜色")
    //@Excel(name = "九月时段颜色")
    private String ninePeriodColor;

    /** 十月时段名称 */
    @ApiModelProperty(value = "十月时段名称")
    @Excel(name = "十月时段名称")
    private String tenPeriodName;

    /** 十月时段颜色 */
    @ApiModelProperty(value = "十月时段颜色")
    //@Excel(name = "十月时段颜色")
    private String tenPeriodColor;

    /** 十一月时段名称 */
    @ApiModelProperty(value = "十一月时段名称")
    @Excel(name = "十一月时段名称")
    private String elevenPeriodName;

    /** 十一月时段颜色 */
    @ApiModelProperty(value = "十一月时段颜色")
    //@Excel(name = "十一月时段颜色")
    private String elevenPeriodColor;

    /** 十二月时段名称 */
    @ApiModelProperty(value = "十二月时段名称")
    @Excel(name = "十二月时段名称")
    private String twelvePeriodName;

    /** 十二月时段颜色 */
    @ApiModelProperty(value = "十二月时段颜色")
    //@Excel(name = "十二月时段颜色")
    private String twelvePeriodColor;

    /** 部门id */
    //@Excel(name = "部门id")
    private Long deptId;
}
