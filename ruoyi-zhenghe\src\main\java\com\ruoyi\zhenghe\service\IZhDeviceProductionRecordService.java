package com.ruoyi.zhenghe.service;

import com.ruoyi.zhenghe.domain.ZhDeviceProductionRecord;
import com.ruoyi.zhenghe.domain.dto.ProductQuantityDto;
import java.util.List;

/**
 * 设备产品生产记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IZhDeviceProductionRecordService 
{
    /**
     * 查询设备产品生产记录
     * 
     * @param id 设备产品生产记录主键
     * @return 设备产品生产记录
     */
    public ZhDeviceProductionRecord selectZhDeviceProductionRecordById(Long id);

    /**
     * 查询设备产品生产记录列表
     * 
     * @param zhDeviceProductionRecord 设备产品生产记录
     * @return 设备产品生产记录集合
     */
    public List<ZhDeviceProductionRecord> selectZhDeviceProductionRecordList(ZhDeviceProductionRecord zhDeviceProductionRecord);

    /**
     * 新增设备产品生产记录
     * 
     * @param zhDeviceProductionRecord 设备产品生产记录
     * @return 结果
     */
    public int insertZhDeviceProductionRecord(ZhDeviceProductionRecord zhDeviceProductionRecord);

    /**
     * 修改设备产品生产记录
     * 
     * @param zhDeviceProductionRecord 设备产品生产记录
     * @return 结果
     */
    public int updateZhDeviceProductionRecord(ZhDeviceProductionRecord zhDeviceProductionRecord);

    /**
     * 批量删除设备产品生产记录
     * 
     * @param ids 需要删除的设备产品生产记录主键集合
     * @return 结果
     */
    public int deleteZhDeviceProductionRecordByIds(Long[] ids);

    /**
     * 删除设备产品生产记录信息
     * 
     * @param id 设备产品生产记录主键
     * @return 结果
     */
    public int deleteZhDeviceProductionRecordById(Long id);

    /**
     * 处理生产记录数据
     * 
     * @param deviceCode 设备编码
     * @param productName 产品名称
     * @param currentCount 当前累计产量
     * @param timestamp 时间戳
     */
    public void processProductionRecord(String deviceCode, String productName, int currentCount, Long timestamp);

    /**
     * 查询设备生产汇总数据
     *
     * @param deviceCode 设备编码
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 生产汇总列表
     */
    public List<ZhDeviceProductionRecord> selectProductionSummary(String deviceCode, String beginTime, String endTime);

    /**
     * 查询产品数量展示界面数据
     *
     * @param productName 产品型号（可选）
     * @param beginTime 开始时间（可选，默认当天0点）
     * @param endTime 结束时间（可选，默认当前时间）
     * @return 产品数量列表
     */
    public List<ZhDeviceProductionRecord> selectProductQuantityList(String productName, String beginTime, String endTime);

    /**
     * 查询产品数量展示界面数据（用于导出）
     *
     * @param productName 产品型号（可选）
     * @param beginTime 开始时间（可选，默认当天0点）
     * @param endTime 结束时间（可选，默认当前时间）
     * @return 产品数量DTO列表
     */
    public List<ProductQuantityDto> selectProductQuantityForExport(String productName, String beginTime, String endTime);

    /**
     * 查询产品生产明细
     *
     * @param productName 产品名称
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 产品生产明细列表
     */
    public List<ZhDeviceProductionRecord> selectProductDetail(String productName, String beginTime, String endTime);
}
