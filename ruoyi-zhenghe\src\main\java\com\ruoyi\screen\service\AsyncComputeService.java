package com.ruoyi.screen.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.screen.service.MultiLevelCacheService;
import com.ruoyi.screen.service.OptimizedIoTDBService;
import com.ruoyi.zhenghe.domain.ZhIotEquipment;
import com.ruoyi.zhenghe.service.IZhIotEquipmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 异步计算服务
 * 将耗时计算改为异步后台处理，接口返回缓存结果
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
@Slf4j
@Service
public class AsyncComputeService {

    @Autowired
    private MultiLevelCacheService cacheService;

    @Autowired
    private OptimizedIoTDBService iotdbService;

    @Autowired
    private IZhIotEquipmentService zhIotEquipmentService;

    // 异步计算专用线程池
    private final ExecutorService asyncExecutor = Executors.newFixedThreadPool(6);

    // 正在计算的任务记录，避免重复计算
    private final Set<String> computingTasks = ConcurrentHashMap.newKeySet();

    /**
     * 异步计算产量数据
     */
    @Async
    public CompletableFuture<JSONObject> asyncComputeYieldData(String deptId, String workshopId) {
        String taskKey = "yield:" + deptId + ":" + workshopId;
        
        if (computingTasks.contains(taskKey)) {
            log.info("产量计算任务已在进行中: {}", taskKey);
            return CompletableFuture.completedFuture(null);
        }

        computingTasks.add(taskKey);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return computeYieldData(deptId, workshopId);
            } finally {
                computingTasks.remove(taskKey);
            }
        }, asyncExecutor);
    }

    /**
     * 异步计算小时产量数据
     */
    @Async
    public CompletableFuture<JSONObject> asyncComputeHourlyYieldData(String deptId, String workshopId) {
        String taskKey = "yieldHour:" + deptId + ":" + workshopId;
        
        if (computingTasks.contains(taskKey)) {
            log.info("小时产量计算任务已在进行中: {}", taskKey);
            return CompletableFuture.completedFuture(null);
        }

        computingTasks.add(taskKey);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return computeHourlyYieldData(deptId, workshopId);
            } finally {
                computingTasks.remove(taskKey);
            }
        }, asyncExecutor);
    }

    /**
     * 异步计算工序产量数据
     */
    @Async
    public CompletableFuture<JSONObject> asyncComputeProcessYieldData(String deptId, String workshopId) {
        String taskKey = "yieldProcess:" + deptId + ":" + workshopId;
        
        if (computingTasks.contains(taskKey)) {
            log.info("工序产量计算任务已在进行中: {}", taskKey);
            return CompletableFuture.completedFuture(null);
        }

        computingTasks.add(taskKey);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return computeProcessYieldData(deptId, workshopId);
            } finally {
                computingTasks.remove(taskKey);
            }
        }, asyncExecutor);
    }

    /**
     * 异步计算能耗数据
     */
    @Async
    public CompletableFuture<JSONObject> asyncComputeEnergyData(String deptId, String workshopId) {
        String taskKey = "energy2:" + deptId + ":" + workshopId;
        
        if (computingTasks.contains(taskKey)) {
            log.info("能耗计算任务已在进行中: {}", taskKey);
            return CompletableFuture.completedFuture(null);
        }

        computingTasks.add(taskKey);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return computeEnergyData(deptId, workshopId);
            } finally {
                computingTasks.remove(taskKey);
            }
        }, asyncExecutor);
    }

    /**
     * 计算产量数据
     */
    private JSONObject computeYieldData(String deptId, String workshopId) {
        try {
            log.info("开始异步计算产量数据: deptId={}, workshopId={}", deptId, workshopId);
            
            // 获取设备列表
            List<ZhIotEquipment> equipmentList = getEquipmentList(deptId, workshopId);
            if (equipmentList.isEmpty()) {
                return createEmptyResult();
            }

            // 创建时间段
            List<OptimizedIoTDBService.TimeSegment> timeSegments = OptimizedIoTDBService.createDailySegments(7);
            
            // 批量查询
            Map<String, Integer> results = iotdbService.aggregateQueryByTimeSegments(equipmentList, timeSegments);
            
            // 构建结果
            JSONObject ans = new JSONObject();
            List<String> dateList = timeSegments.stream().map(OptimizedIoTDBService.TimeSegment::getKey).collect(Collectors.toList());
            List<Integer> yileList = dateList.stream().map(date -> results.getOrDefault(date, 0)).collect(Collectors.toList());
            
            ans.put("dateList", dateList);
            ans.put("yileList", yileList);
            
            // 缓存结果
            String cacheKey = "yield:" + (deptId == null ? "all" : deptId) + ":" + (workshopId == null ? "all" : workshopId);
            cacheService.setPrecomputedResult("yield", deptId, workshopId, "7days", ans);
            
            log.info("产量数据计算完成: deptId={}, workshopId={}", deptId, workshopId);
            return ans;
            
        } catch (Exception e) {
            log.error("计算产量数据失败: deptId={}, workshopId={}", deptId, workshopId, e);
            return createEmptyResult();
        }
    }

    /**
     * 计算小时产量数据
     */
    private JSONObject computeHourlyYieldData(String deptId, String workshopId) {
        try {
            log.info("开始异步计算小时产量数据: deptId={}, workshopId={}", deptId, workshopId);
            
            List<ZhIotEquipment> equipmentList = getEquipmentList(deptId, workshopId);
            if (equipmentList.isEmpty()) {
                return createEmptyResult();
            }

            List<OptimizedIoTDBService.TimeSegment> timeSegments = OptimizedIoTDBService.createHourlySegments(5);
            Map<String, Integer> results = iotdbService.aggregateQueryByTimeSegments(equipmentList, timeSegments);
            
            JSONObject ans = new JSONObject();
            List<String> dateList = timeSegments.stream().map(OptimizedIoTDBService.TimeSegment::getKey).collect(Collectors.toList());
            List<Integer> yileList = dateList.stream().map(hour -> results.getOrDefault(hour, 0)).collect(Collectors.toList());
            
            ans.put("dateList", dateList);
            ans.put("yileList", yileList);
            
            cacheService.setPrecomputedResult("yieldHour", deptId, workshopId, "5hours", ans);
            
            log.info("小时产量数据计算完成: deptId={}, workshopId={}", deptId, workshopId);
            return ans;
            
        } catch (Exception e) {
            log.error("计算小时产量数据失败: deptId={}, workshopId={}", deptId, workshopId, e);
            return createEmptyResult();
        }
    }

    /**
     * 计算工序产量数据
     */
    private JSONObject computeProcessYieldData(String deptId, String workshopId) {
        try {
            log.info("开始异步计算工序产量数据: deptId={}, workshopId={}", deptId, workshopId);
            
            List<ZhIotEquipment> equipmentList = getEquipmentList(deptId, workshopId);
            if (equipmentList.isEmpty()) {
                return createEmptyResult();
            }

            DateTime end = DateUtil.date();
            DateTime begin = DateUtil.beginOfDay(end);
            String startTime = DateUtil.format(begin, "yyyy-MM-dd HH:mm:ss");
            String endTime = DateUtil.format(end, "yyyy-MM-dd HH:mm:ss");

            // 按工序分组计算
            Map<String, AtomicInteger> processCountMap = new ConcurrentHashMap<>();
            
            equipmentList.parallelStream()
                .filter(equipment -> equipment.getProcess() != null && !equipment.getEquipmentName().contains("电表"))
                .forEach(equipment -> {
                    try {
                        Integer count = iotdbService.smartQueryProductionCount(equipment.getEquipmentCode(), startTime, endTime);
                        processCountMap.computeIfAbsent(equipment.getProcess(), k -> new AtomicInteger(0))
                            .addAndGet(count);
                    } catch (Exception e) {
                        log.error("查询设备 {} 工序产量失败", equipment.getEquipmentName(), e);
                    }
                });

            // 构建结果
            JSONObject ans = new JSONObject();
            List<String> processList = new ArrayList<>();
            List<Integer> yileList = new ArrayList<>();
            
            for (Map.Entry<String, AtomicInteger> entry : processCountMap.entrySet()) {
                processList.add(entry.getKey());
                yileList.add(entry.getValue().get());
            }
            
            ans.put("processList", processList);
            ans.put("yileList", yileList);
            
            cacheService.setPrecomputedResult("yieldProcess", deptId, workshopId, "today", ans);
            
            log.info("工序产量数据计算完成: deptId={}, workshopId={}", deptId, workshopId);
            return ans;
            
        } catch (Exception e) {
            log.error("计算工序产量数据失败: deptId={}, workshopId={}", deptId, workshopId, e);
            return createEmptyResult();
        }
    }

    /**
     * 计算能耗数据
     */
    private JSONObject computeEnergyData(String deptId, String workshopId) {
        try {
            log.info("开始异步计算能耗数据: deptId={}, workshopId={}", deptId, workshopId);
            
            List<ZhIotEquipment> allEquipmentList = getEquipmentList(deptId, workshopId);
            List<ZhIotEquipment> equipmentList = allEquipmentList.stream()
                .filter(equipment -> equipment.getEquipmentName() != null && equipment.getEquipmentName().contains("电表"))
                .collect(Collectors.toList());
                
            if (equipmentList.isEmpty()) {
                return createEmptyEnergyResult();
            }

            // 这里简化处理，实际应该调用能耗查询逻辑
            JSONObject ans = new JSONObject();
            List<String> dateList = new ArrayList<>();
            List<Integer> energySum = new ArrayList<>();
            Map<String, List<Integer>> workshopEnergyMap = new HashMap<>();
            
            // 生成过去7天的数据
            for (int i = 6; i >= 0; i--) {
                DateTime dayStart = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -i));
                String format = DateUtil.format(dayStart, "MM-dd");
                dateList.add(format);
                
                // 这里应该调用实际的能耗查询逻辑
                // 暂时使用模拟数据
                energySum.add((int)(Math.random() * 1000));
            }
            
            ans.put("dateList", dateList);
            ans.put("energySum", energySum);
            ans.put("workshopEnergyMap", workshopEnergyMap);
            
            cacheService.setPrecomputedResult("energy2", deptId, workshopId, "7days", ans);
            
            log.info("能耗数据计算完成: deptId={}, workshopId={}", deptId, workshopId);
            return ans;
            
        } catch (Exception e) {
            log.error("计算能耗数据失败: deptId={}, workshopId={}", deptId, workshopId, e);
            return createEmptyEnergyResult();
        }
    }

    /**
     * 获取设备列表
     */
    private List<ZhIotEquipment> getEquipmentList(String deptId, String workshopId) {
        try {
            ZhIotEquipment zhIotEquipment = new ZhIotEquipment();
            if (deptId != null) {
                zhIotEquipment.setDeptId(Long.valueOf(deptId));
            }
            if (workshopId != null) {
                zhIotEquipment.setWorkshopId(Long.valueOf(workshopId));
            }
            
            List<ZhIotEquipment> list = zhIotEquipmentService.selectZhIotEquipmentListNoScope(zhIotEquipment);
            return list != null ? list : new ArrayList<>();
        } catch (Exception e) {
            log.error("获取设备列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 创建空结果
     */
    private JSONObject createEmptyResult() {
        JSONObject result = new JSONObject();
        result.put("dateList", new ArrayList<>());
        result.put("yileList", new ArrayList<>());
        return result;
    }

    /**
     * 创建空能耗结果
     */
    private JSONObject createEmptyEnergyResult() {
        JSONObject result = new JSONObject();
        result.put("dateList", new ArrayList<>());
        result.put("energySum", new ArrayList<>());
        result.put("workshopEnergyMap", new HashMap<>());
        return result;
    }

    /**
     * 获取正在计算的任务数量
     */
    public int getComputingTaskCount() {
        return computingTasks.size();
    }

    /**
     * 获取正在计算的任务列表
     */
    public Set<String> getComputingTasks() {
        return new HashSet<>(computingTasks);
    }
}
