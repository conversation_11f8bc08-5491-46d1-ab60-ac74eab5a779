<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhPeriodMaintainMapper">

    <resultMap type="ZhPeriodMaintain" id="ZhPeriodMaintainResult">
        <result property="id"    column="id"    />
        <result property="yearTime"    column="year_time"    />
        <result property="period"    column="period"    />
        <result property="onePeriodName"    column="one_period_name"    />
        <result property="onePeriodColor"    column="one_period_color"    />
        <result property="twoPeriodName"    column="two_period_name"    />
        <result property="twoPeriodColor"    column="two_period_color"    />
        <result property="threePeriodName"    column="three_period_name"    />
        <result property="threePeriodColor"    column="three_period_color"    />
        <result property="fourPeriodName"    column="four_period_name"    />
        <result property="fourPeriodColor"    column="four_period_color"    />
        <result property="fivePeriodName"    column="five_period_name"    />
        <result property="fivePeriodColor"    column="five_period_color"    />
        <result property="sixPeriodName"    column="six_period_name"    />
        <result property="sixPeriodColor"    column="six_period_color"    />
        <result property="sevenPeriodName"    column="seven_period_name"    />
        <result property="sevenPeriodColor"    column="seven_period_color"    />
        <result property="eightPeriodName"    column="eight_period_name"    />
        <result property="eightPeriodColor"    column="eight_period_color"    />
        <result property="ninePeriodName"    column="nine_period_name"    />
        <result property="ninePeriodColor"    column="nine_period_color"    />
        <result property="tenPeriodName"    column="ten_period_name"    />
        <result property="tenPeriodColor"    column="ten_period_color"    />
        <result property="elevenPeriodName"    column="eleven_period_name"    />
        <result property="elevenPeriodColor"    column="eleven_period_color"    />
        <result property="twelvePeriodName"    column="twelve_period_name"    />
        <result property="twelvePeriodColor"    column="twelve_period_color"    />
        <result property="deptId"    column="dept_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectZhPeriodMaintainVo">
        select id, year_time, period, one_period_name, one_period_color, two_period_name, two_period_color, three_period_name, three_period_color, four_period_name, four_period_color, five_period_name, five_period_color, six_period_name, six_period_color, seven_period_name, seven_period_color, eight_period_name, eight_period_color, nine_period_name, nine_period_color, ten_period_name, ten_period_color, eleven_period_name, eleven_period_color, twelve_period_name, twelve_period_color, dept_id, create_by, create_time, update_by, update_time from zh_period_maintain
    </sql>

    <select id="selectZhPeriodMaintainList" parameterType="ZhPeriodMaintain" resultMap="ZhPeriodMaintainResult">
        select t1.id, t1.year_time, t1.period, t1.one_period_name,
        (select period_color from zh_period t2 where t2.period_name = t1.one_period_name) as one_period_color,
        two_period_name,(select period_color from zh_period t2 where t2.period_name = t1.two_period_name) as two_period_color,
        three_period_name, (select period_color from zh_period t2 where t2.period_name = t1.three_period_name) as three_period_color,
        four_period_name,(select period_color from zh_period t2 where t2.period_name = t1.four_period_name) as four_period_color,
        five_period_name,(select period_color from zh_period t2 where t2.period_name = t1.five_period_name) as five_period_color,
        six_period_name, (select period_color from zh_period t2 where t2.period_name = t1.six_period_name) as six_period_color,
        seven_period_name,(select period_color from zh_period t2 where t2.period_name = t1.seven_period_name) as seven_period_color,
        eight_period_name,(select period_color from zh_period t2 where t2.period_name = t1.eight_period_name) as eight_period_color,
        nine_period_name,(select period_color from zh_period t2 where t2.period_name = t1.nine_period_name) as nine_period_color,
        ten_period_name,(select period_color from zh_period t2 where t2.period_name = t1.ten_period_name) as ten_period_color,
        eleven_period_name, (select period_color from zh_period t2 where t2.period_name = t1.eleven_period_name) as eleven_period_color,
        twelve_period_name,(select period_color from zh_period t2 where t2.period_name = t1.twelve_period_name) as twelve_period_color,
        dept_id, create_by, create_time, update_by, update_time from zh_period_maintain t1
        <where>
            <if test="yearTime != null  and yearTime != ''"> and t1.year_time = #{yearTime}</if>
            <if test="period != null "> and t1.period = #{period}</if>
            <if test="onePeriodName != null  and onePeriodName != ''"> and one_period_name like concat('%', #{onePeriodName}, '%')</if>
            <if test="onePeriodColor != null  and onePeriodColor != ''"> and one_period_color = #{onePeriodColor}</if>
            <if test="twoPeriodName != null  and twoPeriodName != ''"> and two_period_name like concat('%', #{twoPeriodName}, '%')</if>
            <if test="twoPeriodColor != null  and twoPeriodColor != ''"> and two_period_color = #{twoPeriodColor}</if>
            <if test="threePeriodName != null  and threePeriodName != ''"> and three_period_name like concat('%', #{threePeriodName}, '%')</if>
            <if test="threePeriodColor != null  and threePeriodColor != ''"> and three_period_color = #{threePeriodColor}</if>
            <if test="fourPeriodName != null  and fourPeriodName != ''"> and four_period_name like concat('%', #{fourPeriodName}, '%')</if>
            <if test="fourPeriodColor != null  and fourPeriodColor != ''"> and four_period_color = #{fourPeriodColor}</if>
            <if test="fivePeriodName != null  and fivePeriodName != ''"> and five_period_name like concat('%', #{fivePeriodName}, '%')</if>
            <if test="fivePeriodColor != null  and fivePeriodColor != ''"> and five_period_color = #{fivePeriodColor}</if>
            <if test="sixPeriodName != null  and sixPeriodName != ''"> and six_period_name like concat('%', #{sixPeriodName}, '%')</if>
            <if test="sixPeriodColor != null  and sixPeriodColor != ''"> and six_period_color = #{sixPeriodColor}</if>
            <if test="sevenPeriodName != null  and sevenPeriodName != ''"> and seven_period_name like concat('%', #{sevenPeriodName}, '%')</if>
            <if test="sevenPeriodColor != null  and sevenPeriodColor != ''"> and seven_period_color = #{sevenPeriodColor}</if>
            <if test="eightPeriodName != null  and eightPeriodName != ''"> and eight_period_name like concat('%', #{eightPeriodName}, '%')</if>
            <if test="eightPeriodColor != null  and eightPeriodColor != ''"> and eight_period_color = #{eightPeriodColor}</if>
            <if test="ninePeriodName != null  and ninePeriodName != ''"> and nine_period_name like concat('%', #{ninePeriodName}, '%')</if>
            <if test="ninePeriodColor != null  and ninePeriodColor != ''"> and nine_period_color = #{ninePeriodColor}</if>
            <if test="tenPeriodName != null  and tenPeriodName != ''"> and ten_period_name like concat('%', #{tenPeriodName}, '%')</if>
            <if test="tenPeriodColor != null  and tenPeriodColor != ''"> and ten_period_color = #{tenPeriodColor}</if>
            <if test="elevenPeriodName != null  and elevenPeriodName != ''"> and eleven_period_name like concat('%', #{elevenPeriodName}, '%')</if>
            <if test="elevenPeriodColor != null  and elevenPeriodColor != ''"> and eleven_period_color = #{elevenPeriodColor}</if>
            <if test="twelvePeriodName != null  and twelvePeriodName != ''"> and twelve_period_name like concat('%', #{twelvePeriodName}, '%')</if>
            <if test="twelvePeriodColor != null  and twelvePeriodColor != ''"> and twelve_period_color = #{twelvePeriodColor}</if>
            <if test="deptId != null "> and t1.dept_id = #{deptId}</if>
        </where>
        order by t1.period
    </select>

    <select id="selectZhPeriodMaintainById" parameterType="Long" resultMap="ZhPeriodMaintainResult">
        <include refid="selectZhPeriodMaintainVo"/>
        where id = #{id}
    </select>

    <insert id="insertZhPeriodMaintain" parameterType="ZhPeriodMaintain" useGeneratedKeys="true" keyProperty="id">
        insert into zh_period_maintain
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="yearTime != null and yearTime != ''">year_time,</if>
            <if test="period != null">period,</if>
            <if test="onePeriodName != null">one_period_name,</if>
            <if test="onePeriodColor != null">one_period_color,</if>
            <if test="twoPeriodName != null">two_period_name,</if>
            <if test="twoPeriodColor != null">two_period_color,</if>
            <if test="threePeriodName != null">three_period_name,</if>
            <if test="threePeriodColor != null">three_period_color,</if>
            <if test="fourPeriodName != null">four_period_name,</if>
            <if test="fourPeriodColor != null">four_period_color,</if>
            <if test="fivePeriodName != null">five_period_name,</if>
            <if test="fivePeriodColor != null">five_period_color,</if>
            <if test="sixPeriodName != null">six_period_name,</if>
            <if test="sixPeriodColor != null">six_period_color,</if>
            <if test="sevenPeriodName != null">seven_period_name,</if>
            <if test="sevenPeriodColor != null">seven_period_color,</if>
            <if test="eightPeriodName != null">eight_period_name,</if>
            <if test="eightPeriodColor != null">eight_period_color,</if>
            <if test="ninePeriodName != null">nine_period_name,</if>
            <if test="ninePeriodColor != null">nine_period_color,</if>
            <if test="tenPeriodName != null">ten_period_name,</if>
            <if test="tenPeriodColor != null">ten_period_color,</if>
            <if test="elevenPeriodName != null">eleven_period_name,</if>
            <if test="elevenPeriodColor != null">eleven_period_color,</if>
            <if test="twelvePeriodName != null">twelve_period_name,</if>
            <if test="twelvePeriodColor != null">twelve_period_color,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="yearTime != null and yearTime != ''">#{yearTime},</if>
            <if test="period != null">#{period},</if>
            <if test="onePeriodName != null">#{onePeriodName},</if>
            <if test="onePeriodColor != null">#{onePeriodColor},</if>
            <if test="twoPeriodName != null">#{twoPeriodName},</if>
            <if test="twoPeriodColor != null">#{twoPeriodColor},</if>
            <if test="threePeriodName != null">#{threePeriodName},</if>
            <if test="threePeriodColor != null">#{threePeriodColor},</if>
            <if test="fourPeriodName != null">#{fourPeriodName},</if>
            <if test="fourPeriodColor != null">#{fourPeriodColor},</if>
            <if test="fivePeriodName != null">#{fivePeriodName},</if>
            <if test="fivePeriodColor != null">#{fivePeriodColor},</if>
            <if test="sixPeriodName != null">#{sixPeriodName},</if>
            <if test="sixPeriodColor != null">#{sixPeriodColor},</if>
            <if test="sevenPeriodName != null">#{sevenPeriodName},</if>
            <if test="sevenPeriodColor != null">#{sevenPeriodColor},</if>
            <if test="eightPeriodName != null">#{eightPeriodName},</if>
            <if test="eightPeriodColor != null">#{eightPeriodColor},</if>
            <if test="ninePeriodName != null">#{ninePeriodName},</if>
            <if test="ninePeriodColor != null">#{ninePeriodColor},</if>
            <if test="tenPeriodName != null">#{tenPeriodName},</if>
            <if test="tenPeriodColor != null">#{tenPeriodColor},</if>
            <if test="elevenPeriodName != null">#{elevenPeriodName},</if>
            <if test="elevenPeriodColor != null">#{elevenPeriodColor},</if>
            <if test="twelvePeriodName != null">#{twelvePeriodName},</if>
            <if test="twelvePeriodColor != null">#{twelvePeriodColor},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateZhPeriodMaintain" parameterType="ZhPeriodMaintain">
        update zh_period_maintain
        <trim prefix="SET" suffixOverrides=",">
            <if test="yearTime != null and yearTime != ''">year_time = #{yearTime},</if>
            <if test="period != null">period = #{period},</if>
            <if test="onePeriodName != null">one_period_name = #{onePeriodName},</if>
            <if test="onePeriodColor != null">one_period_color = #{onePeriodColor},</if>
            <if test="twoPeriodName != null">two_period_name = #{twoPeriodName},</if>
            <if test="twoPeriodColor != null">two_period_color = #{twoPeriodColor},</if>
            <if test="threePeriodName != null">three_period_name = #{threePeriodName},</if>
            <if test="threePeriodColor != null">three_period_color = #{threePeriodColor},</if>
            <if test="fourPeriodName != null">four_period_name = #{fourPeriodName},</if>
            <if test="fourPeriodColor != null">four_period_color = #{fourPeriodColor},</if>
            <if test="fivePeriodName != null">five_period_name = #{fivePeriodName},</if>
            <if test="fivePeriodColor != null">five_period_color = #{fivePeriodColor},</if>
            <if test="sixPeriodName != null">six_period_name = #{sixPeriodName},</if>
            <if test="sixPeriodColor != null">six_period_color = #{sixPeriodColor},</if>
            <if test="sevenPeriodName != null">seven_period_name = #{sevenPeriodName},</if>
            <if test="sevenPeriodColor != null">seven_period_color = #{sevenPeriodColor},</if>
            <if test="eightPeriodName != null">eight_period_name = #{eightPeriodName},</if>
            <if test="eightPeriodColor != null">eight_period_color = #{eightPeriodColor},</if>
            <if test="ninePeriodName != null">nine_period_name = #{ninePeriodName},</if>
            <if test="ninePeriodColor != null">nine_period_color = #{ninePeriodColor},</if>
            <if test="tenPeriodName != null">ten_period_name = #{tenPeriodName},</if>
            <if test="tenPeriodColor != null">ten_period_color = #{tenPeriodColor},</if>
            <if test="elevenPeriodName != null">eleven_period_name = #{elevenPeriodName},</if>
            <if test="elevenPeriodColor != null">eleven_period_color = #{elevenPeriodColor},</if>
            <if test="twelvePeriodName != null">twelve_period_name = #{twelvePeriodName},</if>
            <if test="twelvePeriodColor != null">twelve_period_color = #{twelvePeriodColor},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhPeriodMaintainById" parameterType="Long">
        delete from zh_period_maintain where id = #{id}
    </delete>

    <delete id="deleteZhPeriodMaintainByYearTime" parameterType="string">
        delete from zh_period_maintain where year_time = #{yearTime}
    </delete>

    <delete id="deleteZhPeriodMaintainByIds" parameterType="String">
        delete from zh_period_maintain where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>