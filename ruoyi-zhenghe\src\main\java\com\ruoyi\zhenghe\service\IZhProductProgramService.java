package com.ruoyi.zhenghe.service;

import java.util.List;
import com.ruoyi.zhenghe.domain.ZhProductProgram;

/**
 * 产品程序号Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface IZhProductProgramService 
{
    /**
     * 查询产品程序号
     * 
     * @param id 产品程序号主键
     * @return 产品程序号
     */
    public ZhProductProgram selectZhProductProgramById(Long id);

    /**
     * 查询产品程序号列表
     * 
     * @param zhProductProgram 产品程序号
     * @return 产品程序号集合
     */
    public List<ZhProductProgram> selectZhProductProgramList(ZhProductProgram zhProductProgram);

    /**
     * 新增产品程序号
     * 
     * @param zhProductProgram 产品程序号
     * @return 结果
     */
    public int insertZhProductProgram(ZhProductProgram zhProductProgram);

    /**
     * 修改产品程序号
     * 
     * @param zhProductProgram 产品程序号
     * @return 结果
     */
    public int updateZhProductProgram(ZhProductProgram zhProductProgram);

    /**
     * 批量删除产品程序号
     * 
     * @param ids 需要删除的产品程序号主键集合
     * @return 结果
     */
    public int deleteZhProductProgramByIds(Long[] ids);

    /**
     * 删除产品程序号信息
     * 
     * @param id 产品程序号主键
     * @return 结果
     */
    public int deleteZhProductProgramById(Long id);
}
