package com.ruoyi.zhenghe.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 检测记录汇总数据传输对象（按日期分组）
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Data
public class DetectionSummaryDto {

    /** 编号（用于显示） */
    @ApiModelProperty(value = "编号")
    private String serialNumber;

    @ApiModelProperty(value = "id")
    private String productId;
    /** 产品信息 */
    @ApiModelProperty(value = "产品型号")
    private String productModel;

    @ApiModelProperty(value = "片数")
    private Integer pieceCount;

    @ApiModelProperty(value = "节数")
    private Integer sectionCount;

    @ApiModelProperty(value = "正时标记")
    private String timingMark;

    /** 检测统计（当天累加） */
    @ApiModelProperty(value = "总数")
    private Integer totalCount;

    @ApiModelProperty(value = "正常数量")
    private Integer qualifiedCount;

    @ApiModelProperty(value = "瑕疵总数")
    private Integer defectCount;

    /** 完成时间（日期） */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "完成时间")
    private Date detectionDate;

    /** 操作 */
    @ApiModelProperty(value = "操作链接")
    private String actionLink;

    private String customerName;

    private String machineType;

    private String printMark;

    private String oe;
    private String chainLength;
    private String chainLengthTolerance;
}
