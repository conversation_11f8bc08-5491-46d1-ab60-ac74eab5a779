package com.ruoyi.zhenghe.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 生产汇总数据DTO
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@ApiModel(value = "ProductionSummaryDto", description = "生产汇总数据DTO")
public class ProductionSummaryDto {

    /** 设备编码 */
    @ApiModelProperty(value = "设备编码")
    private String deviceCode;

    /** 产品名称 */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /** 网关上报属性编码 */
    @ApiModelProperty(value = "网关上报属性编码")
    private String gatewayAttr;

    /** 生产总数量 */
    @ApiModelProperty(value = "生产总数量")
    private Integer totalProductionCount;

    /** 生产总时长(分钟) */
    @ApiModelProperty(value = "生产总时长(分钟)")
    private Integer totalProductionDuration;

    /** 生产批次数 */
    @ApiModelProperty(value = "生产批次数")
    private Integer batchCount;

    /** 平均每批次产量 */
    @ApiModelProperty(value = "平均每批次产量")
    private Double avgBatchProduction;

    /** 平均每分钟产量 */
    @ApiModelProperty(value = "平均每分钟产量")
    private Double avgMinuteProduction;

}
