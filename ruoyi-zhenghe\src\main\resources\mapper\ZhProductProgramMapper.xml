<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhenghe.mapper.ZhProductProgramMapper">

    <resultMap type="ZhProductProgram" id="ZhProductProgramResult">
        <result property="id"    column="id"    />
        <result property="produceName"    column="produce_name"    />
        <result property="programNumber"    column="program_number"    />
        <result property="rate"    column="rate"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectZhProductProgramVo">
        SELECT
            t1.id,
            t1.produce_name,
            t1.program_number,
            t1.rate,
            t1.dept_id,
            t1.create_by,
            t1.create_time,
            t1.update_by,
            t1.update_time,
            d.dept_name
        FROM
            zh_product_program t1
                LEFT JOIN sys_dept d ON t1.dept_id = d.dept_id
    </sql>

    <select id="selectZhProductProgramList" parameterType="ZhProductProgram" resultMap="ZhProductProgramResult">
        <include refid="selectZhProductProgramVo"/>
        <where>
            1 = 1
            <if test="produceName != null  and produceName != ''"> and t1.produce_name like concat('%', #{produceName}, '%')</if>
            <if test="programNumber != null  and programNumber != ''"> and t1.program_number like concat('%', #{programNumber}, '%')</if>
            <if test="rate != null "> and t1.rate = #{rate}</if>
            <if test="deptId != null "> and t1.dept_id = #{deptId}</if>
        </where>
        ${params.dataScope}
        order by
        t1.create_time desc
    </select>

    <select id="selectZhProductProgramById" parameterType="Long" resultMap="ZhProductProgramResult">
        <include refid="selectZhProductProgramVo"/>
        where t1.id = #{id}
    </select>

    <insert id="insertZhProductProgram" parameterType="ZhProductProgram" useGeneratedKeys="true" keyProperty="id">
        insert into zh_product_program
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="produceName != null">produce_name,</if>
            <if test="programNumber != null">program_number,</if>
            <if test="rate != null">rate,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="produceName != null">#{produceName},</if>
            <if test="programNumber != null">#{programNumber},</if>
            <if test="rate != null">#{rate},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateZhProductProgram" parameterType="ZhProductProgram">
        update zh_product_program
        <trim prefix="SET" suffixOverrides=",">
            <if test="produceName != null">produce_name = #{produceName},</if>
            <if test="programNumber != null">program_number = #{programNumber},</if>
            <if test="rate != null">rate = #{rate},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhProductProgramById" parameterType="Long">
        delete from zh_product_program where id = #{id}
    </delete>

    <delete id="deleteZhProductProgramByIds" parameterType="String">
        delete from zh_product_program where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>