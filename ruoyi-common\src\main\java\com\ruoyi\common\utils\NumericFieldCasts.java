package com.ruoyi.common.utils;

/**
 * <AUTHOR>
 * @date 2025/3/19 11:58
 */
public class NumericFieldCasts {

    public static String NumericFieldString(String time){
        if (time.equals("1")){
            return "one_period_name";
        }else if (time.equals("2")){
            return "two_period_name";
        }else if (time.equals("3")){
            return "three_period_name";
        }else if (time.equals("4")){
            return "four_period_name";
        }else if (time.equals("5")){
            return "five_period_name";
        }else if (time.equals("6")){
            return "six_period_name";
        }else if (time.equals("7")){
            return "seven_period_name";
        }else if (time.equals("8")){
            return "eight_period_name";
        }else if (time.equals("9")){
            return "nine_period_name";
        }else if (time.equals("10")){
            return "ten_period_name";
        }else if (time.equals("11")){
            return "eleven_period_name";
        }else if (time.equals("12")){
            return "twelve_period_name";
        }
        return "null";
    }

}
