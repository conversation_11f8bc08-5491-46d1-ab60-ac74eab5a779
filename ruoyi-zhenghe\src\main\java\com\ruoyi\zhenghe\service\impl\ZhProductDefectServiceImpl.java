package com.ruoyi.zhenghe.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.zhenghe.domain.ZhProductDefect;
import com.ruoyi.zhenghe.mapper.ZhProductDefectMapper;
import com.ruoyi.zhenghe.service.IZhProductDefectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品瑕疵Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Service
public class ZhProductDefectServiceImpl implements IZhProductDefectService {
    @Autowired
    private ZhProductDefectMapper zhProductDefectMapper;

    /**
     * 查询产品瑕疵
     *
     * @param id 产品瑕疵主键
     * @return 产品瑕疵
     */
    @Override
    public ZhProductDefect selectZhProductDefectById(Long id) {
        return zhProductDefectMapper.selectZhProductDefectById(id);
    }

    /**
     * 查询产品瑕疵列表
     *
     * @param zhProductDefect 产品瑕疵
     * @return 产品瑕疵
     */
    @Override
    public List<ZhProductDefect> selectZhProductDefectList(ZhProductDefect zhProductDefect) {
        return zhProductDefectMapper.selectZhProductDefectList(zhProductDefect);
    }

    /**
     * 根据产品ID查询瑕疵列表
     *
     * @param productId 产品ID
     * @return 产品瑕疵集合
     */
    @Override
    public List<ZhProductDefect> selectZhProductDefectListByProductId(Long productId) {
        return zhProductDefectMapper.selectZhProductDefectListByProductId(productId);
    }

    @Override
    public List<ZhProductDefect> selectZhProductDefectListByProductIdOpen(Long productId) {
        return zhProductDefectMapper.selectZhProductDefectListByProductIdOpen(productId);
    }

    /**
     * 新增产品瑕疵
     *
     * @param zhProductDefect 产品瑕疵
     * @return 结果
     */
    @Override
    public int insertZhProductDefect(ZhProductDefect zhProductDefect) {
        zhProductDefect.setCreateTime(DateUtils.getNowDate());
        zhProductDefect.setCreateBy(SecurityUtils.getUsername());
        
        // 如果没有设置排序号，自动获取下一个排序号
        if (zhProductDefect.getSortOrder() == null) {
            Integer nextSortOrder = zhProductDefectMapper.getNextSortOrder(zhProductDefect.getProductId());
            zhProductDefect.setSortOrder(nextSortOrder);
        }
        
        // 如果没有设置状态，默认启用
        if (zhProductDefect.getStatus() == null) {
            zhProductDefect.setStatus(1);
        }
        
        return zhProductDefectMapper.insertZhProductDefect(zhProductDefect);
    }

    /**
     * 修改产品瑕疵
     *
     * @param zhProductDefect 产品瑕疵
     * @return 结果
     */
    @Override
    public int updateZhProductDefect(ZhProductDefect zhProductDefect) {
        zhProductDefect.setUpdateTime(DateUtils.getNowDate());
        zhProductDefect.setUpdateBy(SecurityUtils.getUsername());
        return zhProductDefectMapper.updateZhProductDefect(zhProductDefect);
    }

    /**
     * 批量删除产品瑕疵
     *
     * @param ids 需要删除的产品瑕疵主键
     * @return 结果
     */
    @Override
    public int deleteZhProductDefectByIds(Long[] ids) {
        return zhProductDefectMapper.deleteZhProductDefectByIds(ids);
    }

    /**
     * 删除产品瑕疵信息
     *
     * @param id 产品瑕疵主键
     * @return 结果
     */
    @Override
    public int deleteZhProductDefectById(Long id) {
        return zhProductDefectMapper.deleteZhProductDefectById(id);
    }

    /**
     * 根据产品ID删除瑕疵
     *
     * @param productId 产品ID
     * @return 结果
     */
    @Override
    public int deleteZhProductDefectByProductId(Long productId) {
        return zhProductDefectMapper.deleteZhProductDefectByProductId(productId);
    }

    /**
     * 批量更新瑕疵状态
     *
     * @param ids 瑕疵ID数组
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateZhProductDefectStatus(Long[] ids, Integer status) {
        return zhProductDefectMapper.updateZhProductDefectStatus(ids, status);
    }

    /**
     * 启用瑕疵
     *
     * @param ids 瑕疵ID数组
     * @return 结果
     */
    @Override
    public int enableZhProductDefect(Long[] ids) {
        return updateZhProductDefectStatus(ids, 1);
    }

    /**
     * 禁用瑕疵
     *
     * @param ids 瑕疵ID数组
     * @return 结果
     */
    @Override
    public int disableZhProductDefect(Long[] ids) {
        return updateZhProductDefectStatus(ids, 0);
    }

    /**
     * 获取产品下一个排序号
     *
     * @param productId 产品ID
     * @return 下一个排序号
     */
    @Override
    public Integer getNextSortOrder(Long productId) {
        return zhProductDefectMapper.getNextSortOrder(productId);
    }
}
